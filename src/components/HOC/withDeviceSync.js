import React from 'react';
import {Platform} from 'react-native';
import {useMutation} from '@apollo/react-hooks';
import DeviceInfo from 'react-native-device-info';
import AsyncStorage from '@react-native-async-storage/async-storage';
import IP from 'react-native-public-ip';

import {SYNC_DEVICE} from '../../apollo/mutations';
import {persistent} from '../../assets/strings';
import {useAppStateSubscription} from '../../tools/hooks';

export default function withDeviceSync(WrappedComponent = null) {
  return (...props) => {
    const [syncDevice] = useMutation(SYNC_DEVICE);

    const syncDeviceCB = React.useCallback(async () => {
      let ipAddress;
      try {
        ipAddress = await IP();
      } catch (_) {}
      try {
        const deviceId = DeviceInfo.getDeviceId();
        const fcmToken = await AsyncStorage.getItem(persistent.FCM_TOKEN);
        //console.log("FCM Token-================", fcmToken);
        if (fcmToken?.length) {
          await syncDevice({
            variables: {
              deviceId,
              platform: Platform.OS.toUpperCase(),
              fcmToken,
              IPAddress: ipAddress,
              ...(ipAddress ? {IPAddress: ipAddress} : {}),
              deviceName: (await DeviceInfo.getDeviceName()) ?? '',
            },
          });
        }
      } catch (error) {
        if (__DEV__) {
          console.error('Device Sync Error', error);
        }
      }
    }, [syncDevice]);

    useAppStateSubscription({callback: syncDeviceCB});

    React.useEffect(() => {
      let intervalId;

      intervalId = setInterval(() => {
        syncDeviceCB();
      }, 30000);

      return () => {
        clearInterval(intervalId);
      };
    }, [syncDeviceCB]);

    return <WrappedComponent {...props} />;
  };
}
