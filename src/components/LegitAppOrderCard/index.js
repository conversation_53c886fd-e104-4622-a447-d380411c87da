import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import dayjs from 'dayjs';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import FastImage from '../FastImage';

const LegitAppOrderCard = ({item}) => {
  const getStatusColor = status => {
    switch (status) {
      case 1:
        return colors.amber; // Pending
      case 2:
        return colors.green; // Completed
      case 3:
        return colors.warning; // Failed
      default:
        return colors.darkGrey;
    }
  };

  const getStatusText = status => {
    switch (status) {
      case 1:
        return 'Pending';
      case 2:
        return 'Completed';
      case 3:
        return 'Failed';
      default:
        return 'Unknown';
    }
  };

  const parseResult = result => {
    if (!result) return null;
    try {
      return JSON.parse(result);
    } catch (e) {
      return null;
    }
  };

  const resultData = parseResult(item?.result);

  return (
    <View style={styles.cardContainer}>
      <View style={styles.header}>
        <View style={styles.orderInfo}>
          <Text style={styles.orderId}>Order #{item?.authentication_id}</Text>
          <Text style={styles.date}>
            {dayjs(item?.created_at).format('MMM DD, YYYY')}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            {backgroundColor: getStatusColor(item?.status)},
          ]}>
          <Text style={styles.statusText}>{getStatusText(item?.status)}</Text>
        </View>
      </View>

      {item?.images && item?.images?.length > 0 && (
        <View style={styles.imagesContainer}>
          <Text style={styles.imagesLabel}>
            Images ({item?.images?.length})
          </Text>
          <View style={styles.imagesList}>
            {item?.images?.slice(0, 3)?.map((image, index) => (
              <FastImage
                key={image?.id || index}
                source={{uri: image?.legit_app_url || image?.s3_url}}
                style={styles.thumbnailImage}
                resizeMode="cover"
              />
            ))}
            {item?.images?.length > 3 && (
              <View style={styles.moreImagesOverlay}>
                <Text style={styles.moreImagesText}>
                  +{item?.images?.length - 3}
                </Text>
              </View>
            )}
          </View>
        </View>
      )}

      {resultData && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultLabel}>Result:</Text>
          <Text style={styles.resultText}>
            {resultData?.result || 'Authentication completed'}
          </Text>
        </View>
      )}

      {!item?.images?.length && !resultData && (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>
            No additional details available
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginHorizontal: spacings.lg,
    marginVertical: spacings.sm,
    padding: spacings.lg,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacings.md,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: colors.darkFont,
    marginBottom: spacings.xs,
  },
  date: {
    fontSize: fontSize.sm,
    color: colors.darkGrey,
  },
  statusBadge: {
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.xs,
    borderRadius: 16,
  },
  statusText: {
    fontSize: fontSize.sm,
    fontWeight: '500',
    color: colors.white,
  },
  imagesContainer: {
    marginBottom: spacings.md,
  },
  imagesLabel: {
    fontSize: fontSize.md,
    fontWeight: '500',
    color: colors.darkFont,
    marginBottom: spacings.sm,
  },
  imagesList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thumbnailImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: spacings.sm,
  },
  moreImagesOverlay: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: colors.darkBadge,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    fontSize: fontSize.sm,
    fontWeight: '500',
    color: colors.white,
  },
  resultContainer: {
    backgroundColor: colors.lightGrey,
    padding: spacings.md,
    borderRadius: 8,
    marginBottom: spacings.sm,
  },
  resultLabel: {
    fontSize: fontSize.sm,
    fontWeight: '500',
    color: colors.darkFont,
    marginBottom: spacings.xs,
  },
  resultText: {
    fontSize: fontSize.sm,
    color: colors.darkGrey,
  },
  emptyState: {
    padding: spacings.md,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: fontSize.sm,
    color: colors.darkGrey,
    fontStyle: 'italic',
  },
});

export default LegitAppOrderCard;
