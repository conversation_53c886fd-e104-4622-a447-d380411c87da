import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import dayjs from 'dayjs';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import FastImage from '../FastImage';
import Icon from '../Icon';
import {icons} from '../../assets/strings';

const LegitAppOrderCard = ({item, onPress}) => {
  const getStatusText = status => {
    switch (status) {
      case 1:
        return 'PENDING';
      case 2:
        return 'COMPLETED';
      case 3:
        return 'INCONCLUSIVE';
      default:
        return 'INCONCLUSIVE';
    }
  };

  const parseResult = result => {
    if (!result) return null;
    try {
      return JSON.parse(result);
    } catch (e) {
      return null;
    }
  };

  const resultData = parseResult(item?.result);
  const isInconclusive =
    item?.status === 3 || getStatusText(item?.status) === 'INCONCLUSIVE';

  // Parse request_data to get brand and product info
  let requestData = null;
  try {
    requestData = item?.request_data ? JSON.parse(item?.request_data) : null;
  } catch (e) {
    requestData = null;
  }
  const brandName = requestData?.brand || 'Bvlgari';
  const productType = requestData?.category || 'Bracelet Code Checking';

  return (
    <TouchableOpacity
      style={styles.cardContainer}
      onPress={() => onPress?.(item)}>
      <View style={styles.cardContent}>
        {/* Left side - Image */}
        <View style={styles.imageContainer}>
          {item?.images && item?.images?.length > 0 ? (
            <FastImage
              source={{
                uri: item?.images[0]?.legit_app_url || item?.images[0]?.s3_url,
              }}
              style={styles.productImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Icon
                name={icons.UPLOAD_PHOTO}
                size={24}
                tint={colors.darkGrey}
              />
            </View>
          )}
        </View>

        {/* Right side - Content */}
        <View style={styles.contentContainer}>
          <View style={[styles.headerRow]}>
            <Text style={styles.orderNumber}>#{item?.authentication_id}</Text>
            <View style={styles.statusContainer}>
              <View
                style={[
                  styles.statusBadge,
                  isInconclusive && styles.inconclusiveBadge,
                ]}>
                <Text style={styles.statusText}>
                  {getStatusText(item?.status)}
                </Text>
              </View>
              {isInconclusive && (
                <View style={styles.alertIcon}>
                  <Icon
                    name={icons.QUESTIONMARK_CIRCLE}
                    size={16}
                    tint={colors.warning}
                  />
                </View>
              )}
            </View>
          </View>

          <Text style={styles.brandName}>{brandName}</Text>
          <Text style={styles.productType}>{productType}</Text>

          <View style={styles.dateContainer}>
            <Text style={styles.dateLabel}>
              Created on {dayjs(item?.created_at).format('DD MMM YYYY, h:mm A')}
            </Text>
            {resultData && (
              <Text style={styles.dateLabel}>
                Completed on{' '}
                {dayjs(item?.created_at)
                  .add(1, 'day')
                  .format('DD MMM YYYY, h:mm A')}
              </Text>
            )}
          </View>

          {/* Custom Code Badge if available */}
          {requestData?.customCode && (
            <View style={styles.customCodeBadge}>
              <Text style={styles.customCodeText}>
                Custom Code {requestData.customCode}
              </Text>
            </View>
          )}
        </View>

        {/* Time indicator */}
        <View style={styles.timeIndicator}>
          <Text style={styles.timeText}>{'< 12 Hours'}</Text>
        </View>

        {/* Right arrow */}
        {/* <View style={styles.arrowContainer}>
          <Icon name={icons.ARROW_RIGHT} size={20} tint={colors.darkGrey} />
        </View> */}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.white,
    // marginHorizontal: spacings.lg,
    // marginVertical: spacings.sm,
    // borderRadius: 12,
    // borderWidth: 1,
    // borderColor: colors.amber,
    overflow: 'hidden',
  },
  cardContent: {
    flexDirection: 'row',
    padding: spacings.md,
    alignItems: 'center',
  },
  imageContainer: {
    marginRight: spacings.md,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: colors.darkGrey,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacings.xs,
  },
  orderNumber: {
    fontSize: fontSize.sm,
    color: colors.darkGrey,
    fontWeight: '400',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusBadge: {
    backgroundColor: colors.darkGrey,
    paddingHorizontal: spacings.sm,
    paddingVertical: 2,
    borderRadius: 4,
  },
  inconclusiveBadge: {
    backgroundColor: colors.darkGrey,
  },
  statusText: {
    fontSize: fontSize.xs,
    color: colors.white,
    fontWeight: '600',
  },
  alertIcon: {
    marginLeft: spacings.xs,
  },
  brandName: {
    fontSize: fontSize.lg,
    color: colors.black,
    fontWeight: '600',
    marginBottom: spacings.xs,
  },
  productType: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: '500',
    marginBottom: spacings.sm,
  },
  dateContainer: {
    marginBottom: spacings.xs,
  },
  dateLabel: {
    fontSize: fontSize.xs,
    color: colors.darkGrey,
    marginBottom: 2,
  },
  customCodeBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacings.sm,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: spacings.xs,
  },
  customCodeText: {
    fontSize: fontSize.xs,
    color: colors.white,
    fontWeight: '600',
  },
  arrowContainer: {
    marginLeft: spacings.sm,
  },
  timeIndicator: {
    position: 'absolute',
    top: spacings.xl,
    right: spacings.md,
  },
  timeText: {
    fontSize: fontSize.xs,
    color: colors.black,
    fontWeight: '500',
  },
});

export default LegitAppOrderCard;
