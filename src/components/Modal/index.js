import React from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withDelay,
  withTiming,
} from 'react-native-reanimated';

import styles from './styles';

const INITIAL_ROTATE_Z = 5;
const INITIAL_SCALE = 0.7;
const SPRING_CONFIG = { damping: 15, stiffness: 200 };

export default function CommonModal({
  children = null,
  visible = false,
  setVisible = () => null,
  width = 320,
  blurBackground = false,
  style = {},
  delayInMs = 0,
  containerStyle = {},
}) {
  const rotateZ = useSharedValue(INITIAL_ROTATE_Z);
  const scale = useSharedValue(INITIAL_SCALE);
  const opacity = useSharedValue(0);
  const transformTranslate = useAnimatedStyle(() => ({
    transform: [
      {
        rotateZ: `${rotateZ.value} deg`,
      },
      {
        scale: scale.value,
      },
    ],
    ...(delayInMs ? { opacity: opacity.value } : {}),
  }));

  const overlayOpacity = useSharedValue(0);
  const overlayStyle = useAnimatedStyle(() => ({
    opacity: overlayOpacity.value,
  }));

  const onVisible = React.useCallback(() => {
    opacity.value = 1;
    rotateZ.value = withDelay(50, withSpring(0, SPRING_CONFIG));
    scale.value = withDelay(50, withSpring(1, SPRING_CONFIG));
    overlayOpacity.value = withSpring(0.5);
  }, [opacity.value, rotateZ.value, scale.value, overlayOpacity.value]);

  const onHide = React.useCallback(() => {
    rotateZ.value = withSpring(INITIAL_ROTATE_Z, SPRING_CONFIG);
    scale.value = withSpring(INITIAL_SCALE, SPRING_CONFIG);
    overlayOpacity.value = withSpring(0);
    opacity.value = withTiming(0, { duration: 200 });
  }, [opacity.value, rotateZ.value, scale.value, overlayOpacity.value]);

  React.useEffect(() => {
    let timeoutId;
    if (visible) {
      if (delayInMs === 0) {
        onVisible();
      } else {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(onVisible, delayInMs);
      }
    } else {
      onHide();
    }
  }, [onVisible, visible, delayInMs, onHide]);

  return (
    <Modal
      transparent
      visible={visible}
      onRequestClose={() => {
        setVisible(false);
      }}
      animationType="fade">
      <StatusBar backgroundColor="black" barStyle="light-content" />
      <TouchableOpacity
        activeOpacity={1}
        style={{ flex: 1 }}
        onPress={() => {
          setVisible(false);
        }}>
        {blurBackground ? (
          <BlurView
            blurType="dark"
            blurAmount={1}
            reducedTransparencyFallbackColor="white"
            style={StyleSheet.absoluteFillObject}
          />
        ) : (
          <Animated.View style={[styles.overlayLayer, overlayStyle]} />
        )}

        <View style={[styles.modalContainer, containerStyle]}>
          <Animated.View
            onStartShouldSetResponder={() => true}
            style={[
              {
                ...styles.modal,
                width: width,
                ...style,
              },
              transformTranslate,
            ]}>
            {children}
          </Animated.View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}
