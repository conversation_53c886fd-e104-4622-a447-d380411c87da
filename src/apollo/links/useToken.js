/* eslint-disable prettier/prettier */
import {ApolloLink, Observable} from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {persistent} from '../../assets/strings';

const useToken = new ApolloLink((operation, forward) => {
  return new Observable(observer => {
    let handle;

    (async () => {
      const token = await AsyncStorage.getItem(persistent.ACCESS_TOKEN);
      operation.setContext({
        headers: {
          authorization: `Bearer ${token}`,
        },
      });

      handle = forward(operation).subscribe({
        next: observer.next.bind(observer),
        error: observer.error.bind(observer),
        complete: observer.complete.bind(observer),
      });
    })();

    return () => {
      if (handle) {
        handle.unsubscribe();
      }
    };
  });
});

export default useToken;
