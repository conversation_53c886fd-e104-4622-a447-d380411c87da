import React from 'react';
import {ReactNativeFile} from 'apollo-upload-client';
import {useMutation} from '@apollo/react-hooks';

import {UPLOAD_FILE} from '../../apollo/mutations';

const useImageUpload = () => {
  const [progressPercentile, setProgressPercentile] = React.useState(0);
  console.log("Inside useImageUpload kapil---");
  const [uploadFile, {loading, data}] = useMutation(UPLOAD_FILE, {
    context: {
      fetchOptions: {
        useUpload: true,
        onProgress: progress => {
          const percentage = (progress?.loaded * 100) / progress?.total;
          if (percentage !== null && !isNaN(percentage)) {
            setProgressPercentile(percentage);
          }
        },
        onAbortPossible: () => null,
      },
    },
  });

  const upload = image =>
    new Promise(async (resolve, reject) => {
      {
        const date = new Date();
        const nameComponents = [
          date.getFullYear(),
          date.getMonth(),
          date.getDate(),
          date.getHours(),
          date.getMinutes(),
          date.getSeconds(),
          date.getMilliseconds(),
          Math.floor(Math.random() * 1000),
          image.filename,
        ];

        try {
          const file = new ReactNativeFile({
            uri: image.path,
            name: nameComponents.join(''),
            type: image.mime,
          });
          const {data: createData} = await uploadFile({
            variables: {file},
          });

          const success = createData?.uploadFile?.success;
          const imageData = createData?.uploadFile?.data;
          const message = createData?.uploadFile?.message;

          if (success) {
            resolve(imageData);
          } else {
            reject(message);
          }
        } catch (error) {
          reject(error);
        }
      }
    });
  return {
    uploading: loading,
    upload,
    data,
    progressPercentile,
  };
};

export default useImageUpload;
