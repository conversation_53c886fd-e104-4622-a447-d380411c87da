// @ts-check
// Go to axiosConfig.info.js in ../source/tools to get all the details
import axios from 'axios';
import {API_URL, API_URL_DEV} from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {persistent} from '../assets/strings';
console.log('API_URL', API_URL);
console.log('API_URL_DEV', API_URL_DEV);
const instance = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? API_URL_DEV : API_URL,
});

instance.interceptors.request.use(
  async config => {
    config.headers['Content-Type'] = 'application/json';
    config.headers['Accept'] = 'application/json';
    try {
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem(persistent.ACCESS_TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // console.log('AXIOS: Request Token Error', error);
      }
    }

    return config;
  },
  error => Promise.reject(error),
);

let isRefreshing = false;
let subscribers = [];

instance.interceptors.response.use(
  response => {
    if (process.env.NODE_ENV === 'development') {
      // console.log('AXIOS RESPONSE', JSON.stringify({response}, null, 2));
    }
    return response;
  },
  async err => {
    // console.log({err});
    try {
      const {config, response} = err;
      const status = response?.status;

      // if (status === 401) {
      //   if (!isRefreshing) {
      //     isRefreshing = true;

      //     const refreshToken = await AsyncStorage.getItem(
      //       persistent.REFRESH_TOKEN,
      //     );
      //     const fetchRefreshToken = await axios({
      //       url: API_URL + 'refresh-token',
      //       method: 'post',
      //       data: {
      //         refreshToken,
      //       },
      //       headers: {
      //         Accept: 'application/json',
      //         'Content-Type': 'application/json',
      //       },
      //     });

      //     if (fetchRefreshToken?.status === 200) {
      //       isRefreshing = false;
      //       await AsyncStorage.multiSet([
      //         [persistent.ACCESS_TOKEN, fetchRefreshToken?.data?.access_token],
      //         [
      //           persistent.REFRESH_TOKEN,
      //           fetchRefreshToken?.data?.refresh_token,
      //         ],
      //       ]);
      //       config.headers[
      //         'Authorization'
      //       ] = `Bearer ${fetchRefreshToken?.data?.access_token}`;
      //     }
      //     subscribers = [];
      //   }

      //   const requestSubscribers = new Promise(resolve => {
      //     subscribeTokenRefresh(() => {
      //       resolve(axios(config));
      //     });
      //   });

      //   onRefreshed();

      //   return requestSubscribers;
      // } else {
      return Promise.reject(err);
      // }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // console.error('AXIOS ERROR', error);
      }
    }
  },
);

function subscribeTokenRefresh(cb) {
  subscribers.push(cb);
}

function onRefreshed() {
  subscribers.map(cb => cb());
}

subscribers = [];

// Custom Fetch
const parseHeaders = rawHeaders => {
  const headers = new Headers();
  const preProcessedHeaders = rawHeaders.replace(/\r?\n[\t ]+/g, ' ');
  preProcessedHeaders.split(/\r?\n/).forEach(line => {
    const parts = line.split(':');
    const key = parts.shift().trim();
    if (key) {
      const value = parts.join(':').trim();
      headers.append(key, value);
    }
  });
  return headers;
};

const uploadFetch = (url, options) =>
  new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    if (xhr.upload) {
      xhr.upload.onprogress = options.onProgress;
    }
    xhr.open(options.method, url, true);

    Object.keys(options.headers).forEach(key => {
      xhr.setRequestHeader(key, options.headers[key]);
    });

    xhr.onload = () => {
      const opts = {
        status: xhr.status,
        statusText: xhr.statusText,
        headers: parseHeaders(xhr.getAllResponseHeaders() || ''),
      };
      opts.url =
        'responseURL' in xhr
          ? xhr.responseURL
          : opts.headers.get('X-Request-URL');
      const body = 'response' in xhr ? xhr.response : xhr.responseText;
      resolve(new Response(body, opts));
    };

    xhr.onerror = () => {
      reject(new TypeError('Network request failed'));
    };
    xhr.ontimeout = () => {
      reject(new TypeError('Network request failed'));
    };

    options.onAbortPossible(() => {
      xhr.abort();
    });

    xhr.send(options.body);
  });

export const customFetch = (uri, options) => {
  if (options.useUpload) {
    return uploadFetch(uri, options);
  }
  return fetch(uri, options);
};
//

export default instance;
