import React, { useEffect, useState } from 'react';
import { Modal, View, TouchableOpacity, StyleSheet, Image, Platform, Text } from 'react-native';
import <PERSON><PERSON>iewer from 'react-native-image-zoom-viewer';
import RNFS from 'react-native-fs';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import ActivityIndicator from '../../components/ActivityIndicator';
import { icons, screens } from '../../assets/strings';
import {
  API_AI_PREDICT_URL_DEV,
  API_AI_PREDICT_URL
} from '@env';
import { useCredential } from '../../tools/hooks/useUser';

const ImageViewerModal = ({ isVisible, images, imageStatus, onClose, initialIndex, isPassed, navigation, tag, tagYears, keysData }) => {

  const [loading, setLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const [isImagePassed, setIsImagePassed] = useState(false);
  const credential = useCredential()
  const isExpert = React.useMemo(() => credential?.role_id == 3, [
    credential?.role_id,
  ]);


  useEffect(() => {
    if (isVisible) {
      fetchFrontData(initialIndex)
    }
  }, [isVisible]);


  const onImageChange = (index) => {
    // Call the API or perform any necessary action with the new index
    fetchFrontData(index)
  };


  const fetchFrontData = async (runningIndex) => {

    setCurrentIndex(runningIndex)
    const image = imageStatus[runningIndex];
    if(image["passed"] == 1) {
      setIsImagePassed(1)
    } else {
      setIsImagePassed(0)
    }
    
    // if (image.isPassed == null) {       

    //   fetchData(image).then((result) => {
    //     if (result) {
    //       if (result.predictions.length > 0) {
  
    //         let fakeCount = 0;
    //         let realCount = 0;
    //         //const updatedData = [];
    //         for (const [index, results] of result.predictions.entries()) {
    //           if (results.prediction == 'real') {
    //             //updatedData.push({type: front.images[index].type, uri: front.images[index].uri, passed: 1});
    //             realCount = realCount + 1;
    //           } else {
    //             //updatedData.push({type: front.images[index].type, uri: front.images[index].uri, passed: 0});
    //             fakeCount = fakeCount + 1;
    //           }
  
    //         }
  
    //         let overallResult = "Overall Prediction is: Fake";
    //         if (realCount > fakeCount) {
    //           overallResult = "Overall Prediction is: Pass";
    //           images[runningIndex].isPassed = true
    //           setIsImagePassed(true)
    //         } else {
    //           images[runningIndex].isPassed = false
    //           setIsImagePassed(false)
    //         }
  
    //         /*if (runningIndex == 0) {
    //           setfront({"images": updatedData});  
    //         } else if (runningIndex == 1) {
    //           setFrontCloseUp({"images": updatedData});  
    //         } else if (runningIndex == 2) {
    //           setFrontTag({"images": updatedData});  
    //         } else if (runningIndex == 3) {
    //           setBack({"images": updatedData});  
    //         } else if (runningIndex == 4) {
    //           setBackCloseup({"images": updatedData});  
    //         } else if (runningIndex == 5) {
    //           setBackTag({"images": updatedData});  
    //         } else if (runningIndex == 6) {
    //           setCopyright({"images": updatedData});  
    //         } else if (runningIndex == 7) {
    //           setStitichingSleeve({"images": updatedData});  
    //         } else if (runningIndex == 8) {
    //           setStitichingHem({"images": updatedData});  
    //         }*/
  
    //       }
  
    //     }
    //   });
    // } else {
    //   setIsImagePassed(image.isPassed)
    // }
  }

  const handleRedriect = (props) => {
    if (isExpert || tag) {
      console.log("Kapil---------->")
      onClose()
      setTimeout(() => {
        navigation.push(screens.SINGLE_LISTING, {key: keysData[currentIndex]})
      }, 100)
      
      // close
    }
  }

  const fetchData = async (imagesList) => {

    setLoading(true); // Start loading
    setIsImagePassed(false)

    const localPaths = await Promise.all([imagesList].map((url, index) => {
      const filename = `image_${index}.jpg`; // Use a unique filename          
      return downloadImage(url.url, filename);
    }));

    console.log("localPaths=======>", localPaths)

    const formData = new FormData();
    for (const imagePath of localPaths) {

      const fileNew = {
        uri: imagePath,
        type: 'image/jpeg',
        name: imagePath.split('/').pop(),
      };
      formData.append('images', fileNew);
    }


    const url = process.env.NODE_ENV == 'development' ? API_AI_PREDICT_URL_DEV : API_AI_PREDICT_URL


    try {
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      const result = await response.json();
      console.log("result=====", result)
      return result;
    } catch (error) {
      console.error('appraisalURL Error uploading imagesssssss:', appraisalURL);
      return null;
    } finally {
      setLoading(false); // End loading
    }
  }

  // Helper function to get a Blob from a remote image URL
  async function downloadImage(imageUrl, filename) {
    const isIOS = Platform.OS == 'ios';
    const downloadDest = isIOS ? `${RNFS.DocumentDirectoryPath}/${filename}`
      : `file://${RNFS.DocumentDirectoryPath}/${filename}`;

    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]); // Remove the data URL prefix
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      await RNFS.writeFile(downloadDest, base64, 'base64');
      return downloadDest;
    } catch (error) {
      console.error('Error downloading image:', error);
      throw error;
    }
  }

  // Custom rendering of the image so we can add the icon at the end
  const renderCustomImage = (props) => {
    return (

      <View style={{ position: 'relative' }}>
        {/* Image itself */}

        <Image
          source={{ uri: props.source.uri }}
          style={styles.imageStyle}
          resizeMode="contain"
        />

        {/* Icon at the bottom of the image */}

        {loading ? (
          <ActivityIndicator style={styles.bottomIconContainer2} />
        ) : (

          <TouchableOpacity 
          onPress={() => handleRedriect(props)} 
          style={styles.bottomIconContainer} >
            <Icon
              name={isImagePassed ? icons.PASS_ICON_2 : icons.CANCEL_X_2}
              tint={isImagePassed ? colors.green : colors.warning}
              size={30}
            />
          </TouchableOpacity>
        )}

      </View>
    );
  };

  return (
    <Modal visible={isVisible} transparent={true} animationType="slide">
      <View style={styles.container}>
        {/* ImageViewer */}

        <ImageViewer
          imageUrls={images}
          index={initialIndex}
          enableSwipeDown={true}
          onSwipeDown={onClose}
          enableImageZoom={true}
          loadingRender={() => <ActivityIndicator />}
          renderIndicator={() => null}
          renderImage={renderCustomImage} // Custom image rendering
          onChange={onImageChange} // Trigger API call on image index change

        />


        {/* Close Button */}
        {/* {tag && tagYears && (
          <Text style={styles.textButton}>{tag + " " + tagYears[currentIndex] }</Text>
        )} */}
        
        <TouchableOpacity
          onPress={onClose}
          style={styles.closeButton}
        >
          <Icon
            name="cancel_x"
            size={15}
            tint={colors.black}
            containerStyle={styles.iconContainer}
            clickable
            onPress={onClose}
          />
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  closeButton: {
    position: 'absolute',
    top: 60,
    right: 20,
  },

  textButton: {
    position: 'absolute',
  top: 70, // Adjust this value if needed
  left: 0,
  right: 0,
  textAlign: 'center',
  fontSize: 16,
  fontWeight: 'bold',
  color: 'white',
  },

  iconContainer: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 20,
  },
  imageStyle: {
    width: '100%',
    height: '100%', // Ensure it fills the available space
  },
  bottomIconContainer: {
    position: 'absolute',
    bottom: 5, // Position icon 10 pixels from the bottom of the image
    right: 5,
    alignSelf: 'flex-end', // Center icon horizontally
    backgroundColor: 'white', // Optional: background for visibility
    padding: 0,
    borderRadius: 15,
  },
  bottomIconContainer2: {
    position: 'absolute',
    bottom: 5, // Position icon 10 pixels from the bottom of the image
    right: 5,
    alignSelf: 'flex-end', // Center icon horizontally
    padding: 0,
    borderRadius: 15,
  },
});

export default ImageViewerModal;
