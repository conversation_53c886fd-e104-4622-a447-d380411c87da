import React from 'react';
import { Modal, View, Text, Image, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import FastImage from '../../components/FastImage';
import { images } from '../../assets/strings';
import { Dimensions } from 'react-native';
import { spacings } from '../../assets/spacing';

const {width} = Dimensions.get('window');
const WIDTH = width;
const CARD_WIDTH = WIDTH - spacings.xxl;


const AIResultDialog = ({ visible, onClose, completeItem }) => {
    
    // Define dynamic labels for each image index
    const labels = ['Front', 'Back', 'Tag', 'Copyright', 'Stitching Sleeve', 'Stitching Hem'];


    const allImageUrls = [
        completeItem.images.front,
        //completeItem.images.closeup_front,
        completeItem.images.back,
        //completeItem.images.closeup_back,
        completeItem.images.front_tag,
        //completeItem.images.back_tag,
        completeItem.images.lower_hem_stitching,
        completeItem.images.arm_hem_stitching,
        completeItem.images.copyright,
        //completeItem.images.extra,   
    ];


    // const sectionsData = [
    //     [
    //       { image: {"uri": allImageUrls[0]}, label: 'Front' },
    //       { image: {"uri": allImageUrls[1]}, label: 'Back' },
    //     ],
    //     [
    //         { image:{"uri": allImageUrls[2]}, label: 'Tag' },
    //         { image: {"uri": allImageUrls[5]}, label: 'Copyright' },
    //     ],
    //     [
    //         { image: {"uri": allImageUrls[4]}, label: 'Stitching Sleeve' },
    //         { image: {"uri": allImageUrls[3]}, label: 'Stitching Hem' },
    //     ],
    //   ];
      


// Function to generate dynamic sectionsData
/*const generateSectionsData = (allImageUrls) => {
  const sections = [];

  // Create pairs of images dynamically
  for (let i = 0; i < allImageUrls.length; i += 2) {
    const section = [];

    // First item in the pair
    if (allImageUrls[i]) {
      section.push({
        image: { uri: allImageUrls[i] },
        label: labels[i] || `Image ${i + 1}`, // Default label if out of labels array
      });
    }

    // Second item in the pair
    if (allImageUrls[i + 1]) {
      section.push({
        image: { uri: allImageUrls[i + 1] },
        label: labels[i + 1] || `Image ${i + 2}`, // Default label if out of labels array
      });
    }

    sections.push(section);
  }

  return sections;
};*/


const generateSectionsData = (allImageUrls) => {
  const sections = [];
  let labelIndex = 0; // Track the label position

  // Create a section dynamically if the image is available
  const createSection = (imageUri, label) => {
    return {
      image: { uri: imageUri },
      label: label || `Image ${labelIndex + 1}`, // Fallback label if not in predefined array
    };
  };

  let tempSection = []; // Temporary storage to hold the current pair

  for (let i = 0; i < allImageUrls.length; i++) {
    if (allImageUrls[i]) {
      tempSection.push(createSection(allImageUrls[i], labels[labelIndex]));

      labelIndex++; // Move to the next label

      // Push a completed pair (or just one image) every two iterations
      if (tempSection.length === 2) {
        sections.push(tempSection);
        tempSection = []; // Reset for next section
      }
    }
  }

  // If there is one image left after the loop (odd number of images), add it as a section
  if (tempSection.length > 0) {
    sections.push(tempSection);
  }

  return sections;
};


// Example usage:
const sectionsData = generateSectionsData(allImageUrls);





  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.overlay}>
        <View style={styles.dialog}>
        <View style={styles.header}>
              <Text style={styles.headerText}>AI Overall Result: Pass/Fail</Text>
              <TouchableOpacity onPress={onClose}>
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>

            {/* Overall Result */}
            <View style={styles.resultSection}>
              <Text style={styles.resultText}>Overall Result: Pass/Fail</Text>
            </View>
          <ScrollView>
            {/* Header */}
            

            {/* Dynamic Content Section */}
            <View style={styles.section}>
              {sectionsData.map((row, rowIndex) => (
                <View key={rowIndex} style={styles.row}>
                  {row.map((item, colIndex) => (
                    <View key={colIndex} style={styles.column}>

                        {console.log("rowIndex ===", rowIndex)}
                          <View key={colIndex} style={styles.row2}>

                          <Image style={styles.icon} source={item.image ? item.image : images.PLACEHOLDER} 
                           resizeMode="contain"/>

                        <Image style={styles.icon} source={item.image ? item.image : images.PLACEHOLDER} 
                           resizeMode="contain"/>
                              {/* <FastImage
                                  source={
                                      item.image.uri
                                          ? item.image
                                          : images.PLACEHOLDER
                                  }

                                  style={styles.icon}
                              />

                              <FastImage
                                  source={
                                      item.image.uri
                                          ? item.image
                                          : images.PLACEHOLDER
                                  }

                                  style={styles.icon}
                              /> */}
                          </View>

                      <Text>{item.label}</Text>
                    </View>
                  ))}
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialog: {
    width: '90%',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    fontSize: 16,
    color: 'red',
  },
  resultSection: {
    backgroundColor: '#d1e7dd',
    padding: 10,
    borderRadius: 8,
    marginBottom: 20,
  },
  resultText: {
    fontSize: 16,
    color: '#000',
  },
  section: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  column: {
    alignItems: 'center',
  },
  row2: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  icon: {
    width: '25%',
    height: 100,
    //height: CARD_WIDTH / 2,
    borderRadius: 2,
    marginEnd:10
  },
});

export default AIResultDialog;
