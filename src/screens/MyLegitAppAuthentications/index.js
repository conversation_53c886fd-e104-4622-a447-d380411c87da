import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {useQuery} from '@apollo/client';
import MyAuthenticatonsLoader from '../../components/Loaders/MyAuthenticatonsLoader';
import {GET_LEGITAPP_ALL_POSTS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import LegitAppOrderCard from '../../components/LegitAppOrderCard';
import {fontSize} from '../../assets/font';
import {icons, screens} from '../../assets/strings';
import Icon from '../../components/Icon';

const Index = ({navigation}) => {
  const [refreshing, setRefreshing] = useState(false);

  const {data, refetch, loading} = useQuery(GET_LEGITAPP_ALL_POSTS);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch?.();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const handleOrderPress = useCallback(
    item => {
      navigation.navigate(screens.LEGITAPP_ORDER_DETAIL, {
        orderId: item.id,
      });
    },
    [navigation],
  );

  const renderItem = useCallback(
    ({item}) => {
      return <LegitAppOrderCard item={item} onPress={handleOrderPress} />;
    },
    [handleOrderPress],
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Filter Button */}
      <TouchableOpacity style={styles.filterButton}>
        <Icon name={icons.ELIPSIS_HORIZONTAL} size={20} tint={colors.white} />
      </TouchableOpacity>

      {/* ALL Button */}
      <TouchableOpacity style={styles.allButton}>
        <Text style={styles.allButtonText}>ALL</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAdditionalPhotosCard = () => (
    <TouchableOpacity style={styles.additionalPhotosCard}>
      <View style={styles.additionalPhotosContent}>
        <View style={styles.warningIconContainer}>
          <Icon
            name={icons.QUESTIONMARK_CIRCLE}
            size={24}
            tint={colors.amber}
          />
        </View>
        <View style={styles.additionalPhotosTextContainer}>
          <Text style={styles.additionalPhotosTitle}>
            Additional Photos Required
          </Text>
          <Text style={styles.additionalPhotosId}>#9914709694915429</Text>
        </View>
        <Icon name={icons.ARROW_RIGHT} size={20} tint={colors.black} />
      </View>
    </TouchableOpacity>
  );

  const currentData = data?.getLegitAppAuthentications?.data;
  // console.log('currentData', JSON.stringify(currentData, '\t', 2));
  return (
    <View style={styles.container}>
      <FlatList
        data={currentData}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString?.()}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={
          <View>
            {renderHeader()}
            {renderAdditionalPhotosCard()}
          </View>
        }
        ListEmptyComponent={
          currentData == null && loading ? null : (
            <View style={styles.emptyView}>
              <Icon
                name={icons.UPLOAD_PHOTO}
                size={70}
                tint={colors.darkGrey}
              />
              <Text style={styles.emptyViewText}>
                No Legit App orders found
              </Text>
            </View>
          )
        }
        ListFooterComponent={
          currentData == null && loading ? <MyAuthenticatonsLoader /> : null
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // borderWidth: 1,
    // borderColor: colors.red,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.secondaryBlack,
    justifyContent: 'center',
    alignItems: 'center',
  },
  allButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacings.xl,
    paddingVertical: spacings.sm,
    borderRadius: 20,
  },
  allButtonText: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '600',
  },
  additionalPhotosCard: {
    backgroundColor: colors.white,
    marginHorizontal: spacings.sm,
    marginBottom: spacings.md,
    // borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.amber,
  },
  additionalPhotosContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacings.lg,
  },
  warningIconContainer: {
    marginRight: spacings.md,
  },
  additionalPhotosTextContainer: {
    flex: 1,
  },
  additionalPhotosTitle: {
    color: colors.black,
    fontSize: fontSize.md,
    fontWeight: '600',
    marginBottom: spacings.xs,
  },
  additionalPhotosId: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
  },
  emptyView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacings.xxl,
  },
  emptyViewText: {
    fontSize: fontSize.lg,
    color: colors.darkGrey,
    marginTop: spacings.md,
    textAlign: 'center',
  },
});

export default Index;
