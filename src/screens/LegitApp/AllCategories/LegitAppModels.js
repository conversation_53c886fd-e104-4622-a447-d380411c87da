import React from 'react';
import { StyleSheet, Text, View, FlatList, Dimensions } from 'react-native';
import { useQuery } from '@apollo/client';

import AllCategoriesLoader from '../../../components/Loaders/AllCategoriesLoader';
import { GET_LEGIT_APP_MODELS } from '../../../apollo/queries';
import colors from '../../../assets/colors';
import { spacings } from '../../../assets/spacing';
import { screens, images } from '../../../assets/strings';
import { fontSize } from '../../../assets/font';
import QueryManager from '../../../components/QueryManager';
import locale from '../../../assets/locale.json';
import RefreshControl from '../../../components/RefreshControl';
import FastImage from '../../../components/FastImage';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import { useInteractionManager } from '../../../tools/hooks';

const { width } = Dimensions.get('window');
const WIDTH = width - spacings.sm;
const CARD_WIDTH = WIDTH / 3.2;

const LegitAppModels = ({ route, navigation }) => {
  const categoryFromRoute = route?.params?.selectedCategory;
  const brandFromRoute = route?.params?.selectedBrand;

  const { isInteractionsComplete } = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const [refreshing, setRefreshing] = React.useState(false);

  const { data, loading, error, refetch } = useQuery(GET_LEGIT_APP_MODELS, {
    variables: {
      categoryId: categoryFromRoute?.id,
      brandId: brandFromRoute?.id
    },
  });

  const rawData = data?.getLegitAppModels?.data || [];
  const remainder = rawData.length % 3;
  const paddedData = remainder === 0 ? rawData : [...rawData, ...Array(3 - remainder).fill({ isPlaceholder: true })];

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', e => {
      try {
        refetch?.();
      } catch (error) { }
    });
    return unsubscribe;
  }, [navigation, refetch]);

  const handleCategoryClick = React.useCallback(
    item =>
      navigation.push(screens.LEGIT_APP_NEW_AUTHENTICATION, {
        selectedModel: item,
        selectedCategory: categoryFromRoute,
        selectedBrand: brandFromRoute
      }),
    [navigation.push],
  );

  function formatData(data, numColumns) {
    if (!Array.isArray(data)) {
      //console.warn("formatData expected an array but got:", data);
      return [];
    }
  
    const formattedData = [...data]; // safe now
    const numberOfFullRows = Math.floor(formattedData.length / numColumns);
    let numberOfElementsLastRow = formattedData.length - numberOfFullRows * numColumns;
  
    let index = 1001;
    while (numberOfElementsLastRow !== 0 && numberOfElementsLastRow < numColumns) {
      formattedData.push({
        id: index,
        key: `blank-${numberOfElementsLastRow}`,
        isPlaceholder: true,
      });
      numberOfElementsLastRow++;
      index++;
    }
  
    return formattedData;
  }
  
  

  const renderItem = React.useCallback(
    ({ item }) => {
      if (item.isPlaceholder) {
        return <View style={{ flex: 1, margin: 8 }} />;
      }
  
      return (
        <TouchAwareButton
          style={styles.card}
          onPress={() => handleCategoryClick(item)}
        >
          <View style={styles.cardContent}>
            <FastImage
              source={
                item?.icon_image_url?.length ? { uri: item.icon_image_url } : images.logo2
              }
              style={styles.imageStyle}
              resizeMode="contain"
            />
            <Text
              style={[
                styles.titleStyle,
                { fontFamily: 'Inter-Medium', width: CARD_WIDTH - 10 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}
            >
              {item.title}
            </Text>
          </View>
        </TouchAwareButton>
      );
    },
    [handleCategoryClick]
  );
  

  const keyExtractor = React.useCallback(item => item.id.toString(), []);

  return (
    <View style={styles.container}>
      <QueryManager
        {...{
          data: !(data == null) && isInteractionsComplete,
          error,
          refetch,
          loading: loading || !isInteractionsComplete,
        }}>
        <QueryManager.Data>
          <FlatList
            data={formatData(data?.getLegitAppModels?.data, 3)}
            renderItem={renderItem}
            numColumns={3}
            columnWrapperStyle={styles.contentContainer}
            keyExtractor={keyExtractor}
            refreshControl={<RefreshControl {...{ onRefresh, refreshing }} />}
            initialNumToRender={8}
            contentContainerStyle={{ paddingBottom: 120 }}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <AllCategoriesLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale['SomethingWentWrong.']}
        />
      </QueryManager>

      {/* Bottom Fixed Button */}
      <View style={styles.bottomButtonContainer}>
        <TouchAwareButton
          style={styles.bottomButton}
          onPress={() => {

            navigation.push(screens.LEGIT_APP_NEW_AUTHENTICATION, {
              selectedModel: {
                id: -1,
                title: 'Other',
                description: null,
              },
              selectedCategory: categoryFromRoute,
              selectedBrand: brandFromRoute
            })


          }}>
          <Text style={styles.bottomButtonText}>
            Can’t find your model?{'\n'}
            <Text style={{ textDecorationLine: 'underline' }}>Click Here</Text>
          </Text>
        </TouchAwareButton>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: spacings.lg,
  },
  cardContent: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    flex: 1,
    height: '100%',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: colors.darkGreyBorder,
  },
  imageStyle: {
    height: CARD_WIDTH * 0.2,
    width: CARD_WIDTH * 0.2,
    alignSelf: 'center',
    resizeMode: 'contain',
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.white,
    fontWeight: 'bold',
    textTransform: 'capitalize',
    textAlign: 'center',
    marginTop: spacings.lg,
    paddingHorizontal: spacings.lg,
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  card: {
    marginBottom: 12,
    backgroundColor: colors.black,
    justifyContent: 'center',
    alignItems: 'center',
    width: CARD_WIDTH,
    height: WIDTH / 2.8,
  },

  // 🔽 Bottom Button Styles
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 20,
    left: spacings.xxxl,
    right: spacings.xxxl,
    zIndex: 10,
  },
  bottomButton: {
    backgroundColor: '#FF3366',
    borderRadius: 25,
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomButtonText: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default LegitAppModels;
