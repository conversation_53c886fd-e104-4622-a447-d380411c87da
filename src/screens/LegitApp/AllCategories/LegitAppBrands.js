import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  Dimensions
} from 'react-native';
import { useQuery } from '@apollo/client';

import AllCategoriesLoader from '../../../components/Loaders/AllCategoriesLoader';
import { GET_LEGIT_APP_BRAND } from '../../../apollo/queries';
import colors from '../../../assets/colors';
import { spacings } from '../../../assets/spacing';
import { screens, images } from '../../../assets/strings';
import { fontSize } from '../../../assets/font';
import QueryManager from '../../../components/QueryManager';
import locale from '../../../assets/locale.json';
import RefreshControl from '../../../components/RefreshControl';
import FastImage from '../../../components/FastImage';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import { useInteractionManager } from '../../../tools/hooks';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width - spacings.sm;

const LegitAppBrands = ({ route, navigation }) => {
  const selectedCategory = route?.params?.params;
  const { isInteractionsComplete } = useInteractionManager({ allowedPlatforms: ['android'] });

  const [refreshing, setRefreshing] = useState(false);

  const { data, loading, error, refetch } = useQuery(GET_LEGIT_APP_BRAND, {
    variables: { categoryId: selectedCategory?.id },
    cacheId: GET_LEGIT_APP_BRAND,
  });

  const onRefresh = useCallback(async (enableRefresh = true) => {
    try {
      setRefreshing(enableRefresh);
      await refetch();
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', () => {
      refetch?.();
    });

    return unsubscribe;
  }, [navigation, refetch]);

  const handleBrandPress = useCallback(
    (brand) => {
      navigation.push(screens.LEGIT_APP_ALLMODEL, {
        params: brand,
        selectedCategory,
        selectedBrand: brand,
      });
    },
    [navigation, selectedCategory]
  );

  const renderBrandItem = useCallback(
    ({ item }) => (
      <TouchAwareButton style={styles.card} onPress={() => handleBrandPress(item)}>
        <View style={styles.cardContent}>
          <FastImage
            source={item?.icon_image_url?.length ? { uri: item.icon_image_url } : images.logo2}
            style={styles.imageStyle}
            resizeMode="contain"
          />
          <Text style={styles.titleStyle} numberOfLines={2} ellipsizeMode="tail">
            {item.title}
          </Text>
        </View>
      </TouchAwareButton>
    ),
    [handleBrandPress]
  );

  const renderHorizontalItem = ({ item }) => (
    <TouchAwareButton style={styles.horizontalItem} onPress={() => handleBrandPress(item)}>
      <FastImage
        source={item?.icon_image_url?.length ? { uri: item.icon_image_url } : images.logo2}
        style={styles.horizontalImage}
        resizeMode="contain"
      />
      <Text
        style={styles.horizontalText}
        numberOfLines={1}
        adjustsFontSizeToFit
        minimumFontScale={0.5}
      >{item.title}</Text>
    </TouchAwareButton>
  );

  const keyExtractor = useCallback(item => item.id?.toString(), []);
  const featuredBrands = useMemo(
    () => data?.getLegitAppBrands?.data?.filter(item => item.featured),
    [data]
  );

  return (
    <View style={styles.container}>
      <QueryManager
        data={data && isInteractionsComplete}
        error={error}
        loading={loading || !isInteractionsComplete}
        refetch={refetch}
      >
        <QueryManager.Data>
          <FlatList
            data={data?.getLegitAppBrands?.data}
            renderItem={renderBrandItem}
            numColumns={1}
            keyExtractor={keyExtractor}
            refreshControl={<RefreshControl onRefresh={onRefresh} refreshing={refreshing} />}
            initialNumToRender={8}
            ListHeaderComponent={
              featuredBrands?.length > 0 && (
                <View>
                  <Text
                    style={[styles.titleStyle2, { fontFamily: 'Inter-Medium', marginBottom: 20 }]}
                    numberOfLines={1}
                    ellipsizeMode="clip"
                  >
                    Featured Brands
                  </Text>
                  <FlatList
                    data={featuredBrands}
                    renderItem={renderHorizontalItem}
                    horizontal
                    keyExtractor={keyExtractor}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.horizontalList}
                  />
                </View>
              )
            }
          />
        </QueryManager.Data>

        <QueryManager.Loading>
          <AllCategoriesLoader />
        </QueryManager.Loading>

        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale['SomethingWentWrong.']}
        />
      </QueryManager>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: spacings.lg,
  },
  card: {
    marginBottom: 12,
    backgroundColor: colors.black,
    alignItems: 'stretch',
    width: CARD_WIDTH,
    height: 80,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacings.md,
    flex: 1,
    height: '100%',
    borderBottomWidth: 0.5,
    borderBottomColor: colors.darkGreyBorder,
    justifyContent: 'flex-start',
    marginLeft: 10,
  },
  imageStyle: {
    height: 35,
    width: 35,
    resizeMode: 'contain',
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.white,
    fontWeight: 'bold',
    textTransform: 'capitalize',
    marginLeft: spacings.lg,
    flexShrink: 1,
  },
  titleStyle2: {
    fontSize: fontSize.xl,
    color: colors.white,
    fontWeight: 'bold',
    marginLeft: fontSize.md,
    textTransform: 'capitalize',
  },
  horizontalList: {
    paddingHorizontal: spacings.md,
    paddingBottom: spacings.md,
  },
  horizontalItem: {
    alignItems: 'center',
    marginRight: 15,
    borderWidth: 1,
    borderColor: colors.darkGreyBorder,
    borderRadius: 4,
    padding: 5,
    height: 100,
    justifyContent: 'center',
    width: 85,
  },
  horizontalImage: {
    width: 30,
    height: 30,
    borderRadius: 25,
    resizeMode: 'cover',
  },
  horizontalText: {
    marginTop: 4,
    color: colors.white,
    fontSize: fontSize.sm,
    textAlign: 'center',
    paddingHorizontal: spacings.sm
  },
});

export default LegitAppBrands;
