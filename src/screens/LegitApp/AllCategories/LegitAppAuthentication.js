import React, { useCallback, useState } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Text,
  RefreshControl,
} from 'react-native';
import { useQuery } from '@apollo/client';
import { useFocusEffect } from '@react-navigation/native';

import MyAuthenticatonsLoader from '../../../components/Loaders/MyAuthenticatonsLoader';
import { GET_LEGITAPP_ALL_POSTS } from '../../../apollo/queries';
import colors from '../../../assets/colors';
import { spacings } from '../../../assets/spacing';
import MyAuthCard from '../../../components/MyAuthCard';
import { fontSize } from '../../../assets/font';
import { icons, images, screens } from '../../../assets/strings';
import Icon from '../../../components/Icon';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import BottomSheet from '../../../components/BottomSheet';
import locale from '../../../assets/locale.json';
import { useAuthenticationInfo } from '../../../tools/hooks';
import { APOLLO_CACHE_IDS } from '../../../assets/strings';
import LegitAppAuthCard from './LegitAppAuthCard';
import FastImage from '../../../components/FastImage';
import dayjs from 'dayjs';


const { height: SCREEN_HEIGHT } = Dimensions.get('screen');

const LIMIT = 100;
const { width } = Dimensions.get('window');
const WIDTH = width - spacings.sm;
const CARD_WIDTH = WIDTH / 2.2;


const LegitAppAuthentication = ({ navigation }) => {

  const [refreshing, setRefreshing] = useState(false);
  const scrollRef = React.useRef();

  const { data, refetch, loading } = useQuery(GET_LEGITAPP_ALL_POSTS, {
    variables: {
      limit: LIMIT, // TODO add pagination
      cacheId: APOLLO_CACHE_IDS.getPosts_LEGITAPP_AUTHENTICATIONS,
    },
  });

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    console.log("onRefresh=====>");
    try {
      await refetch?.();
    } catch (error) {
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (_) { }
    }, [refetch]),
  );


  const renderItem = React.useCallback(
    ({ item }) => {
      // Step 1: Parse request_data
      let requestData = {};
      try {
        requestData = item?.request_data ? JSON.parse(item.request_data) : {};
      } catch (e) {
        console.warn("Invalid JSON in request_data", e);
      }
  
      const categoryName = requestData?.category_name || 'Unknown Category';
      const brandName = requestData?.brand_name || 'Unknown Brand';
      const modelName = requestData?.model_name || 'Unknown Modal';
  
      return (
        <TouchAwareButton
          style={styles.card}
          onPress={handleCategoryClick.bind(null, item)}>
          <View style={styles.cardContent}>
            <FastImage
              source={
                item?.images?.[0]?.asset_link
                  ? { uri: item?.images[0]?.asset_link }
                  : images.logo2
              }
              style={styles.imageStyle}
              resizeMode="cover"
            />
  
            <Text
              style={[
                styles.titleStyle,
                { fontFamily: 'Inter-Medium', width: CARD_WIDTH - 8 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              {brandName}
            </Text>
  
            <Text
              style={[
                styles.titleStyle1,
                { fontFamily: 'Inter-Medium', width: CARD_WIDTH - 8 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              {categoryName}
            </Text>
  
            <Text
              style={[
                styles.titleStyle2,
                { fontFamily: 'Inter-Medium', width: CARD_WIDTH - 8 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </TouchAwareButton>
      );
    },
    [handleCategoryClick]
  );
  



  const handleCategoryClick = item => {
  }

  // const renderItem = React.useCallback(
  //   ({ item }) => {
  //     return (
  //       <TouchAwareButton
  //         onPress={() =>
  //           navigation.navigate(screens.SINGLE_LISTING, {
  //             params: item?.id,
  //           })
  //         }
  //         scaleAnimationThreshold={0.97}>
  //         <LegitAppAuthCard
  //           item={item}
  //           onMenuPressed={handleMenuPressed}
  //           showAuthenticationPendingStatus
  //         />
  //       </TouchAwareButton>
  //     );
  //   },
  //   [navigation.navigate, handleMenuPressed],
  // );

  return (
    <View style={styles.container}>
      {console.log("API Data======", data?.getLegitAppAuthentications?.data)}


      <FlatList
        data={data?.getLegitAppAuthentications?.data}
        renderItem={renderItem}
        numColumns={2}
        columnWrapperStyle={styles.contentContainer}
        keyExtractor={item => item?.id?.toString?.()}
        ListEmptyComponent={
          data?.getLegitAppAuthentications?.data == null && loading ? null : (
            <View style={styles.emptyView}>
              <Icon
                name={icons.UPLOAD_PHOTO}
                size={70}
                tint={colors.darkGrey}
              />
              <Text style={styles.emptyViewText}>{locale.NoPostsFound}</Text>
            </View>
          )
        }

        ListFooterComponent={
          data?.getLegitAppAuthentications?.data == null && loading ? (
            <MyAuthenticatonsLoader />
          ) : null
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            progressBackgroundColor={colors.lightGrey}
            colors={[colors.primary, colors.black]}
            tintColor={colors.primary}
            onRefresh={onRefresh}
          />
        }
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 2.3,
    justifyContent: 'center',
    alignItems: 'center',
  },

  card: {
    marginBottom: 12,
    backgroundColor: colors.black,
    justifyContent: 'center',
    alignItems: 'center',
    width: CARD_WIDTH,
    height: 240, // Ensure card has a fixed height
  },

  cardContent: {
    justifyContent: 'flex-start', // Align items to the top
    alignItems: 'flex-start',     // Align items to the left
    flexDirection: 'column',      // Stack image and text vertically
    flex: 1,                      // Allow it to take full space
    height: '100%',               // Ensure it fills the space within each card
    borderWidth: 1,
    borderRadius: 4,
    borderColor: colors.darkGreyBorder,
    width: '100%',                // Ensure it stretches to full width
  },

  imageStyle: {
    height: 160,              // Make the image fill the height of the card
    width: '100%',               // Ensure the image stretches to the full width of the card
    resizeMode: 'cover',         // Ensures the image stretches to fill the container without distortion
  },

  smallImageStyle: {
    width: 50,               // Set the size of the small image
    height: 50,              // Ensure it's 50px in height
    marginTop: spacings.sm,
    marginLeft: spacings.md // Add some space between the image and the text
  },

  titleStyle1: {
    fontSize: fontSize.md,
    color: colors.white,
    fontWeight: 'bold',
    textTransform: 'capitalize',
    textAlign: 'left',          // Text aligns to the left
    marginTop: spacings.sm,     // Adjust spacing between image and text
    width: '100%',              // Ensure title uses full width available
    paddingLeft: spacings.md,   // Add left padding for text alignment
  },

  titleStyle2: {
    fontSize: fontSize.sm,
    color: colors.darkGreyBorder,
    textTransform: 'capitalize',
    textAlign: 'left',          // Text aligns to the left
    width: '100%',
    marginTop: spacings.sm,          // Ensure title uses full width available
    paddingLeft: spacings.md,   // Add left padding for text alignment
  },

  titleStyle: {
    fontSize: fontSize.md,
    color: colors.white,
    textTransform: 'capitalize',
    textAlign: 'left',          // Text aligns to the left
    marginTop: spacings.md,     // Adjust spacing between image and text
    width: '100%',              // Ensure title uses full width available
    paddingLeft: spacings.md,   // Add left padding for text alignment
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: 15,
  },
});


export default LegitAppAuthentication;

