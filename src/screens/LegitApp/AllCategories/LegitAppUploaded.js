import React, { useCallback, useState } from 'react';
import { StyleSheet, TouchableOpacity, Text, View, Image } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';


import { authenticationTypeScreen } from '../../../assets/strings';
import ImagePickerModal from '../../../components/ImagePicker/LegitAppPickImageDialog';
import colors from '../../../assets/colors';
import { fontSize } from '../../../assets/font';
import { spacings } from '../../../assets/spacing';
import Icon from '../../../components/Icon';
import InfoModal from '../../../components/Modal/InfoModal';
import locale from '../../../assets/locale.json';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import ActivityIndicator from '../../../components/ActivityIndicator';
import { useWindowDimensions } from '../../../tools/hooks';
import { GRAPHQL_URL } from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { persistent } from '../../../assets/strings';
import axios from 'axios';

const IMAGE_INITIAL_STATE = {
  local: null,
  remote: null,
};

const LegitAppUploaded = ({ item, required, uploadOptions, defaultImageUrls = null, setRequiredImages }) => {


  const {
    window: { width: windowWidth },
  } = useWindowDimensions();
  const isMounted = React.useRef(false);

  const [imagePath, setImagePath] = useState(() => ({
    [item?.value]: IMAGE_INITIAL_STATE,
  }));

  const [loading, setLoading] = useState(false);
  const [pickDialogVisible, setPickDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);


  const handleImageUploadClick = () => {
    if(loading) {

    } else {
      setPickDialogVisible(true);
    }
  };

  const deleteImage = useCallback(
    (type) => {
      setImagePath(IMAGE_INITIAL_STATE);

      setRequiredImages(prev =>
        prev.filter(image => image !== type)
      );
    },
    [setRequiredImages]
  );

  const chooseImageUploadTypes = ({ value, image, method }) => {

  };

  const getExtensionFromMime = (mime) => {
    if (!mime) return 'png'; // fallback extension
    const parts = mime.split('/');
    return parts.length > 1 ? parts[1] : 'png';
  };

  async function uploadImage(img) {

    const token = await AsyncStorage.getItem(persistent.ACCESS_TOKEN);
    const form = new FormData();

    // 1. The GraphQL query and variables
    form.append('operations', JSON.stringify({
      query: `
    mutation UploadLegitAppImages($files: [Upload!]!) {
      uploadLegitAppImages(files: $files) {
        success
        message
        data {
          id
          s3_url
          legit_app_url
          asset_link
          image_type
          created_at
        }
      }
    }
  `,
      variables: {
        files: [null] // placeholder for file(s)
      }
    }));


    // 2. The map tells which form field corresponds to which variable
    form.append('map', JSON.stringify({
      '0': ['variables.files.0']
    }));

    const imagePath = img.path || img.sourceURL;

    // 3. Attach the actual file (Node.js version, for browser use `fileInput.files[0]`)
    //form.append('0', fs.createReadStream(img?.path)); // use actual image path

    const extension = getExtensionFromMime(img.mime);
    const fileName = img.fileName || `photo.${extension}`;
   
    form.append('0', {
      uri: imagePath,
      name: fileName,
      type: img.mime || 'image/png',
    });

    
    const response = await fetch(GRAPHQL_URL, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: form,
    });

    const resJson = await response.json();


    if (resJson.data && resJson.data.uploadLegitAppImages.success) {
      // Access the data only if success is true
      const imageData = resJson.data.uploadLegitAppImages.data;
      console.log('Upload response:', resJson.data.uploadLegitAppImages.data);
      const legitAppUrl = imageData[0]?.legit_app_url;

      if (legitAppUrl) {

        chooseImageUploadTypes({
          value: item.value,
          image: legitAppUrl,
        });
        setImagePath({
          [item?.value]: {
            local: img?.path,
            remote: legitAppUrl,
          },
        });
        setRequiredImages(prev => [...prev, legitAppUrl]);


      } else {
        console.error('Legit App URL is not available');
      }
      return imageData;
    } else {
      console.error('Image upload failed:', resJson.data.uploadLegitAppImages.message);
    }
  }

  const handleImageUpload = async img => {
    setLoading(true);
    console.log("Start Image Upload.....Kapil")
    try {
      await uploadImage(img);

    } catch (error) {
      console.log("Image Upload error==Kapil...>", error)
      setErrorDialogVisible(true);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (!isMounted.current) {
      const isValid =
        !(defaultImageUrls?.local == null) &&
        !(defaultImageUrls?.remote == null);

      setImagePath(previousState =>
        isValid ? { [item?.value]: defaultImageUrls } : previousState,
      );
      if (isValid && item?.value) {
        chooseImageUploadTypes({
          value: item?.value,
          image: defaultImageUrls?.remote,
        });
      }
      isMounted.current = true;
    }
  }, [
    defaultImageUrls,
    item?.value,
    chooseImageUploadTypes,
  ]);

  const _image = React.useMemo(() => imagePath?.[item?.value], [
    imagePath,
    item?.value,
  ]);

  return (
    <View style={[styles.container, { width: windowWidth / 4 - 20 }]}>

      {item?.isPlaceholder ? (
        <View />
      ) : (
        <View style={{
          overflow: 'hidden', borderRadius: 4, borderWidth: 1,
          borderColor: colors.darkGreyBorder
        }}>
          <TouchAwareButton
            style={[
              styles.button,
              { width: '100%', height: windowWidth / 4 - 20 },
            ]}
            onPress={handleImageUploadClick}>
            {loading ? (
              <ActivityIndicator />
            ) : _image?.local?.length > 0 || _image?.remote?.length > 0 ? (
              <Image
                style={[
                  styles.imagePlaceHolder,
                  { width: '100%', height: windowWidth / 4 - 20 },
                ]}
                source={{ uri: _image?.image_url || _image?.remote }}
              />
            ) : (
              <>
                <Image
                  style={[
                    styles.imagePlaceHolder,
                    { width: '100%', height: windowWidth / 4 - 20 },
                  ]}
                  source={{ uri: item?.image_url }}
                />
              </>
            )}
          </TouchAwareButton>
        </View>
      )}


      {_image?.remote?.length ? (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteImage(_image?.remote)}>
          <Icon name="cancel_x" size={12} tint={colors.white} />
        </TouchableOpacity>
      ) : null}

      <Text style={[styles.imageLabels, { maxWidth: '100%' }]} numberOfLines={2}>
        {item.title}
      </Text>
      <ImagePickerModal
        visible={pickDialogVisible}
        setVisible={setPickDialogVisible}
        onImageSelected={handleImageUpload}
        name={item.value}
        uploadOptions={uploadOptions}
        canSelectFromGallery={!(uploadOptions == 2 || uploadOptions == 3)}
      />

      <InfoModal
        setVisible={setErrorDialogVisible}
        popUp={{
          state: errorDialogVisible,
          isError: true,
          data: {
            title: locale.UploadFailed,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacings.lg,
  },
  button: {
    paddingVertical: spacings.xl,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: colors.black,
    overflow: 'hidden',
    borderRadius: 15,
  },
  buttonText: {
    color: 'white',
    fontSize: 15,
  },
  imagePlaceHolder: {
    width: '100%',
    resizeMode: 'cover',
    borderRadius: 2,
  },
  iconStyles: {
    padding: spacings.lg,
  },
  imageLabels: {
    marginTop: spacings.sm,
    fontSize: fontSize.md,
    color: colors.darkGrey,
    textAlign: "center"
  },
  required: {
    color: colors.warning,
  },
  deleteButton: {
    position: 'absolute',
    right: 5,
    top: 5,
    padding: 8,
    backgroundColor: colors.black,
    borderRadius: 30,
  },
  iconLabel: {
    color: colors.black,
    fontWeight: 'bold',
    fontSize: fontSize.lg,
  },
});

export default React.memo(LegitAppUploaded);

