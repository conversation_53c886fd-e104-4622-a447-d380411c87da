import React from 'react';
import { StyleSheet, Text, View, FlatList, Dimensions } from 'react-native';
import { useQuery } from '@apollo/client';

import AllCategoriesLoader from '../../../components/Loaders/AllCategoriesLoader';
import { GET_CATEGORIES, GET_LEGIT_APP_BRAND, GET_LEGIT_APP_CATEGORIES } from '../../../apollo/queries';
import colors from '../../../assets/colors';
import { spacings } from '../../../assets/spacing';
import { screens, images } from '../../../assets/strings';
import { fontSize } from '../../../assets/font';
import QueryManager from '../../../components/QueryManager';
import locale from '../../../assets/locale.json';
import RefreshControl from '../../../components/RefreshControl';
import FastImage from '../../../components/FastImage';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import { useInteractionManager } from '../../../tools/hooks';

const LegitAppGuide = ({ route, navigation }) => {
    const guideFromRoute = route?.params?.params;
    const { isInteractionsComplete } = useInteractionManager({
        allowedPlatforms: ['android'],
    });

    React.useEffect(() => {

        console.log("useEffect......", guideFromRoute)
    }, []);

    const handleCategoryClick = React.useCallback(
        item =>
            navigation.push(screens.LEGIT_APP_ALLMODEL, {
                params: item,
                selectedCategory: route?.params?.params,
                selectedBrand: item
            }),
        [navigation.push],
    );

    const renderItem = React.useCallback(
        ({ item }) => (
            <View style={styles.cardContent}>

                <Text
                    style={[
                        styles.titleStyle,
                        { fontFamily: 'Inter-Medium', alignSelf: 'stretch' },
                        ,
                    ]}>
                    {item.title}
                </Text>

                <Text
                    style={[
                        styles.subTitleStyle,
                        { fontFamily: 'Inter-Medium', alignSelf: 'stretch' },

                    ]}>
                    {item.subtitle}
                </Text>

                <FastImage
                    source={
                        item?.image_urls?.length ? { uri: item?.image_urls } : images.logo1
                    }
                    style={styles.imageStyle}
                />

            </View>
        ),
        [handleCategoryClick],
    );

    //const keyExtractor = React.useCallback(item => item.id.toString(), []);

    return (
        <View style={styles.container}>
            <FlatList
                data={guideFromRoute}
                renderItem={renderItem}
                keyExtractor={(item, index) => item.id?.toString() || index.toString()}
                initialNumToRender={5}
                maxToRenderPerBatch={5}
                windowSize={7}
                removeClippedSubviews={true}
                // Make sure these are NOT set:
                pagingEnabled={false}
                snapToInterval={undefined}
                decelerationRate="normal" // smooth scrolling
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.black,
        paddingTop: spacings.lg,
    },
    cardContent: {
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        flexDirection: 'column',
        flex: 1,
        paddingHorizontal: 12,
        // Remove this:
        // height: '100%',
    },

    imageStyle: {
        width: '100%',
        height: undefined,
        aspectRatio: 1.5, // or whatever fits your layout
        resizeMode: 'contain',
        marginTop: 15,
        marginBottom: 25,
        borderRadius: 8, // ✅ Rounded corners
        overflow: 'hidden', // ✅ Ensures content is clipped to the border radius
    },

    titleStyle: {
        fontSize: fontSize.xl,
        color: colors.white,
        fontWeight: 'bold',
        textTransform: 'capitalize',
        textAlign: 'left', // Ensure title is centered
        marginTop: spacings.sm, // Adjust spacing between image and text
    },

    subTitleStyle: {
        fontSize: fontSize.sm,
        color: colors.darkGrey,
        fontWeight: 'bold',
        textTransform: 'capitalize',
        textAlign: 'left', // Ensure title is centered
        marginTop: spacings.sm, // Adjust spacing between image and text
    },
});

export default LegitAppGuide;
