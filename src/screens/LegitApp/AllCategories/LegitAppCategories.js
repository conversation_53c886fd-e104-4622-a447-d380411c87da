import React from 'react';
import { StyleSheet, Text, View, FlatList, Dimensions } from 'react-native';
import { useQuery } from '@apollo/client';

import AllCategoriesLoader from '../../../components/Loaders/AllCategoriesLoader';
import { GET_CATEGORIES, GET_LEGIT_APP_CATEGORIES } from '../../../apollo/queries';
import colors from '../../../assets/colors';
import { spacings } from '../../../assets/spacing';
import { screens, images } from '../../../assets/strings';
import { fontSize } from '../../../assets/font';
import QueryManager from '../../../components/QueryManager';
import locale from '../../../assets/locale.json';
import RefreshControl from '../../../components/RefreshControl';
import FastImage from '../../../components/FastImage';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import { useInteractionManager } from '../../../tools/hooks';

const { width } = Dimensions.get('window');
const WIDTH = width - spacings.sm;
const CARD_WIDTH = WIDTH / 2.2;

const LegitAppCategories = ({ navigation }) => {
    const { isInteractionsComplete } = useInteractionManager({
        allowedPlatforms: ['android'],
    });

    const [refreshing, setRefreshing] = React.useState(false);

    const { data, loading, error, refetch } = useQuery(GET_LEGIT_APP_CATEGORIES);

    const onRefresh = React.useCallback(
        async (enableRefreshControl = true) => {
            try {
                setRefreshing(enableRefreshControl);
                await refetch?.();
            } catch (error) {
            } finally {
                setRefreshing(false);
            }
        },
        [refetch],
    );

    React.useEffect(() => {
        const unsubscribe = navigation.addListener('tabPress', e => {
            try {
                refetch?.();
            } catch (error) { }
        });

        return unsubscribe;
    }, [navigation, refetch]);

    const handleCategoryClick = React.useCallback(
        item =>
            navigation.push(screens.LEGIT_APP_ALLBRAND, {
                params: item,
            }),
        [navigation.push],
    );

    const renderItem = React.useCallback(
        ({ item }) => (
            <TouchAwareButton
                style={styles.card}
                onPress={handleCategoryClick.bind(null, item)}>
                <View style={styles.cardContent}>
                    <FastImage
                        source={
                            item?.icon_image_url?.length ? { uri: item?.icon_image_url } : images.logo2
                        }
                        style={styles.imageStyle}
                        resizeMode="contain"
                    />
                    <Text
                        style={[
                            styles.titleStyle,
                            { fontFamily: 'Inter-Medium', width: CARD_WIDTH - 8 },
                        ]}
                        ellipsizeMode="clip"
                        numberOfLines={2}>
                        {item.title}
                    </Text>
                </View>
            </TouchAwareButton>
        ),
        [handleCategoryClick],
    );

    const keyExtractor = React.useCallback(item => item.id.toString(), []);

    return (
        <View style={styles.container}>
            <QueryManager
                {...{
                    data: !(data == null) && isInteractionsComplete,
                    error,
                    refetch,
                    loading: loading || !isInteractionsComplete,
                }}>
                <QueryManager.Data>
                    <FlatList
                        data={data?.getLegitAppCategories?.data}
                        renderItem={renderItem}
                        numColumns={2}
                        columnWrapperStyle={styles.contentContainer}
                        keyExtractor={keyExtractor}
                        refreshControl={<RefreshControl {...{ onRefresh, refreshing }} />}
                        initialNumToRender={8}
                    />
                </QueryManager.Data>
                <QueryManager.Loading>
                    <AllCategoriesLoader />
                </QueryManager.Loading>
                <QueryManager.Error
                    renderDefaultErrorComponent
                    customErrorText={locale['SomethingWentWrong.']}
                />
            </QueryManager>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.black,
        paddingTop: spacings.lg,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.white,
    },
    cardContent: {
        justifyContent: 'center', // Center vertically
        alignItems: 'center', // Center horizontally
        flexDirection: 'column', // Stack image and text vertically
        flex: 1, // Allow it to take full space
        height: '100%', // Ensure it fills the space within each card
        borderWidth: 1,
        borderRadius: 4,
        borderColor: colors.darkGreyBorder
    },

    imageStyle: {
        height: CARD_WIDTH * 0.2,
        width: CARD_WIDTH * 0.2,
        alignSelf: 'center',
        resizeMode: 'contain',
    },
    titleStyle: {
        fontSize: fontSize.md,
        color: colors.white,
        fontWeight: 'bold',
        paddingHorizontal: spacings.lg,
        textTransform: 'capitalize',
        textAlign: 'center', // Ensure title is centered
        marginTop: spacings.lg, // Adjust spacing between image and text
    },
    contentContainer: {
        justifyContent: 'space-between',
        paddingHorizontal: spacings.lg,
    },
    card: {
        marginBottom: 12,
        backgroundColor: colors.black,
        justifyContent: 'center',
        alignItems: 'center', // Center contents in TouchAwareButton
        width: CARD_WIDTH,
        height: CARD_WIDTH / 1.3, // Ensure card has a fixed height
    },
    errorContainerText: {
        fontSize: fontSize.lg,
        color: colors.black,
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.white,
    },
});

export default LegitAppCategories;
