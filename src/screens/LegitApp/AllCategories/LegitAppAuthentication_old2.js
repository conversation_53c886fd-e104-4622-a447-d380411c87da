import React from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import colors from '../../../assets/colors';

const data = [
  {
    id: '8903089085417217',
    brand: 'Bvlgari',
    title: 'Bracelet Code Checking',
    createdAt: '9 May 2025, 10:52 PM',
    updatedAt: '9 May 2025, 10:52 PM',
    customCode: 'Custom Code 3',
    image: 'https://example.com/image1.jpg',
  },
  {
    id: '8847540531918381',
    brand: 'Bvlgari',
    title: 'Earrings Code Checking',
    createdAt: '9 May 2025, 10:04 PM',
    updatedAt: '9 May 2025, 10:04 PM',
    image: 'https://example.com/image2.jpg',
  },
  // Add more items similarly...
];

const AuthCard = ({ item }) => (
  <View style={styles.card}>
    <Image source={{ uri: item.image }} style={styles.image} />
    <View style={styles.cardContent}>
      <Text style={styles.idText}>#{item.id}</Text>
      <Text style={styles.brand}>{item.brand}</Text>
      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.metaText}>Created on {item.createdAt}</Text>
      <Text style={styles.metaText}>Updated on {item.updatedAt}</Text>
      {item.customCode && (
        <View style={styles.codeBox}>
          <Text style={styles.codeText}>{item.customCode}</Text>
        </View>
      )}
    </View>
    <View style={styles.statusBox}>
      <Text style={styles.statusText}>IN PROGRESS</Text>
      <Text style={styles.timerText}>{'< 12 Hours'}</Text>
    </View>
  </View>
);

export default function LegitAppAuthentication_old() {
  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <AuthCard item={item} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    paddingTop: 20,
    paddingHorizontal: 10,
  },
  
  card: {
    flexDirection: 'row',
    backgroundColor: '#111',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  image: {
    width: 55,
    height: 55,
    borderRadius: 8,
    marginRight: 10,
  },
  cardContent: {
    flex: 1,
  },
  idText: {
    color: colors.darkGreyBorder,
    fontSize: 12,
  },
  brand: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    color: '#fff',
    fontSize: 14,
  },
  metaText: {
    color: '#aaa',
    fontSize: 10,
  },
  codeBox: {
    marginTop: 4,
    backgroundColor: '#007bff',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  codeText: {
    color: '#fff',
    fontSize: 12,
  },
  statusBox: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  statusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  timerText: {
    color: '#fff',
    fontSize: 12,
  },
});
