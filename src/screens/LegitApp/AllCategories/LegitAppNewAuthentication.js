import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, FlatList, Dimensions, ScrollView, TextInput } from 'react-native';
import { useQuery } from '@apollo/client';

import AllCategoriesLoader from '../../../components/Loaders/AllCategoriesLoader';
import { GET_CATEGORIES, GET_LEGIT_APP_BRAND, GET_LEGIT_APP_CATEGORIES, GET_LEGIT_APP_MODELS, GET_LEGIT_AUTHENTICATION_TIME } from '../../../apollo/queries';
import colors from '../../../assets/colors';
import { spacings } from '../../../assets/spacing';
import { screens, images } from '../../../assets/strings';
import { fontSize } from '../../../assets/font';
import QueryManager from '../../../components/QueryManager';
import locale from '../../../assets/locale.json';
import RefreshControl from '../../../components/RefreshControl';
import FastImage from '../../../components/FastImage';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import { useInteractionManager } from '../../../tools/hooks';
import { TouchableOpacity } from 'react-native';
import { formatURL } from '../../../tools/utils';
import Icon from '../../../components/Icon';
import LinearGradient from 'react-native-linear-gradient';
import LegitAppUploaded from './LegitAppUploaded';

const { width } = Dimensions.get('window');
const WIDTH = width - spacings.lg;
const CARD_WIDTH = WIDTH / 2.2;


const LegitAppNewAuthentication = ({ route, navigation }) => {
  const categoryFromRoute = route?.params?.selectedCategory;
  const brandFromRoute = route?.params?.selectedBrand;
  const modelFromRoute = route?.params?.selectedModel;
  const [selectedTimeId, setSelectedTimeId] = React.useState(null);
  const [selectedTimeCost, setSelectedTimeCost] = React.useState(null);
  const [selectedAuthenticationTimeId, setSelectedAuthenticationTimeId] = React.useState(null);
  const [showError, setShowError] = useState(false);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [textInputValue, setTextInputValue] = useState('');
  const [textOwnweInputValue, setTextOwnweInputValue] = useState('');
  const [textCustomInputValue, setTextCustomInputValue] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [requiredImages, setRequiredImages] = useState([]);

  const { isInteractionsComplete } = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const [refreshing, setRefreshing] = React.useState(false);


  const { data, loading, error, refetch } = useQuery(
    GET_LEGIT_AUTHENTICATION_TIME,
    {
      variables: {
        categoryId: categoryFromRoute?.id,
        brandId: brandFromRoute?.id,
        modelId: modelFromRoute.id
      },
    },
  );

  const onRefresh = React.useCallback(

    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  React.useEffect(() => {
    console.log("Kapil....")
    const unsubscribe = navigation.addListener('tabPress', e => {
      try {
        refetch?.();
      } catch (error) { }
    });

    return unsubscribe;
  }, [navigation, refetch]);



  const ProductInfoCard = () => {
    return (
      <View style={styles.prod_container}>
        {/* Logo and Product Image */}
        <View style={styles.prod_topSection}>
          {/* <FastImage
            source={
                          brandFromRoute.icon_image_url
                        }
            style={styles.prod_logo}
            resizeMode="contain"
          />
          <FastImage
            source={
                          modelFromRoute.icon_image_url
                        }
            style={styles.prod_image}
            resizeMode="contain"
          /> */}
        </View>

        {/* Text Content */}
        <Text style={styles.prod_category}>{categoryFromRoute.title}</Text>
        <Text style={styles.prod_brand}>{brandFromRoute.title}</Text>
        <Text style={styles.prod_model}>{modelFromRoute.title}</Text>

        {/* CTA Button */}
        <TouchableOpacity style={styles.prod_button}
          onPress={() => handleVideoLinking()}
        >
          <Icon name="play-circle-outline" size={18} color={colors.yellow} />
          <Text style={styles.prod_buttonText}>Watch How To Authenticate</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const ViewPhotoInstructionsButton = ({ onPress }) => (
    <TouchableOpacity onPress={() => handleGuideLine()} activeOpacity={0.85} style={styles.viewPhot_container}>
      <LinearGradient
        colors={['#FF5F6D', '#FFC371']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.viewPhot_button}
      >
        <View style={styles.viewPhot_content}>
          <Text style={styles.viewPhot_icon}>❓</Text>
          <Text style={styles.viewPhot_text}>VIEW PHOTO INSTRUCTIONS</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );


  const ViewContinueButton = ({ onPress }) => (
    <TouchableOpacity onPress={() => handleContinue()} activeOpacity={0.85} style={styles.viewPhot_container}>
      <LinearGradient
        colors={['#FF5F6D', '#FFC371']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.viewPhot_button}
      >
        <View style={styles.viewPhot_content}>
          <Text style={styles.viewPhot_text}>Continue</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );


  const handleGuideLine = async () => {

    navigation.push(screens.LEGIT_APP_GUIDELINE, {
      params: data?.getLegitAppAuthenticationSet?.data?.guideline
    })
  }
  

  const priceToProductId = {
    3: "L8_token_5",
    5: "L8_token_5_1",
    6: "L8_token_6",
    8: "L8_token_8",
    10: "L8_token_10",
    20: "L8_token_20",
    30: "L8_token_30",
    40: "L8_token_40"
  };
  
  const getProductIdByPrice = (selectedTimeCost) => {
    return priceToProductId.hasOwnProperty(selectedTimeCost)
      ? priceToProductId[selectedTimeCost]
      : null;
  };

  const handleContinue = async () => {



    if (selectedTimeId == null) {
      setShowErrorMessage('Please select Authentication Service')
      setShowError(true);
      return;
    }
    setShowError(false);

    
    

    if (getProductIdByPrice(selectedTimeCost) == null) {
      setShowError(true);
      setShowErrorMessage('Product is not available for this price')
      return;
    }

    setShowError(false);

    console.log("getProductIdByPrice,,,,,,,,,,", getProductIdByPrice(selectedTimeCost))

    const requiredPhotos = data?.getLegitAppAuthenticationSet?.data?.placeholder?.filter(item => item.required);

    const allUploaded = requiredPhotos?.every(item =>
      item?.uploadedImageUrls?.length > 0 // Adjust based on your upload logic
    );

    if (requiredImages.length != requiredPhotos.length) {
      setShowError(true);
      setShowErrorMessage('Please upload all the required photos first')
      return;
    }

    setShowError(false);

    

    const authId2 = "";
    const iapKeyForAuthentication = getProductIdByPrice;
    const authenticationDetail = {
      "iap_price_ios": selectedTimeCost,
      brand_id: brandFromRoute.id,
      category_id: categoryFromRoute.id,
      model_id: modelFromRoute.id,
      category_name: categoryFromRoute.title,
      brand_name: brandFromRoute.title,
      model_name: modelFromRoute.title,
      authentication_set_id: selectedTimeId,
      turnaround_time_id: selectedAuthenticationTimeId,
      image_urls: requiredImages,
      user_remark: textInputValue?.trim() ? textInputValue : 'No additional remarks provided',
      user_custom_code: textCustomInputValue?.trim() ? textCustomInputValue : '',
      category: categoryFromRoute,
      brand: brandFromRoute,
      modal: modelFromRoute,
    };

    navigation.navigate(screens.LEGITAPPCHECKOUT, {
      authId2,
      iapKeyForAuthentication,
      authenticationDetail,
    });


  }

  const handleCertificateLinking = async () => {
    const url = formatURL("https://legitapp.com/cert/6809939146413702");

    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.error('Cannot open URL:', url);
      }
    } catch (err) {
      console.error('Error opening URL:', err);
    }
  };
  const handleTurnAroundTimeClick = React.useCallback(item => {
    setSelectedTimeId(item.id);
    setSelectedTimeCost(parseInt(item.credit, 10))
    setSelectedAuthenticationTimeId(data?.getLegitAppAuthenticationSet?.data.id);
    console.log("data?.getLegitAppAuthenticationSet?.data.id", data?.getLegitAppAuthenticationSet?.data.id)


  }, []);


  const handleVideoLinking = async () => {
    const url = formatURL("https://www.youtube.com/watch?v=9iwaMoXQGQw");

    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.error('Cannot open URL:', url);
      }
    } catch (err) {
      console.error('Error opening URL:', err);
    }
  };





  const renderTurnAroundTimeList = () => {
    return data?.getLegitAppAuthenticationSet?.data?.turn_around_time_list?.map((item, index) => {
      const isLeft = index % 2 === 0;
      return (
        <View
          key={item.id}
          style={[
            styles.gridItem,
            isLeft ? { marginRight: 0 } : null,
          ]}
        >
          <TouchAwareButton
            style={[
              styles.timeCard,
              selectedTimeId === item.id && styles.selectedTimeCard,
            ]}
            onPress={() => handleTurnAroundTimeClick(item)}
          >
            <TouchAwareButton.View style={styles.imageWrapper}>
              <Text
                style={[
                  styles.timeTitle,
                  selectedTimeId === item.id && styles.selectedTimeTitle,
                ]}
              >
                {item.title}
              </Text>
              <Text
                style={[
                  styles.timeSub,
                  { marginTop: 10 },
                  selectedTimeId === item.id && styles.selectedTimeSub,
                ]}
              >
                ${parseInt(item.credit, 10)}
              </Text>
            </TouchAwareButton.View>
          </TouchAwareButton>
        </View>
      );
    });
  };


  const YesNoButton = () => {
    const [selected, setSelected] = useState("Yes");

    return (
      <View style={styles.yesNoContainer}>
        <TouchableOpacity
          style={[
            styles.yesNoButton,
            selected === "Yes"
              ? styles.yesNoSelectedButton
              : styles.yesNoUnselectedButton,
          ]}
          onPress={() => setSelected("Yes")}
        >
          <Text
            style={[
              styles.yesNoText,
              selected === "Yes"
                ? styles.yesNoSelectedText
                : styles.yesNoUnselectedText,
            ]}
          >
            Yes
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.yesNoButton,
            selected === "No"
              ? styles.yesNoSelectedButton
              : styles.yesNoUnselectedButton,
          ]}
          onPress={() => setSelected("No")}
        >
          <Text
            style={[
              styles.yesNoText,
              selected === "No"
                ? styles.yesNoSelectedText
                : styles.yesNoUnselectedText,
            ]}
          >
            No
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const SuggestionTags = () => {
    const tags = [
      "No Insole",
      "No Tag",
      "No Package",
      "No Box Cover",
      "Used",
      "Non-original",
    ];
    const toggleTag = (tag) => {
      setSelectedTags((prevSelected) =>
        prevSelected.includes(tag)
          ? prevSelected.filter((t) => t !== tag)
          : [...prevSelected, tag]
      );
    };

    React.useEffect(() => {
      setTextInputValue(selectedTags.map(tag => `- ${tag}`).join('\n'));
    }, [selectedTags]);


    return (
      <View style={styles.suggestionContainer}>
        {tags.map((tag) => {
          const isSelected = selectedTags.includes(tag);
          return (
            <TouchableOpacity
              key={tag}
              style={[
                styles.suggestionTag,
                isSelected && styles.selectedSuggestionTag,
              ]}
              onPress={() => toggleTag(tag)}
            >
              <Text
                style={[
                  styles.suggestionTagText,
                  isSelected && styles.selectedSuggestionTagText,
                ]}
              >
                {tag}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  function formatData(data, numColumns) {
    if (!Array.isArray(data)) {
      //console.warn("formatData expected an array but got:", data);
      return [];
    }

    const formattedData = [...data]; // safe now
    const numberOfFullRows = Math.floor(formattedData.length / numColumns);
    let numberOfElementsLastRow = formattedData.length - numberOfFullRows * numColumns;

    let index = 1001;
    while (numberOfElementsLastRow !== 0 && numberOfElementsLastRow < numColumns) {
      formattedData.push({
        id: index,
        key: `blank-${numberOfElementsLastRow}`,
        isPlaceholder: true,
      });
      numberOfElementsLastRow++;
      index++;
    }

    return formattedData;
  }

  const keyExtractor = React.useCallback(item => item.id.toString(), []);

  return (

    <View style={styles.viewContainer}>
      <QueryManager
        {...{
          data: !(data == null) && isInteractionsComplete,
          error,
          refetch,
          loading: loading || !isInteractionsComplete,
        }}>
        <QueryManager.Data>
          <ScrollView contentContainerStyle={styles.container}>

            <ProductInfoCard />
            <Text
              style={[
                styles.titleStyle,
                { fontFamily: 'Inter-Medium', marginBottom: 20 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              Authentication Service
            </Text>

            {/* Turn Around Times */}

            <View style={styles.gridWrapper}>{/* Mapped items here */}
              {renderTurnAroundTimeList()}

            </View>

            <Text
              style={[
                styles.titleStyle,
                { fontFamily: 'Inter-Medium', marginBottom: 8 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              LEGIT APP Certificate
            </Text>

            <Text
              style={[
                styles.smallTitleStyle,
                { fontFamily: 'Inter-Medium' },
              ]}
              ellipsizeMode="clip">
              We issue a certificate of authencity for every case we gave the authenication result. Augment your case for a refund or have the undeniable. rock-solid proof on your item's authenticity status.
            </Text>

            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 20, marginBottom: 8 }}>
              <Text
                style={[
                  styles.titleStyle,
                  { fontFamily: 'Inter-Medium', marginRight: 8 },
                ]}
                ellipsizeMode="clip"
                numberOfLines={2}
              >
                Certificate Owner
              </Text>

              <Text
                style={[
                  styles.smallTitleStyle,
                  { fontFamily: 'Inter-Medium' },
                ]}
                ellipsizeMode="clip"
                numberOfLines={2}
              >
                (Optional)
              </Text>
            </View>


            <Text
              style={[
                styles.smallTitleStyle,
                { fontFamily: 'Inter-Medium' },
              ]}
              ellipsizeMode="clip">
              Please enter your legal business or legal business entity name to be eligible for the Finanical Guarantee policy.
            </Text>

            <TextInput
              // onChangeText={setFirstName}
              style={styles.inputField}
              placeholder="Owner Name to be Shown on Certificate"
              placeholderTextColor={colors.darkGreyBorder}
              selectionColor={colors.white}
              value={textOwnweInputValue}
              onChangeText={setTextOwnweInputValue}

            />

            <TouchableOpacity onPress={() => handleCertificateLinking()}>
              <Text style={styles.linkButtonText}>
                View Certificate Example
              </Text>
            </TouchableOpacity>

            <ViewPhotoInstructionsButton />

            {categoryFromRoute.hasBox && (
              <>
                <Text
                  style={[
                    styles.titleStyle,
                    { fontFamily: 'Inter-Medium', marginTop: 20 },
                  ]}
                  ellipsizeMode="clip"
                  numberOfLines={2}>
                  Do you have the box?
                </Text>

                <YesNoButton />
              </>
            )}


            {/* Guideline */}
            {/* {authentication.guideline && (
          <View style={styles.guideline}>
            <Text style={styles.sectionTitle}>{authentication.guideline.title}</Text>
            <Text style={styles.guidelineSub}>{authentication.guideline.subtitle}</Text>
            {authentication.guideline.image_urls?.map((uri, index) => (
              <FastImage key={index} source={{ uri }} style={styles.guidelineImage} />
            ))}
          </View>
        )} */}

            {/* Required Photos */}
            <Text
              style={[
                styles.titleStyle,
                { fontFamily: 'Inter-Medium', marginBottom: 20, marginTop: 20 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              Required Photos
            </Text>

            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
              {formatData(
                data?.getLegitAppAuthenticationSet?.data?.placeholder?.filter(item => item.required) || [],
                4
              ).map((item, index) => (
                <LegitAppUploaded
                  uploadOptions={'authenticationType'}
                  key={item?.title}
                  item={item}
                  required={true}
                  setRequiredImages={setRequiredImages}
                />
              ))}
            </View>

            <Text
              style={[
                styles.titleStyle,
                { fontFamily: 'Inter-Medium', marginBottom: 20, marginTop: 20 },
              ]}
              ellipsizeMode="clip"
              numberOfLines={2}>
              Optional Photos
            </Text>

            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
              {formatData(
                data?.getLegitAppAuthenticationSet?.data?.placeholder?.filter(item => !item.required) || [],
                4
              ).map((item, index) => (
                <LegitAppUploaded
                  uploadOptions={'authenticationType'}
                  key={item?.title}
                  item={item}
                  required={false}
                />
              ))}
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 20, marginBottom: 8 }}>
              <Text
                style={[
                  styles.titleStyle,
                  { fontFamily: 'Inter-Medium', marginRight: 8 },
                ]}
                ellipsizeMode="clip"
                numberOfLines={2}
              >
                Case Custom Code
              </Text>

              <Text
                style={[
                  styles.smallTitleStyle,
                  { fontFamily: 'Inter-Medium' },
                ]}
                ellipsizeMode="clip"
                numberOfLines={2}
              >
                (Optional)
              </Text>
            </View>


            <Text
              style={[
                styles.smallTitleStyle,
                { fontFamily: 'Inter-Medium' },
              ]}
              ellipsizeMode="clip">
              You can input your own unique order number for better tracing in case history.
            </Text>

            <TextInput
              // onChangeText={setFirstName}
              style={styles.inputField}
              placeholder="Enter case custom code"
              placeholderTextColor={colors.darkGreyBorder}
              selectionColor={colors.white}
              value={textCustomInputValue}
              keyboardType="numeric"
              onChangeText={setTextCustomInputValue}
            />

            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 20, marginBottom: 8 }}>
              <Text
                style={[
                  styles.titleStyle,
                  { fontFamily: 'Inter-Medium', marginRight: 8 },
                ]}
                ellipsizeMode="clip"
                numberOfLines={2}
              >
                Note to our Authenticators
              </Text>

              <Text
                style={[
                  styles.smallTitleStyle,
                  { fontFamily: 'Inter-Medium' },
                ]}
                ellipsizeMode="clip"
                numberOfLines={2}
              >
                (Optional)
              </Text>
            </View>

            <TextInput
              style={[
                styles.inputField,
                {
                  height: 120,           // Adjust height for a textarea feel
                  fontSize: 16,
                  textAlignVertical: 'top', // Important for multiline alignment
                  padding: 16,
                },
              ]}
              placeholder="Tell us more about your item e.g. condition, model code, purchase date."
              placeholderTextColor={colors.darkGreyBorder}
              selectionColor={colors.white}
              multiline={true}
              numberOfLines={5}
              value={textInputValue}
              onChangeText={setTextInputValue}
            />


            {/* {categoryFromRoute.tags && categoryFromRoute.tags.length > 0 && !categoryFromRoute.tags.includes("No Tag") && ( */}
            {categoryFromRoute.id == 1 && (
              <>
                <SuggestionTags />
              </>
            )}




            <View style={styles.nextButtonWrapper}>
              {showError ? <Text style={styles.errorText}>{showErrorMessage}</Text> : null}
              <ViewContinueButton />
            </View>




            {/* <FlatList
          data={data?.getLegitAppAuthenticationSet?.data?.placeholder}
          keyExtractor={item => item.title}
          renderItem={({ item }) => (
            <View style={styles.photoCard}>
              <FastImage source={{ uri: item.image_url }} style={styles.photo} />
              <Text style={styles.photoTitle}>{item.title}</Text>
              {item.description ? (
                <Text style={styles.photoDesc}>{item.description}</Text>
              ) : null}
            </View>
          )}
          numColumns={2}
          contentContainerStyle={styles.photoGrid}
        /> */}
          </ScrollView>
        </QueryManager.Data>
        <QueryManager.Loading>
          <AllCategoriesLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale['SomethingWentWrong.']}
        />
      </QueryManager>
    </View>


  );
};


const styles = StyleSheet.create({
  viewContainer: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: spacings.lg,
  },

  container: {
    padding: spacings.lg,
    backgroundColor: colors.black,
  },

  titleStyle: {
    fontSize: fontSize.xl,
    color: colors.white,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },

  smallTitleStyle: {
    fontSize: fontSize.md,
    color: colors.darkGreyBorder,
    textTransform: 'capitalize',
    marginBottom: 0
  },

  timeCard: {
    marginBottom: spacings.lg,
    backgroundColor: colors.black,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.darkGreyBorder,
    marginBottom: 16

  },

  selectedTimeCard: {
    marginBottom: spacings.lg,
    backgroundColor: colors.black,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.red,
    marginBottom: 16
  },


  gridWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 10
  },

  gridItem: {
    width: '47%', // Two cards with margin between them
    marginBottom: 5,
  },

  imageWrapper: {
    height: CARD_WIDTH / 1.8,
    width: CARD_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
  },


  timeTitle: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.darkGreyBorder,
  },

  selectedTimeTitle: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.white,
  },

  timeSub: {
    fontSize: fontSize.md,
    fontWeight: 'bold',
    color: colors.darkGreyBorder,
  },

  selectedTimeSub: {
    fontSize: fontSize.md,
    fontWeight: 'bold',
    color: colors.white,
  },

  inputField: {
    height: 44,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.darkGreyBorder,
    paddingHorizontal: 12,
    color: colors.white, // Text color
    backgroundColor: colors.black, // To contrast against black bg
    marginTop: 8,
    marginBottom: 20,
  },

  linkButtonText: {
    color: colors.darkGreyBorder,
    textAlign: 'center',
    textDecorationLine: 'underline',
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    marginTop: 0,
  },

  guideline: {
    marginTop: spacings.md,
  },
  guidelineSub: {
    fontSize: fontSize.sm,
    marginBottom: spacings.sm,
  },
  guidelineImage: {
    height: 200,
    width: '100%',
    marginVertical: spacings.sm,
    borderRadius: 10,
  },
  photoGrid: {
    gap: spacings.md,
  },
  photoCard: {
    flex: 1,
    margin: spacings.sm / 2,
    padding: spacings.sm,
    backgroundColor: colors.lightGrey,
    borderRadius: 10,
    alignItems: 'center',
  },
  photo: {
    width: '100%',
    height: 150,
    borderRadius: 6,
    marginBottom: spacings.sm,
  },
  photoTitle: {
    fontSize: fontSize.md,
    fontWeight: '600',
  },
  photoDesc: {
    fontSize: fontSize.sm,
    color: colors.darkGreyBorder,
    textAlign: 'center',
    marginTop: spacings.xs,
  },

  prod_container: {
    backgroundColor: "#18191E",
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  prod_topSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 40,
  },
  prod_logo: {
    width: 80,
    height: 60,
  },
  prod_image: {
    width: 120,
    height: 90,
  },
  prod_category: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.darkGrey,
    marginBottom: 4,
  },
  prod_brand: {
    fontSize: fontSize.xl,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  prod_model: {
    fontSize: fontSize.xl,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 10,
  },
  prod_button: {
    borderColor: colors.white,
    borderWidth: 1,
    paddingVertical: 6,
    paddingHorizontal: 8,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  prod_buttonText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: fontSize.sm,
    fontWeight: 'bold',
  },

  viewPhot_container: {
    marginTop: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  viewPhot_button: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  viewPhot_content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewPhot_icon: {
    color: '#fff',
    fontSize: 18,
    marginRight: 8,
  },
  viewPhot_text: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  errorText: {
    fontSize: fontSize.sm,
    color: colors.warning,
    margin: spacings.sm,
  },

  yesNoContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 10,
    borderWidth: 1,
    borderColor: colors.darkGreyBorder,
    borderRadius: 4,
    overflow: "hidden", // Add this line
    marginBottom: 10,
  },

  yesNoButton: {
    flex: 1,
    margin: 0,
    paddingVertical: 15,
    alignItems: "center",
    overflow: 'hidden'
  },
  yesNoSelectedButton: {
    backgroundColor: "#FF4444", // Selected color (red)
  },
  yesNoUnselectedButton: {
    backgroundColor: "#F0F0F0", // Unselected color (light gray)
  },
  yesNoText: {
    fontSize: 16,
    fontWeight: "bold",
  },
  yesNoSelectedText: {
    color: "#FFFFFF", // White text for selected
  },
  yesNoUnselectedText: {
    color: "#000000", // Black text for unselected
  },

  suggestionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginVertical: 10,
  },
  suggestionTag: {
    backgroundColor: colors.black,
    borderColor: colors.darkGreyBorder,
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: 6,
    paddingHorizontal: 12,
    margin: 5,
  },
  selectedSuggestionTag: {
    backgroundColor: '#555', // or use your primary color
  },
  suggestionTagText: {
    color: colors.darkGreyBorder,
    fontWeight: "500",
  },
  selectedSuggestionTagText: {
    color: '#fff',
  }
});

export default LegitAppNewAuthentication;
