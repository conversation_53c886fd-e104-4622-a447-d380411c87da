import React, { useEffect, useState } from 'react';
import { View, Text, Image, FlatList, StyleSheet, ScrollView, Modal, TouchableOpacity, Dimensions, Platform } from 'react-native';
import Icon from '../../components/Icon';
import { icons, images, screens } from '../../assets/strings';
import colors from '../../assets/colors';
import FastImage from '../../components/FastImage';
import ImageViewerModal from './ImageViewerModal';
import ActivityIndicator from '../../components/ActivityIndicator';
import RNFS from 'react-native-fs';
import { useCredential } from '../../tools/hooks/useUser';

//import { FontAwesome } from '@expo/vector-icons'; // For checkmark and cross icons

/*const authenticationData = [
  {
    type: 'Front',
    images: [
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
    ],
  },
  {
    type: 'Back',
    images: [
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
    ],
  },
  {
    type: 'Tag',
    images: [
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
    ],
  },
  // Add other authentication types...
];*/


const AiResultNewForTagScan = ({ route, navigation }) => {

  const credential = useCredential()
  const isExpert = React.useMemo(() => credential?.role_id == 3, [
    credential?.role_id,
  ]);


  // Define dynamic labels for each image index
  const { front: initialFront, frontCloseUp: initialFrontCloseUp, frontTag: initialFrontTag,
    back: initialBack, backCloseup: initialBackCloseup, backTag: initialBackTag,
    copyright: initialCopyright, stitichingSleeve: initialStitichingSleeve,
    stitichingHem: initialStitichingHem, url, predication, predictionData, keysData, tShirtTag, tShirtTagName } = route.params;
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedPassed, setSelectedPassed] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState([]);
  const [authenticationData, setAuthenticationData] = useState([]);

  const [front, setfront] = useState(initialFront); // Initialize frontCloseUp with the value from route.params
  const [frontCloseUp, setFrontCloseUp] = useState(initialFrontCloseUp); // Initialize frontCloseUp with the value from route.params
  const [frontTag, setFrontTag] = useState(initialFrontTag); // Initialize frontCloseUp with the value from route.params

  const [back, setBack] = useState(initialBack); // Initialize frontCloseUp with the value from route.params
  const [backCloseup, setBackCloseup] = useState(initialBackCloseup); // Initialize frontCloseUp with the value from route.params
  const [backTag, setBackTag] = useState(initialBackTag); // Initialize frontCloseUp with the value from route.params

  const [copyright, setCopyright] = useState(initialCopyright); // Initialize frontCloseUp with the value from route.params
  const [stitichingSleeve, setStitichingSleeve] = useState(initialStitichingSleeve); // Initialize frontCloseUp with the value from route.params
  const [stitichingHem, setStitichingHem] = useState(initialStitichingHem); // Initialize frontCloseUp with the value from route.params


  const [triggerApiCall, setTriggerApiCall] = useState(false); // Flag to control when the API call should happen


  // First useEffect: Update authenticationData when component mounts or when the dependencies change
  useEffect(() => {
    const updatedData = [];

    const imageTypes = [
      { type: 'Front', images: front },
      { type: 'Front Closeup', images: frontCloseUp },
      { type: 'Front Tag', images: frontTag },
      { type: 'Back', images: back },
      { type: 'Back Closeup', images: backCloseup },
      { type: 'Back Tag', images: backTag },
      { type: 'Copyright', images: copyright },
      { type: 'Stitching Sleeve', images: stitichingSleeve },
      { type: 'Stitching Hem', images: stitichingHem },
    ];

    imageTypes.forEach(({ type, images }) => {
      if (images && images.images.length > 0) {
        if (tShirtTag) {
          updatedData.push({ type, front: images.images[0], images: images.images.slice(1) });
        } else {
          updatedData.push({ type, front: images.images[0], images: images.images });
        }


      }
    });

    setAuthenticationData(updatedData); // Set the updated data
    if (!triggerApiCall) {
      setTriggerApiCall(true); // Set this flag to true when the data is ready  
    }

  }, [
    front,
    frontCloseUp,
    frontTag,
    back,
    backCloseup,
    backTag,
    copyright,
    stitichingSleeve,
    stitichingHem,
  ]);


  useEffect(() => {

    fetchProbabilitData()
    if (authenticationData.length > 0) {
      if (front && front.images.length > 0) {
        //fetchFrontData(0)        
      }

      if (frontCloseUp && frontCloseUp.images.length > 0) {
        //fetchFrontData(1)  
      }

      if (frontTag && frontTag.images.length > 0) {
        //fetchFrontData(2)
      }

      if (back && back.images.length > 0) {
        //fetchFrontData(3)
      }

      if (backCloseup && backCloseup.images.length > 0) {
        //fetchFrontData(4)
      }

      if (backTag && backTag.images.length > 0) {
        //fetchFrontData(5)
      }

      if (copyright && copyright.images.length > 0) {
        //fetchFrontData(6)
      }

      if (stitichingSleeve && stitichingSleeve.images.length > 0) {
        //fetchFrontData(7)
      }

      if (stitichingHem && stitichingHem.images.length > 0) {
        //fetchFrontData(8)
      }
    }

  }, [triggerApiCall]); // Empty dependency array ensures this runs only once when the component mounts

  function fetchProbabilitData() {


    const updatedFrontData = [];
    const updatedFrontTagData = [];
    const updatedFrontCloseUpData = [];

    const updatedBackData = [];
    const updatedBackTagData = [];
    const updatedBackCloseUpData = [];

    const updatedCopyrightData = [];
    const updatedStitichingSleeveData = [];
    const updatedStitichingHemData = [];

    console.log("front====>", front.images.length)
    console.log("frontTag====>", frontTag.images.length)
    console.log("frontCloseUp====>", frontCloseUp.images.length)
    console.log("back====>", back.images.length)
    console.log("backTag====>", backTag.images.length)
    console.log("backCloseup====>", backCloseup.images.length)
    console.log("copyright====>", copyright.images.length)
    console.log("stitichingSleeve====>", stitichingSleeve.images.length)
    console.log("stitichingHem====>", stitichingHem.images.length)
    //for (const [index, probability] of probabilityData.entries()) {
    for (const [index, predication] of predictionData.entries()) {
      //if(probability >= 90) {

      if (predication == 1) {
        if (index < front.images.length) {
          updatedFrontData.push({ type: front.images[index].type, uri: front.images[index].uri, passed: 1 });
        }

        if (index < frontTag.images.length) {
          updatedFrontTagData.push({ type: frontTag.images[index].type, uri: frontTag.images[index].uri, passed: 1 });
        }

        if (index < frontCloseUp.images.length) {
          updatedFrontCloseUpData.push({ type: frontCloseUp.images[index].type, uri: frontCloseUp.images[index].uri, passed: 1 });
        }

        if (index < back.images.length) {
          updatedBackData.push({ type: back.images[index].type, uri: back.images[index].uri, passed: 1 });
        }

        if (index < backTag.images.length) {
          updatedBackTagData.push({ type: backTag.images[index].type, uri: backTag.images[index].uri, passed: 1 });
        }

        if (index < backCloseup.images.length) {
          updatedBackCloseUpData.push({ type: backCloseup.images[index].type, uri: backCloseup.images[index].uri, passed: 1 });
        }

        if (index < copyright.images.length) {
          updatedCopyrightData.push({ type: copyright.images[index].type, uri: copyright.images[index].uri, passed: 1 });
        }

        if (index < stitichingSleeve.images.length) {
          updatedStitichingSleeveData.push({ type: stitichingSleeve.images[index].type, uri: stitichingSleeve.images[index].uri, passed: 1 });
        }

        if (index < stitichingHem.images.length) {
          updatedStitichingHemData.push({ type: stitichingHem.images[index].type, uri: stitichingHem.images[index].uri, passed: 1 });
        }

      } else {
        if (index < front.images.length) {
          updatedFrontData.push({ type: front.images[index].type, uri: front.images[index].uri, passed: 0 });
        }

        if (index < frontTag.images.length) {
          updatedFrontTagData.push({ type: frontTag.images[index].type, uri: frontTag.images[index].uri, passed: 0 });
        }

        if (index < frontCloseUp.images.length) {
          updatedFrontCloseUpData.push({ type: frontCloseUp.images[index].type, uri: frontCloseUp.images[index].uri, passed: 0 });
        }

        if (index < back.images.length) {
          updatedBackData.push({ type: back.images[index].type, uri: back.images[index].uri, passed: 0 });
        }

        if (index < backTag.images.length) {
          updatedBackTagData.push({ type: backTag.images[index].type, uri: backTag.images[index].uri, passed: 0 });
        }

        if (index < backCloseup.images.length) {
          updatedBackCloseUpData.push({ type: backCloseup.images[index].type, uri: backCloseup.images[index].uri, passed: 0 });
        }

        if (index < copyright.images.length) {
          updatedCopyrightData.push({ type: copyright.images[index].type, uri: copyright.images[index].uri, passed: 0 });
        }

        if (index < stitichingSleeve.images.length) {
          updatedStitichingSleeveData.push({ type: stitichingSleeve.images[index].type, uri: stitichingSleeve.images[index].uri, passed: 0 });
        }

        if (index < stitichingHem.images.length) {
          updatedStitichingHemData.push({ type: stitichingHem.images[index].type, uri: stitichingHem.images[index].uri, passed: 0 });
        }
      }


      // const [frontCloseUp, setFrontCloseUp] = useState(initialFrontCloseUp); // Initialize frontCloseUp with the value from route.params

      // const [back, setBack] = useState(initialBack); // Initialize frontCloseUp with the value from route.params
      // const [backCloseup, setBackCloseup] = useState(initialBackCloseup); // Initialize frontCloseUp with the value from route.params
      // const [backTag, setBackTag] = useState(initialBackTag); // Initialize frontCloseUp with the value from route.params

      // const [copyright, setCopyright] = useState(initialCopyright); // Initialize frontCloseUp with the value from route.params
      // const [stitichingSleeve, setStitichingSleeve] = useState(initialStitichingSleeve); // Initialize frontCloseUp with the value from route.params
      // const [stitichingHem, setStitichingHem] = useState(initialStitichingHem); // Initialize frontCloseUp with the value from route.params

    }

    if (updatedFrontData.length > 0) {
      setfront({ "images": updatedFrontData });
    }

    if (updatedFrontTagData.length > 0) {
      setFrontTag({ "images": updatedFrontTagData });
    }

    if (updatedFrontCloseUpData.length > 0) {
      setFrontCloseUp({ "images": updatedFrontCloseUpData });
    }

    if (updatedBackData.length > 0) {
      setBack({ "images": updatedBackData });
    }

    if (updatedBackTagData.length > 0) {
      setBackTag({ "images": updatedBackTagData });
    }

    if (updatedBackCloseUpData.length > 0) {
      setBackCloseup({ "images": updatedBackCloseUpData });
    }

    if (updatedCopyrightData.length > 0) {
      setCopyright({ "images": updatedCopyrightData });
    }

    if (updatedStitichingSleeveData.length > 0) {
      setStitichingSleeve({ "images": updatedStitichingSleeveData });
    }

    if (updatedStitichingHemData.length > 0) {
      setStitichingHem({ "images": updatedStitichingHemData });
    }

  }

  const fetchData = async (imagesList) => {

    const localPaths = await Promise.all(imagesList.map((url, index) => {
      const filename = `image_${index}.jpg`; // Use a unique filename          
      return downloadImage(url.uri, filename);
    }));


    const formData = new FormData();
    for (const imagePath of localPaths) {

      const fileNew = {
        uri: imagePath,
        type: 'image/jpeg',
        name: imagePath.split('/').pop(),
      };
      formData.append('images', fileNew);
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      const result = await response.json();
      console.log("result=====", result)
      return result;
    } catch (error) {
      console.error('appraisalURL Error uploading imagesssssss:', appraisalURL);
      return null;
    }
  }

  const fetchFrontData = async (runningIndex) => {

    console.log("Calling ", runningIndex)
    fetchData(front.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: front.images[index].type, uri: front.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: front.images[index].type, uri: front.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }
          if (runningIndex == 0) {
            setfront({ "images": updatedData });
            fetchFrontCloseData(1)
          }
        }
      }
    });
  }

  const fetchFrontCloseData = async (runningIndex) => {

    fetchData(frontCloseUp.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: frontCloseUp.images[index].type, uri: frontCloseUp.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: frontCloseUp.images[index].type, uri: frontCloseUp.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 1) {
            setFrontCloseUp({ "images": updatedData });
            fetchTagData(2)
          }
        }

      }
    });
  }

  const fetchTagData = async (runningIndex) => {

    fetchData(frontTag.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: frontTag.images[index].type, uri: frontTag.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: frontTag.images[index].type, uri: frontTag.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 2) {
            setFrontTag({ "images": updatedData });
            fetchBackData(3)
          }
        }

      }
    });
  }

  const fetchBackData = async (runningIndex) => {

    fetchData(back.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: back.images[index].type, uri: back.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: back.images[index].type, uri: back.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 3) {
            setBack({ "images": updatedData });
            fetchBackCloseData(4)
          }
        }

      }
    });
  }

  const fetchBackCloseData = async (runningIndex) => {

    fetchData(backCloseup.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: backCloseup.images[index].type, uri: backCloseup.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: backCloseup.images[index].type, uri: backCloseup.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 4) {
            setBackCloseup({ "images": updatedData });
            fetchBackTagData(5)
          }
        }

      }
    });
  }

  const fetchBackTagData = async (runningIndex) => {

    fetchData(backTag.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: backTag.images[index].type, uri: backTag.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: backTag.images[index].type, uri: backTag.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 5) {
            setBackTag({ "images": updatedData });
            fetchCopyrightData(6)
          }
        }

      }
    });
  }

  const fetchCopyrightData = async (runningIndex) => {

    fetchData(copyright.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: copyright.images[index].type, uri: copyright.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: copyright.images[index].type, uri: copyright.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 6) {
            setCopyright({ "images": updatedData });
            fetchStitichingSleeveData(7)
          }
        }

      }
    });
  }

  const fetchStitichingSleeveData = async (runningIndex) => {

    fetchData(stitichingSleeve.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: stitichingSleeve.images[index].type, uri: stitichingSleeve.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: stitichingSleeve.images[index].type, uri: stitichingSleeve.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 7) {
            setStitichingSleeve({ "images": updatedData });
            fetchStitichingHemData(8)
          }
        }

      }
    });
  }

  const fetchStitichingHemData = async (runningIndex) => {

    fetchData(stitichingHem.images).then((result) => {
      if (result) {
        if (result.predictions.length > 0) {

          let fakeCount = 0;
          let realCount = 0;
          const updatedData = [];
          for (const [index, results] of result.predictions.entries()) {
            if (results.prediction == 'real') {
              updatedData.push({ type: stitichingHem.images[index].type, uri: stitichingHem.images[index].uri, passed: 1 });
              realCount = realCount + 1;
              console.log("This is real=========>");
            } else {
              updatedData.push({ type: stitichingHem.images[index].type, uri: stitichingHem.images[index].uri, passed: 0 });
              fakeCount = fakeCount + 1;
              console.log("This is fakse=========>");
            }

          }

          let overallResult = "Overall Prediction is: Fake";
          if (realCount > fakeCount) {
            overallResult = "Overall Prediction is: Pass";
          }

          if (runningIndex == 8) {
            setStitichingHem({ "images": updatedData });
          }
        }

      }
    });
  }



  // Helper function to get a Blob from a remote image URL
  async function downloadImage(imageUrl, filename) {
    const isIOS = Platform.OS == 'ios';
    const downloadDest = isIOS ? `${RNFS.DocumentDirectoryPath}/${filename}`
      : `file://${RNFS.DocumentDirectoryPath}/${filename}`;

    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]); // Remove the data URL prefix
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      await RNFS.writeFile(downloadDest, base64, 'base64');
      return downloadDest;
    } catch (error) {
      console.error('Error downloading image:', error);
      throw error;
    }
  }
  const handleRedriect = (index, authItems) => {
    if (isExpert || tShirtTag) {
      navigation.push(screens.SINGLE_LISTING, { key: keysData[index] })
    }
  }

  const handleImagePress = (index, authItems) => {

    const images = [];
    authItems.images.forEach((item, index) => {
      images.push({ uri: item.uri, passed: item.passed });
    })

    console.log("Kapil============", images)
    setSelectedPassed(authItems.images[index].passed);
    setCurrentImageIndex(index);
    setSelectedImage(images);
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedImage(null);
  };

  return (
    <View style={styles.container}>
      {authenticationData.map((authItem, mainScroolIndex) => (
        <View key={mainScroolIndex} style={styles.authBox}>
          {tShirtTag && mainScroolIndex === 0 && tShirtTagName && tShirtTag?.[0] && (
            <Text style={styles.authTitle}>
              {tShirtTagName}
            </Text>
          )}
  
  
          
            <Text style={styles.authTitle}>
               {tShirtTag[0] == null
      ? "No results for this Tag"
      : "Estimated Tag Year: " + tShirtTag[0].split("-")[0]
    }
            </Text>
          
  
          <View style={[styles.largeImage, { backgroundColor: '#f1f1f1' }]}>
            <FastImage
              source={
                authItem.front
                  ? authItem.front
                  : images.PLACEHOLDER
              }
              style={styles.image}
            />

<Text style={[styles.authTitle, {marginTop: 10, fontSize: 10}]}>
To determine if this Tag is Real or Fake, submit the shirt for one of our authentication options
            </Text>
          </View>
        </View>
      ))}
  
      <ImageViewerModal
        isVisible={modalVisible}
        images={selectedImage.map((img) => ({ url: img.uri }))}
        imageStatus={selectedImage}
        onClose={() => setModalVisible(false)}
        initialIndex={currentImageIndex}
        isPassed={predication}
        keysData={keysData}
        navigation={navigation}
        tag={tShirtTagName}
        tagYears={tShirtTag}
      />
    </View>
  );
  
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center', // Center vertically
    alignItems: 'center', // Center horizontally
    padding: 10,
  },

  authBox: {
    width: '90%', // Adjust width to avoid overflow
    backgroundColor: '#f1f1f1',
    padding: 10,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center', // Center content horizontally
    justifyContent: 'center', // Center content vertically
  },

  authTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  
  largeImage: {
    width: Dimensions.get('window').width * 0.7, // 70% of screen width
    height: 330, 
    borderRadius: 10,    
    justifyContent: 'center', // Center FastImage vertically
    alignItems: 'center', // Center FastImage horizontally
  },

  image: {
    width: (Dimensions.get('window').width * 0.7) - 40,
    height: 296,
    borderRadius: 10,
    borderWidth: 4,
    borderColor: colors.primary,
  },
});


export default AiResultNewForTagScan;
