import React from 'react';
import {StyleSheet} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';

import MyLegitAppAuthentications from '../MyLegitAppAuthentications';
import MyAuthentications from '../MyAuthentications';
import MyVotes from '../MyVotes';
import colors from '../../assets/colors';
import {screens, navLabels} from '../../assets/strings';
import {fontSize} from '../../assets/font';

const TopTabs = createMaterialTopTabNavigator();

const Index = () => {
  return (
    <TopTabs.Navigator
      initialRouteName={screens.MY_AUTHENTICATIONS}
      screenOptions={{
        swipeEnabled: false,
        tabBarActiveTintColor: colors.black,
        tabBarIndicatorStyle: styles.indicator,
        tabBarStyle: styles.barStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
      }}>
      <TopTabs.Screen
        name={screens.MY_AUTHENTICATIONS}
        component={MyAuthentications}
        options={{
          tabBarLabel: navLabels.MY_AUTHENTICATIONS,
        }}
      />
      <TopTabs.Screen
        name={screens.MY_LEGITAPP_STUFF}
        component={MyLegitAppAuthentications}
        options={{
          tabBarLabel: navLabels.MY_LEGIT_APP_AUTHENTICATIONS,
        }}
      />
      <TopTabs.Screen
        name={screens.MY_VOTES}
        component={MyVotes}
        options={{tabBarLabel: navLabels.MY_VOTES}}
      />
    </TopTabs.Navigator>
  );
};

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: colors.primary,
    borderRadius: 25,
  },
  barStyle: {
    backgroundColor: colors.white,
    elevation: 1,
    borderTopWidth: 3,
    borderTopColor: colors.lightGrey,
  },
  tabBarLabelStyle: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
  },
});

export default Index;
