import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  TextInput,
} from 'react-native';
import { useMutation, useLazyQuery } from '@apollo/client';
import IAP from 'react-native-iap';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  APPLY_AUTHENTICATOR_CODE,
  APPLY_COUPON_CODE,
  CONFIRM_IAP_FOR_IOS,
  LEGITAPP_CREATE_AUTHENTICATION,
  LOG_ACTIVITY_TO_SERVER,
} from '../../apollo/mutations';
import { GET_PAYMENT_DETAILS } from '../../apollo/queries';
import {
  authenticationTypeScreen,
  screens,
  persistent,
} from '../../assets/strings';
import colors from '../../assets/colors';
import { fontSize } from '../../assets/font';
import { spacings } from '../../assets/spacing';
import Icon from '../../components/Icon';
import Layout from '../../layout';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';

const POPUP_INITIAL_STATE = {
  state: false,
  text: '',
};

const PAYMENT_STATE = {
  isCouponApplied: false,
  discountId: null,
  data: {
    total: null,
    subtotal: null,
    tax: null,
    discountAmount: null,
    totalWithoutDiscount: null,
    isDiscounted: false,
    isFullDiscount: false,
    iapKeyIOS: null,
  },
};

const INITIAL_PURCHASE_STATE = {
  success: false,
  data: null,
  message: '',
};

const IAP_ERROR_MESSAGE_MAPPING = {
  E_USER_CANCELLED:
    locale.PleaseFinishAllTheInAppPurchaseStepsToCompleteThePurchase,
};

const LegitAppCheckOut = ({ route, navigation }) => {

  const authenticationDetail = route?.params?.authenticationDetail;
  const authId = route?.params?.authId;
  const iapKeyForAuthentication = route?.params?.iapKeyForAuthentication;


  const [purchaseState, setPurchaseState] = React.useState(
    INITIAL_PURCHASE_STATE,
  );

  const listeners = React.useRef({
    purchaseUpdatedListener: null,
    purchaseErrorListener: null,
  });

  const [paymentState, setPaymentState] = React.useState({
    ...PAYMENT_STATE,
    data: {
      ...PAYMENT_STATE.data,
      subtotal: authenticationDetail?.iap_price_ios,
      total: authenticationDetail?.iap_price_ios,
      tax: 0,
    },
  });

  const [globalDisable, setGlobalDisable] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
  const [successpopup, setSuccessPopup] = React.useState(POPUP_INITIAL_STATE);

  const [applyAuthenticatorCode] = useMutation(APPLY_AUTHENTICATOR_CODE);
  const [confirmIAPForIOS] = useMutation(CONFIRM_IAP_FOR_IOS);
  const [logActivityToServer] = useMutation(LOG_ACTIVITY_TO_SERVER);

  const detachListeners = () => {
    if (!(listeners.current.purchaseUpdatedListener == null)) {
      listeners.current.purchaseUpdatedListener.remove();
      listeners.current.purchaseUpdatedListener = null;
    }
    if (!(listeners.current.purchaseErrorListener == null)) {
      listeners.current.purchaseErrorListener.remove();
      listeners.current.purchaseErrorListener = null;
    }
  };

  const [
    createAuthentication,
    { loading: createAuthenticationLoading },
  ] = useMutation(LEGITAPP_CREATE_AUTHENTICATION);

  const loading = createAuthenticationLoading


  const confirmPayment = React.useCallback(
    async (
      receiptData,
      isRetrying = false,
      iapKey = null,
      discountId = null,
    ) => {
      try {
        setIsLoading(true);

        const data = {
          brand_id: authenticationDetail.brand_id,
          category_id: authenticationDetail.category_id,
          model_id: authenticationDetail.model_id,
          category_name: authenticationDetail.category_name,
          brand_name: authenticationDetail.brand_name,
          model_name: authenticationDetail.model_name,
          authentication_set_id: authenticationDetail.turnaround_time_id,
          turnaround_time_id: authenticationDetail.authentication_set_id,
          image_urls: authenticationDetail.image_urls,
          user_remark: authenticationDetail.user_remark,
          user_custom_code: authenticationDetail.user_custom_code
        };


        console.log("Data......", data)

        const response = await (
          createAuthentication({
            variables: {
              input: data,
            },
          })
        );



        console.log("response?.data-----", response?.data)

        const isSuccessful = response?.data?.submitLegitAppAuthenticationWithUrls?.success;
        const message = response?.data?.submitLegitAppAuthenticationWithUrls?.message;
        if (isSuccessful) {
          // navigation.navigate(screens.AUTH_SUCCESSFUL, {
          //   params: response?.data?.confirmIAPForIOS?.data?.key,
          //   authenticationType: authenticationDetail?.type,
          // });

          setIsLoading(false);
          setSuccessPopup({
            state: true,
            text: message ?? locale.SomethingWentWrongPleaseTryAgain,
          });

          /*logActivityToServer({
            variables: {
              type: 'IN_APP_PURCHASE_SUCCESS_IOS_CLIENT',
              data: JSON.stringify({ receiptData, iapKey, discountId, authId }),
              message: 'IOS: In app purchase successful.',
            },
          })
            .then(() => null)
            .catch(() => null);

            const confirmIAPForIOSResponse = await confirmIAPForIOS({
              variables: {
                authenticationId: +authId,
                receipt: receiptData?.transactionReceipt,
                iap_key: iapKey ?? iapKeyForAuthentication,
                isUpgradeFromExpertCertifiedToExpertCertifiedNFT: false,
                data: JSON.stringify(receiptData),
                discountId,
              },
            });
            setIsLoading(false);
            const isSuccessful = confirmIAPForIOSResponse?.data?.confirmIAPForIOS?.success;
            const code = confirmIAPForIOSResponse?.data?.confirmIAPForIOS?.code;
            const message = confirmIAPForIOSResponse?.data?.confirmIAPForIOS?.message;

            console.log("isSuccessful.......", isSuccessful)
            console.log("code.......", code)
            console.log("message.......", message)
            setSuccessPopup({
              state: true,
              text: message ?? locale.SomethingWentWrongPleaseTryAgain,
            });*/



          

        } else {
          setIsLoading(false);
          setPopup({
            state: true,
            text: message ?? locale.SomethingWentWrongPleaseTryAgain,
          });
        }
        
        // navigation.navigate(screens.AUTH_SUCCESSFUL, {
        //   params: response?.data?.confirmIAPForIOS?.data?.key,
        //   authenticationType: authenticationDetail?.type,
        // });


        /*

        if (isSuccessful) {
          navigation.navigate(screens.AUTH_SUCCESSFUL, {
            params: response?.data?.confirmIAPForIOS?.data?.key,
            authenticationType: authenticationDetail?.type,
          });
        } else {
          if (code === 'PAYMENT_ALREADY_DONE') {
            navigation.navigate(screens.AUTH_SUCCESSFUL, {
              params: response?.data?.confirmIAPForIOS?.data?.key,
              authenticationType: authenticationDetail?.type,
            });
          } else {
            setPopup({
              state: true,
              text: message ?? locale.SomethingWentWrongPleaseTryAgain,
            });
          }
        }*/
      } catch (error) {
        setIsLoading(false);
        setPopup({
          state: true,
          text: locale.SomethingWentWrongPleaseTryAgain,
        });
      }
    },
    [
      navigation.navigate,
      authId,
      iapKeyForAuthentication,
      authenticationDetail?.type,
    ],
  );

  const initializePurchaseListener = React.useCallback(
    (iapKey = null, discountId = null) => {
      try {
        detachListeners();
        listeners.current.purchaseUpdatedListener = IAP.purchaseUpdatedListener(
          purchase => {
            // Log to server  silently
            // logActivityToServer({
            //   variables: {
            //     type: 'IN_APP_PURCHASE_SUCCESS_IOS_CLIENT',
            //     data: JSON.stringify({ purchase, iapKey, discountId, authId }),
            //     message: 'IOS: In app purchase successful.',
            //   },
            // })
            //   .then(() => null)
            //   .catch(() => null);

            setPurchaseState({
              ...INITIAL_PURCHASE_STATE,
              success: true,
              data: purchase,
            });
            IAP.finishTransaction(purchase);
            confirmPayment(purchase, false, iapKey, discountId);
          },
        );
        listeners.current.purchaseErrorListener = IAP.purchaseErrorListener(
          errorResponse => {
            setIsLoading(false);

            setPopup({
              state: true,
              text:
                errorResponse?.message ??
                locale.SomethingWentWrongPleaseTryAgain,
            });
            // Log to server  silently
            logActivityToServer({
              variables: {
                type: 'IN_APP_PURCHASE_ERROR_IOS_CLIENT',
                data: JSON.stringify({
                  error: errorResponse,
                  iapKey,
                  discountId,
                  authId,
                }),
                message: errorResponse?.message ?? '',
              },
            })
              .then(() => null)
              .catch(() => null);
          },
        );
      } catch (_error) {
        setPopup({
          state: true,
          text: locale.SomethingWentWrongPleaseTryAgain,
        });
      }
    },
    [
      detachListeners,
      authenticationDetail?.type,
      confirmPayment,
      logActivityToServer,
      authId,
    ],
  );




  const handlePayment = async () => {

    await confirmPayment(
      { transactionReceipt: (+new Date()).toString() },
      false,
      paymentState.data.iapKeyIOS,
      paymentState.discountId,
    );

    return

    setIsLoading(true);

    if (paymentState.data.isFullDiscount) {
      await confirmPayment(
        { transactionReceipt: (+new Date()).toString() },
        false,
        paymentState.data.iapKeyIOS,
        paymentState.discountId,
      );
    } else {
      // In App purchases
      console.log("Kapil Testing iapKeyForAuthentication", iapKeyForAuthentication);
      try {
        await IAP.initConnection();
        const _iapKey = iapKeyForAuthentication

        const availablePurchases = await IAP.getProducts([
          _iapKey,
        ]);

        const product = availablePurchases?.find(
          purchase => purchase?.productId == _iapKey,
        );

        if (!product) {

          setIsLoading(false);
          const errorCode = error?.code;
          setPopup({
            state: true,
            text: locale.SomethingWentWrongPleaseTryAgain,
          });

        } else {
          const sku = product.productId
          await IAP.requestPurchase({ sku });
          initializePurchaseListener(
            paymentState.isCouponApplied ? _iapKey : null,
            paymentState.isCouponApplied ? paymentState.discountId : null,
          );
          await IAP.clearProductsIOS();
        }


      } catch (error) {
        setIsLoading(false);
        console.log("Kapil Testing iapKeyForAuthentication", iapKeyForAuthentication);
        const errorCode = error?.code;
        setPopup({
          state: true,
          text:
            IAP_ERROR_MESSAGE_MAPPING?.[errorCode] ??
            error?.message ??
            locale.SomethingWentWrongPleaseTryAgain,
        });
        // Log to server  silently
        logActivityToServer({
          variables: {
            type: 'IN_APP_PURCHASE_ERROR_IOS_CLIENT',
            data: JSON.stringify({ error }),
            message: error?.message ?? '',
          },
        })
          .then(() => null)
          .catch(() => null);
      }
    }
  };

  function redirect() {
    setSuccessPopup(POPUP_INITIAL_STATE)
    navigation.reset({
      routes: [
        {
          name: screens.DRAW_NAVIGATOR,
          state: {
            routes: [
              {
                name: screens.TAB_NAVIGATOR,
                state: {
                  routes: [{name: screens.HOME}],
                },
              },
            ],
          },
        },
      ],
    });
  }

  React.useEffect(() => {
    return () => {
      detachListeners();
    };
  }, [detachListeners]);

  const isDisabled = globalDisable || isLoading;

  return (
    <View style={styles.container}>
      <View style={styles.subContainer}>
      <Text style={styles.subTitle}>Authentication - {authenticationDetail.category.title}</Text>
      <Text style={styles.mainTitle}>{authenticationDetail.brand.title} | {authenticationDetail.modal.title}</Text>
      <View style={styles.separator} />
    </View>
      

      <Text style={styles.labelTitle}>{locale.CheckOut}</Text>
      <View style={styles.taxSection}>

        <View style={styles.desc}>
          <Text style={[styles.totalLabel]}>
            {authenticationTypeScreen?.TOTAL}
          </Text>
          <Text style={styles.totalLabel}>
            ${(paymentState?.data?.total).toFixed(2)}
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={styles.checkoutButton}
        onPress={
          purchaseState.success
            ? confirmPayment.bind(null, purchaseState.data, true)
            : handlePayment
        }
        disabled={isDisabled}>
        {isLoading ? (
          <ActivityIndicator color={colors.white} />
        ) : purchaseState.success ? (
          <>
            <Icon name="reload" tint={colors.white} size={18} />
            <Text style={styles.checkoutButtonText}>{locale.Retry}</Text>
          </>
        ) : (
          <>
            <Icon name="lock" tint={colors.white} size={18} />
            <Text style={styles.checkoutButtonText}>
              {authenticationTypeScreen.PAY_NOW}
            </Text>
          </>
        )}
      </TouchableOpacity>
      <InfoModal
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
        popUp={{
          state: popup.state,
          isError: true,
          data: { title: locale.Error, description: popup.text },
        }}
      />

      <InfoModal
        setVisible={() =>
          redirect()
          }
        popUp={{
          state: successpopup.state,
          isError: false,
          data: { title: locale.Success, description: successpopup.text },
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    padding: spacings.lg,
    marginVertical: 10,
  },
  subContainer: {
    marginBottom: 30,
  },
  subTitle: {
    color: colors.darkGreyBorder,
    fontSize: fontSize.sm,
    textAlign: 'center',
  },
  mainTitle: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 2,
  },
  separator: {
    marginTop: 8,
    height: 1,
    backgroundColor: '#444',
    width: '100%',
    marginTop: 20,
  },
  emptyContainer: {
    flex: 1,
    backgroundColor: colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentType: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    flexWrap: 'wrap',
  },
  inputStyles: {
    height: 50,
    borderWidth: 1,
    color: colors.white,
    borderRadius: 4,
    width: '100%',
    borderColor: colors.grey,
    marginBottom: spacings.lg,
    paddingLeft: spacings.md,
  },
  desc: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    flexWrap: 'wrap',
  },
  taxSection: {
    marginBottom: spacings.lg,
    paddingBottom: spacings.lg,
  },
  checkoutButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
    borderRadius: 4,
    flexDirection: 'row',
  },
  checkoutButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    marginLeft: spacings.sm,
    fontWeight: 'bold',
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.white,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Regular',
  },
  totalLabel: {
    fontWeight: 'bold',
    fontSize: fontSize.lg,
    color: colors.white,
    marginBottom: spacings.sm,
  },
  changeButton: {
    alignItems: 'flex-end',
    paddingBottom: spacings.md,
  },
  changeButtonText: {
    fontWeight: 'bold',
    color: colors.primary,
    fontSize: fontSize.lg,
  },
  errorStyles: {
    color: colors.warning,
    fontSize: fontSize.sm,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.white,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  labelTitle: {
    fontSize: 20,
    color: colors.white,
    marginBottom: 10,
    fontFamily: 'Gugi-Regular',
  },
});

export default LegitAppCheckOut;
