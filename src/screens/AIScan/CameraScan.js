import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { RNCamera } from 'react-native-camera';
import {
  API_AI_SCAN_URL
} from '@env';
import { useNavigation } from '@react-navigation/core';
import ActivityIndicator from '../../components/ActivityIndicator';
import colors from '../../assets/colors';
import { screens } from '../../assets/strings';
import { useImageUpload } from '../../tools/hooks';
import locale from '../../assets/locale.json';
import InfoModal from '../../components/Modal/InfoModal';
import ImageCropPicker from 'react-native-image-crop-picker';
import {
  API_AI_PREDICT_APPRAISAL_URL_DEV,
  API_AI_PREDICT_URL_DEV
} from '@env';


const CameraScan = () => {
  const cameraRef = useRef(null);
  const [capturedImage, setCapturedImage] = useState(null);
  const [loading, setLoading] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState(locale.SomethingWentWrongPleaseTryAgain);
  const [popup, setPopup] = React.useState(false);

  const navigation = useNavigation();
  const { upload } = useImageUpload();

  const url = process.env.NODE_ENV == 'development' ? API_AI_PREDICT_URL_DEV : API_AI_PREDICT_URL_DEV;
  const appraisalURL = process.env.NODE_ENV === 'development' ? API_AI_PREDICT_APPRAISAL_URL_DEV : API_AI_PREDICT_APPRAISAL_URL_DEV
  


  useEffect(() => {
    // Open the camera when the screen is visible
    //openCamera();
  }, []);

  useEffect(() => {
    if (!popup.state && !popup.isError) {
      setCapturedImage(null);
    }
  }, [popup]);

  const sendPostRequest = async (path) => {
    setLoading(true);
    try {

      const requestBody = {
        image_url: path.path,
        tshirt_tags: [
          "Alstyle Apparel & Activewear T-Shirt Tags 1995-2006",
          "Anvil T-Shirt Tags 1989-2007",
          "Ched and Anvil T-Shirt Tags 1976-1988",
          "Delta T-Shirt Tags 1988-2014",
          "Fruit of the Loom 1970-1998",
          "Giant T-Shirt Tags 1991-1996",
          "Gildan T-Shirt Tags 1995-2002",
          "Hanes T-Shirt Tags 1989-1997",
          "Jerzees T-Shirt Tags 1985-1998",
          "Oneita T-Shirt Tags 1984-1999",
          "Screen Stars T-Shirt Tags 1980-1994",
          "Signal T-Shirt Tags 1977-1994",
          "Sportswear T-Shirt Tags 1968-1990",
          "Stedman & Hi Cru T-Shirt Tags 1971-1997",
          "Tennessee River T-Shirt Tags 1984-2010",
          "Wild Oats T-Shirt Tags 1984-1997",
          "Winterland T-Shirt Tags 1982-2008",
          "Others"
        ]
      };


      try {
        await fetch(API_AI_SCAN_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json', // Specify that you're sending JSON
          },
          body: JSON.stringify(requestBody),
        }).then((response) => {
          if (!response.ok) {
            setLoading(false);
            throw new Error('Network response was not ok', url);
          }
          return response.json();
        })
          .then((data) => {
            console.log('Predication Result:', data);
            if (data?.message) {
              setErrorMessage(data?.message)
              setPopup(true);
              setLoading(false);
            } else if (data.results.length > 0) {

              handlePostAIScanResult(path, data.results)
            }
          })
          .catch((error) => {
            console.error('Error uploading image:', error.message);
            setErrorMessage(error.message)
            setPopup(true);
          });

      } catch (error) {
        console.error('Error uploading image:', error.message);
        setErrorMessage(error.message)
        setPopup(true);
      }

    } catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      //setLoading(false);
    }
  }

  const handlePostAIScanResult = async (path, results) => {

    try {

      const url = "";
      const front = { images: [] };
      const frontCloseUp = { images: [] };
      const frontTag = { images: [] };
      const back = { images: [] };
      const backCloseup = { images: [] };
      const backTag = { images: [] };

      const copyright = { images: [] };
      const stitichingHem = { images: [] };
      const stitichingSleeve = { images: [] };
      const predictionData = [];
      const keysData = [];
      var tShirtTagName = "";

      var tShirtTag = [""];


      results.forEach((item, index) => {
        if (index == 0) {
          const tag = item["tag"];
          const year_end = item["year_end"];
          const year_start = item["year_start"];
          tShirtTagName = tag
          //tShirtTag = tag + " " + year_start + "-" + year_end          
          //tShirtTag = [year_start + "-" + year_end]          
          return
        }
        tShirtTag = item["years"];
        const similarImages = item["similar images"];
        console.log("similarImages-----", item)
        const predictions = item["predictions"];
        const keys = item["keys"];
        const status = item["status"];

        predictionData.push(...predictions)
        keysData.push(...keys)

        frontTag.images.push({type: 'Front Tag', uri: path.path, passed: null});

        if (similarImages) {

          similarImages.forEach((imageItems, imageIndex) => {
            if (status[imageIndex]) {
              if (status[imageIndex] == 'public') {
                frontTag.images.push({ type: 'Front Tag', uri: imageItems, passed: null });
              }
            } else {
              frontTag.images.push({ type: 'Front Tag', uri: imageItems, passed: null });
            }
          })
        }
      })

      /*if(frontTag.images.length > 0){

        console.log("Start-----------", frontTag.images[0].uri)
        handlePostAIAppraisal(path.path, frontTag.images[0].uri, tShirtTag, tShirtTagName)

        
      } else {
        console.log("Start-----------")
      }*/


      let predication = false;
      setCapturedImage(null)
      navigation.navigate(screens.AITAGRESULT, {
        front, frontCloseUp, frontTag,
        back, backTag, backCloseup,
        copyright, stitichingSleeve, stitichingHem, url, predication, predictionData, keysData, tShirtTag, tShirtTagName
      });

    } catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      setLoading(false);
    }
  };

  const handlePostAIAppraisal = async (path, scanImage, tShirtTag, tShirtTagName) => {
    let isAPICalled = 0
    console.log("path====", path)
    //setLoading(true);
    try {

      // Upload the locally saved images
      
      const formData = new FormData();
      const fileNew = {
        uri: path,
        type: 'image/jpeg',
        name: path.split('/').pop(),
      };
      formData.append('images', fileNew);


      try {
        await fetch(appraisalURL, {
          method: 'POST',
          body: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }).then((response) => {
          if (!response.ok) {
            throw new Error('Network response was not ok', url);
          }
          return response.json();
        })
          .then((data) => {
            console.log('Predication Result:', data.results);
            if (data.results.length > 0) {

              isAPICalled = 1
              handlePostAIForPredictImages(data.results, path, tShirtTag, tShirtTagName)
            }
          })
          .catch((error) => {
            console.error('Error uploading image:', error.message);
            setErrorMessage(error.message)
            setPopup(true);
          });

      } catch (error) {
        console.error('Error uploading image:', error.message);
        setErrorMessage(error.message)
        setPopup(true);
      }
    }
    catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      if (isAPICalled == 0) {
        setLoading(false);
      }
    }
  }

  const handlePostAIForPredictImages = async (results, scanImage, tShirtTag, tShirtTagName) => {

    try {
        const front = { images: [] };
        const frontCloseUp = { images: [] };
        const frontTag = { images: [] };
        const back = { images: [] };
        const backCloseup = { images: [] };
        const backTag = { images: [] };

        const copyright = { images: [] };
        const stitichingHem = { images: [] };
        const stitichingSleeve = { images: [] };
        //const probabilityData = [];
        const predictionData = [];
        const keysData = [];

        results.forEach((item, index) => {
          const similarImages = item["similar images"];
          //const probability = item["probability"];    
          const predictions = item["predictions"];
          const keys = item["keys"];
          const status = item["status"];

          //probabilityData.push(...probability)
          predictionData.push(...predictions)
          keysData.push(...keys)

          frontTag.images.push({type: 'Front Tag', uri: scanImage, passed: null});

          if (similarImages.front_tag) {

            similarImages.front_tag.forEach((imageItems, imageIndex) => {
              //frontTag.images.push({type: 'Front Tag', uri: imageItems, passed: null});
              if(status[imageIndex]) {
                if(status[imageIndex] == 'public') {
                  frontTag.images.push({type: 'Front Tag', uri: imageItems, passed: null});
                }
              } else {
                frontTag.images.push({type: 'Front Tag', uri: imageItems, passed: null});
              }
            })
          }

          

          
          /*if (similarImages.front) {
            similarImages.front.forEach((imageItems, imageIndex) => {
              front.images.push({ type: 'Front', uri: imageItems, passed: null });
            })
          }

          if (similarImages.closeup_front) {
            similarImages.closeup_front.forEach((imageItems, imageIndex) => {
              frontCloseUp.images.push({ type: 'Front Closeup', uri: imageItems, passed: null });
            })
          }

          if (similarImages.back) {

            similarImages.back.forEach((imageItems, imageIndex) => {
              back.images.push({ type: 'Back', uri: imageItems, passed: null });
            })
          }

          if (similarImages.closeup_back) {

            similarImages.closeup_back.forEach((imageItems, imageIndex) => {
              backCloseup.images.push({ type: 'Back Closeup', uri: imageItems, passed: null });
            })
          }

          if (similarImages.back_tag) {
            similarImages.back_tag.forEach((imageItems, imageIndex) => {
              backTag.images.push({ type: 'Back Tag', uri: imageItems, passed: null });
            })
          }

          if (similarImages.copyright) {
            similarImages.copyright.forEach((imageItems, imageIndex) => {
              copyright.images.push({ type: 'Copyright', uri: imageItems, passed: null });
            })
          }

          if (similarImages.arm_hem_stitching) {
            similarImages.arm_hem_stitching.forEach((imageItems, imageIndex) => {
              stitichingSleeve.images.push({ type: 'Stitching Sleeve', uri: imageItems, passed: null });
            })
          }

          if (similarImages.lower_hem_stitching) {
            similarImages.lower_hem_stitching.forEach((imageItems, imageIndex) => {
              stitichingHem.images.push({ type: 'Stitching Hem', uri: imageItems, passed: null });
            })
          }*/

        })

        setCapturedImage(null)

        let predication = false;
        navigation.navigate(screens.AIRESULT, {
          front, frontCloseUp, frontTag,
          back, backTag, backCloseup,
          copyright, stitichingSleeve, stitichingHem, url, predication, predictionData, keysData, tShirtTag, tShirtTagName
        });
    } catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      setLoading(false);
    }
  };

  const takePicture = async () => {
    // if (cameraRef.current) {
    //   try {
    //     const options = { quality: 0.5, base64: true };
    //     const data = await cameraRef.current.takePictureAsync(options);
    //     setCapturedImage(data.uri); // Save the captured image URI
    //     //console.log('Captured Image URI:', data.uri);
    //     const {path} = await upload(data);
    //     console.log("path======>", path);
    //     //sendPostRequest()
    //   } catch (error) {
    //     console.error('Error taking picture:', error);
    //   }
    // }

    openCamera()
  };

  const openCamera = async () => {

    try {
      const image = await ImageCropPicker.openCamera({
        freeStyleCropEnabled: true,
        cropping: true,
        mediaType: 'photo',
        width: 1080,
        height: 1080,
      });

      setCapturedImage(image.path)

      setLoading(true);
      const path = await upload(image);
      console.log("path======>", path);
      setCapturedImage(path.path)
      sendPostRequest(path)
      //handlePostAIAppraisal(path.path)

    } catch (error) {
      console.log('Canceled by user or error occurred', error);
      setErrorMessage(locale.SomethingWentWrongPleaseTryAgain)
      setPopup(true);
      setLoading(false);
    }
  }



  return (
    <View style={styles.container}>

      {capturedImage ? (
        <>
          <Image source={{ uri: capturedImage }} style={styles.preview} />
          {/* <TouchableOpacity
            onPress={() => setCapturedImage(null)}
            style={styles.captureButton}
          >
            <Text style={styles.buttonText}>Retake</Text>
          </TouchableOpacity> */}
        </>
      ) : (
        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={takePicture} style={styles.captureButton}>
            <Text style={styles.buttonText}>Take Image</Text>
          </TouchableOpacity>

          <Text style={styles.buttonText2}>Click on the 'Take Image' button to capture the tag image again.</Text>
        </View>
      )}

      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size={50} color={colors.white} />
        </View>
      )}

      <InfoModal
        //setVisible={setPopup}

        setVisible={() => {
          //console.log("Kapillllllllllll", popup.isError)
          if (!popup.isError) {
            //navigation.goBack();
            //setCapturedImage(null)
          }
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}

        popUp={{
          isError: true,
          state: popup,
          data: {
            title: locale.Error,
            //description: locale.SomethingWentWrongPleaseTryAgain,
            description: errorMessage,
          },
        }}
      />
    </View>
  );

};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',

  },
  camera: {
    flex: 1,
    width: '100%',
  },
  buttonContainer: {
    position: 'absolute',
    alignSelf: 'center',
  },
  captureButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 50,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
  },

  buttonText2: {
    color: '#FFF',
    fontSize: 16,
    alignSelf: 'center',
    padding: 20,
    textAlign: 'center',
    justifyContent: 'center',
  },
  preview: {
    width: 300, // Set width for the image
    height: 400, // Set height for the image
    resizeMode: 'contain', // Adjust image scaling
    alignSelf: 'center',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject, // Fills the entire screen
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent overlay
    zIndex: 100, // Ensures it overlays everything
  }
});


export default CameraScan;
