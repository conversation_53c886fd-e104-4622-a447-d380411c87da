import React, {useState, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';
import dayjs from 'dayjs';

import {GET_LEGITAPP_AUTHENTICATION} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {icons} from '../../assets/strings';
import Icon from '../../components/Icon';
import FastImage from '../../components/FastImage';
import BottomSheet from '../../components/BottomSheet';
import MyAuthenticatonsLoader from '../../components/Loaders/MyAuthenticatonsLoader';

const {height: screenHeight} = Dimensions.get('window');

const LegitAppOrderDetail = ({navigation, route}) => {
  const {orderId} = route.params;
  const [refreshing, setRefreshing] = useState(false);
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);
  const bottomSheetRef = useRef(null);

  const {data, loading, refetch} = useQuery(GET_LEGITAPP_AUTHENTICATION, {
    variables: {id: orderId},
    fetchPolicy: 'cache-and-network',
  });

  const orderData = data?.getLegitAppAuthentication?.data;

  useFocusEffect(
    useCallback(() => {
      refetch?.();
    }, [refetch]),
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch?.();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const getStatusText = status => {
    switch (status) {
      case 1:
        return 'PENDING';
      case 2:
        return 'COMPLETED';
      case 3:
        return 'INCONCLUSIVE';
      default:
        return 'INCONCLUSIVE';
    }
  };

  const parseResult = result => {
    if (!result) return null;
    try {
      return JSON.parse(result);
    } catch (e) {
      return null;
    }
  };

  const resultData = parseResult(orderData?.result);
  const isInconclusive = orderData?.status === 3;

  const handleBackPress = () => {
    navigation.goBack();
  };

  const toggleBottomSheet = () => {
    setBottomSheetVisible(!bottomSheetVisible);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
        <Icon name={icons.ARROW_LEFT} size={24} tint={colors.white} />
      </TouchableOpacity>
      <View style={styles.headerTitleContainer}>
        <Text style={styles.headerTitle}>#{orderData?.authentication_id}</Text>
      </View>
      <View style={styles.backButton}></View>
      {/* <TouchableOpacity style={styles.menuButton} onPress={toggleBottomSheet}>
        <Icon name={icons.ELIPSIS_HORIZONTAL} size={24} tint={colors.white} />
      </TouchableOpacity> */}
    </View>
  );

  const renderBrandInfo = () => (
    <View style={styles.brandContainer}>
      <Text style={styles.brandName}>Bvlgari</Text>
      <Text style={styles.productType}>Bracelet Code Checking</Text>
    </View>
  );

  const renderLegitAppBadge = () => (
    <View style={styles.legitAppContainer}>
      <View style={styles.legitAppBadge}>
        <Icon name={icons.SUCCESS_CHECK_ICON} size={16} tint={colors.white} />
        <Text style={styles.legitAppText}>LEGITAPP</Text>
      </View>
    </View>
  );

  const renderStatusSection = () => (
    <View style={styles.statusContainer}>
      <View
        style={[
          styles.statusBadge,
          isInconclusive && styles.inconclusiveStatusBadge,
        ]}>
        <Text style={styles.statusText}>
          {getStatusText(orderData?.status)}
        </Text>
      </View>
      <Text style={styles.orderIdText}>#{orderData?.authentication_id}</Text>
      <Text style={styles.dateText}>
        Checked at {dayjs(orderData?.created_at).format('YYYY/MM/DD, h:mm A')}
      </Text>
    </View>
  );

  const renderCheckingResult = () => (
    <View style={styles.resultContainer}>
      <View style={styles.resultHeader}>
        <Icon name={icons.SEARCH_ICON} size={24} tint={colors.white} />
        <Text style={styles.resultTitle}>Checking Result</Text>
      </View>

      <View style={styles.resultContent}>
        <Text style={styles.resultStatus}>
          {getStatusText(orderData?.status)}
        </Text>
        <Text style={styles.resultId}>#{orderData?.authentication_id}</Text>
        <Text style={styles.resultDescription}>
          Checked by LEV Authentication Expert
        </Text>
        <Text style={styles.resultSubtext}>Joyce, Luxury Expert Lead</Text>
      </View>

      <View style={styles.disclaimerContainer}>
        <Text style={styles.disclaimerText}>
          We offer the special product code checking service which we can help
          to check if the unique product code matches the product item
          officially. Matched the product code does not imply that the product
          is authentic. For checking the authenticity of the product, please use
          our authentication service instead.
        </Text>
      </View>

      <TouchableOpacity style={styles.compensationButton}>
        <Text style={styles.compensationText}>Check compensation records</Text>
        <Icon name={icons.ARROW_RIGHT} size={16} tint={colors.white} />
      </TouchableOpacity>
    </View>
  );

  const renderReviews = () => (
    <View style={styles.reviewsContainer}>
      <Text style={styles.reviewsTitle}>Reviews from Authenticators</Text>

      <View style={styles.reviewItem}>
        <View style={styles.reviewHeader}>
          <View style={styles.reviewerInfo}>
            <Icon name={icons.PERSON_ICON} size={24} tint={colors.white} />
            <View style={styles.reviewerDetails}>
              <Text style={styles.reviewerName}>LEV Authentication Expert</Text>
              <Text style={styles.reviewDate}>
                {dayjs(orderData?.created_at).format('MM/DD/YYYY, h:mm A')}
              </Text>
            </View>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <Text style={styles.profileButtonText}>Profile</Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.reviewStatus}>INCONCLUSIVE</Text>
      </View>

      <View style={styles.reviewItem}>
        <View style={styles.reviewHeader}>
          <View style={styles.reviewerInfo}>
            <Icon name={icons.PERSON_ICON} size={24} tint={colors.white} />
            <View style={styles.reviewerDetails}>
              <Text style={styles.reviewerName}>Joyce, Luxury Expert Lead</Text>
              <Text style={styles.reviewDate}>
                {dayjs(orderData?.created_at).format('MM/DD/YYYY, h:mm A')}
              </Text>
            </View>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <Text style={styles.profileButtonText}>Profile</Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.reviewText}>
          We gave the above authentication result based on - The photos provided
          are not valid.
        </Text>
      </View>
    </View>
  );

  const renderTimelineContent = () => (
    <ScrollView
      style={styles.timelineScrollView}
      showsVerticalScrollIndicator={false}>
      <View style={styles.timelineHeader}>
        <Text style={styles.timelineTitle}>Order Timeline</Text>
      </View>

      <View style={styles.timelineContainer}>
        <View style={styles.timelineItem}>
          <View style={styles.timelineIcon}>
            <View style={styles.timelineDot} />
          </View>
          <View style={styles.timelineContent}>
            <Text style={styles.timelineItemTitle}>Final Result</Text>
            <Text style={styles.timelineItemSubtitle}>
              The result is INCONCLUSIVE
            </Text>
          </View>
        </View>

        <View style={styles.timelineItem}>
          <View style={styles.timelineIcon}>
            <View style={styles.timelineDot} />
          </View>
          <View style={styles.timelineContent}>
            <Text style={styles.timelineItemTitle}>Authenticator Reviewed</Text>
            <Text style={styles.timelineItemSubtitle}>
              Authenticator - Joyce (BZH) provided a result
            </Text>
          </View>
        </View>

        <View style={styles.timelineItem}>
          <View style={styles.timelineIcon}>
            <View style={styles.timelineDot} />
          </View>
          <View style={styles.timelineContent}>
            <Text style={styles.timelineItemTitle}>Authenticator Reviewed</Text>
            <Text style={styles.timelineItemSubtitle}>
              Authenticator - LEV (PH4QSQN) provided a result. Please wait for
              our next authenticator
            </Text>
          </View>
        </View>

        <View style={styles.timelineItem}>
          <View style={styles.timelineIcon}>
            <View style={styles.timelineDot} />
          </View>
          <View style={styles.timelineContent}>
            <Text style={styles.timelineItemTitle}>
              Service Request Submitted
            </Text>
            <Text style={styles.timelineItemSubtitle}>
              Authentication Service - 12 Hours
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );

  const renderUploadedPhotos = () => (
    <View style={styles.uploadedPhotosContainer}>
      <TouchableOpacity style={styles.uploadedPhotosHeader}>
        <Text style={styles.uploadedPhotosTitle}>Uploaded Product Photos</Text>
        <Icon name={icons.CHEVRON_DOWN} size={20} tint={colors.white} />
      </TouchableOpacity>

      {orderData?.images && orderData?.images?.length > 0 && (
        <View style={styles.photosGrid}>
          {orderData.images.map((image, index) => (
            <View key={image.id || index} style={styles.photoContainer}>
              <FastImage
                source={{uri: image.legit_app_url || image.s3_url}}
                style={styles.photoImage}
                resizeMode="cover"
              />
              <View style={styles.photoOverlay}>
                <Icon name={icons.CANCEL_X} size={20} tint={colors.white} />
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  if (loading && !orderData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          {renderHeader()}
          <MyAuthenticatonsLoader />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {renderHeader()}

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.primary}
            />
          }>
          {renderBrandInfo()}
          {renderLegitAppBadge()}
          {renderStatusSection()}
          {renderCheckingResult()}
          {renderReviews()}
          {renderUploadedPhotos()}

          <View style={styles.bottomSpacing} />
        </ScrollView>

        <BottomSheet
          ref={bottomSheetRef}
          visible={bottomSheetVisible}
          onClose={() => setBottomSheetVisible(false)}
          height={screenHeight * 0.6}>
          <View style={styles.bottomSheetContent}>
            {renderTimelineContent()}
          </View>
        </BottomSheet>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
  },
  backButton: {
    width: 40,
    maxWidth: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexGrow: 1,
  },
  headerTitle: {
    fontSize: fontSize.lg,
    color: colors.white,
    fontWeight: '600',
    width: 'auto',
    textAlign: 'center',
  },
  menuButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  brandContainer: {
    alignItems: 'center',
    paddingVertical: spacings.lg,
  },
  brandName: {
    fontSize: fontSize.xl,
    color: colors.white,
    fontWeight: '600',
    marginBottom: spacings.xs,
  },
  productType: {
    fontSize: fontSize.md,
    color: colors.white,
    fontWeight: '400',
  },
  legitAppContainer: {
    alignItems: 'center',
    paddingVertical: spacings.md,
  },
  legitAppBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.secondaryBlack,
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.amber,
  },
  legitAppText: {
    color: colors.white,
    fontSize: fontSize.sm,
    fontWeight: '600',
    marginLeft: spacings.xs,
  },
  statusContainer: {
    alignItems: 'center',
    paddingVertical: spacings.lg,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.darkGrey,
    marginHorizontal: spacings.lg,
  },
  statusBadge: {
    backgroundColor: colors.darkGrey,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
    borderRadius: 8,
    marginBottom: spacings.md,
  },
  inconclusiveStatusBadge: {
    backgroundColor: colors.darkGrey,
  },
  statusText: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '600',
  },
  orderIdText: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '500',
    marginBottom: spacings.xs,
  },
  dateText: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
  },
  resultContainer: {
    margin: spacings.lg,
    backgroundColor: colors.secondaryBlack,
    borderRadius: 12,
    padding: spacings.lg,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacings.lg,
  },
  resultTitle: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: '600',
    marginLeft: spacings.sm,
  },
  resultContent: {
    alignItems: 'center',
    marginBottom: spacings.lg,
  },
  resultStatus: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: '600',
    marginBottom: spacings.xs,
  },
  resultId: {
    color: colors.darkGrey,
    fontSize: fontSize.md,
    marginBottom: spacings.sm,
  },
  resultDescription: {
    color: colors.white,
    fontSize: fontSize.sm,
    marginBottom: spacings.xs,
  },
  resultSubtext: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
  },
  disclaimerContainer: {
    backgroundColor: colors.black,
    padding: spacings.md,
    borderRadius: 8,
    marginBottom: spacings.lg,
  },
  disclaimerText: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
    lineHeight: 20,
  },
  compensationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.secondaryBlack,
    paddingVertical: spacings.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.darkGrey,
  },
  compensationText: {
    color: colors.white,
    fontSize: fontSize.md,
    marginRight: spacings.sm,
  },
  reviewsContainer: {
    margin: spacings.lg,
  },
  reviewsTitle: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: '600',
    marginBottom: spacings.lg,
  },
  reviewItem: {
    backgroundColor: colors.secondaryBlack,
    padding: spacings.lg,
    borderRadius: 12,
    marginBottom: spacings.md,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacings.md,
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  reviewerDetails: {
    marginLeft: spacings.sm,
    flex: 1,
  },
  reviewerName: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '500',
  },
  reviewDate: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
  },
  profileButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.xs,
    borderRadius: 16,
  },
  profileButtonText: {
    color: colors.white,
    fontSize: fontSize.sm,
    fontWeight: '500',
  },
  reviewStatus: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '600',
  },
  reviewText: {
    color: colors.white,
    fontSize: fontSize.md,
    lineHeight: 20,
  },
  bottomSpacing: {
    height: spacings.xxl,
  },
  bottomSheetContent: {
    padding: spacings.lg,
  },
  timelineScrollView: {
    flex: 1,
  },
  timelineHeader: {
    marginBottom: spacings.lg,
  },
  timelineTitle: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: '600',
  },
  timelineContainer: {
    paddingLeft: spacings.md,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: spacings.lg,
  },
  timelineIcon: {
    width: 20,
    alignItems: 'center',
    marginRight: spacings.md,
  },
  timelineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
  timelineContent: {
    flex: 1,
  },
  timelineItemTitle: {
    color: colors.white,
    fontSize: fontSize.md,
    fontWeight: '600',
    marginBottom: spacings.xs,
  },
  timelineItemSubtitle: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
    lineHeight: 18,
  },
  uploadedPhotosContainer: {
    margin: spacings.lg,
  },
  uploadedPhotosHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.secondaryBlack,
    padding: spacings.lg,
    borderRadius: 12,
    marginBottom: spacings.md,
  },
  uploadedPhotosTitle: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: '600',
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  photoContainer: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: spacings.md,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoOverlay: {
    position: 'absolute',
    top: spacings.sm,
    right: spacings.sm,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LegitAppOrderDetail;
