export const screens = {
  AUTHENTICATE: 'AUTHENTICATE',
  AUTHENTICATE_TAB: 'AUTHENTICATE_TAB',
  PROFILE: 'PROFILE',
  HOWITWORKS: 'HOWITWORKS',
  LOGOUT: 'LOGOUT',
  TAB_NAVIGATOR: 'TAB_NAVIGATOR',
  DRAW_NAVIGATOR: 'Draw_NAVIGATOR',
  ALL_ACTIVITY: 'ALL_ACTIVITY',
  FOLLOWING_FEED: 'FOLLOWING_FEED',
  MY_SUBMISSIONS: 'MY_SUBMISSIONS',
  MY_VOTES: 'MY_VOTES',
  HOME: 'HOME',
  MY_STUFF: 'MY_STUFF',
  MY_LEGITAPP_STUFF: 'MY_LEGITAPP_STUFF',
  MY_STUFF_EXPERT: 'MY_STUFF_EXPERT',
  WALLET: 'WALLET',
  FORGOT_PASSWORD: 'FORGOT_PASSWORD',
  LOGI<PERSON>: 'LOGIN',
  INITIAL_SCREEN: 'IN<PERSON><PERSON><PERSON>_SCREEN',
  REGISTER: 'REGISTER',
  EMAIL_VERIFICATION: 'EMAIL_VERIFICATION',
  CREATE_PASSWORD: 'CREATE_PASSWORD',
  RESET_PASSWORD: 'RESET_PASSWORD',
  VALIDATE_CODE: 'VALIDATE_CODE',
  FA2_VALIDATE_CODE: 'FA2_VALIDATE_CODE',
  FA2_VALIDATE_CODE_FROM_PROFILE: 'FA2_VALIDATE_CODE_FROM_PROFILE',
  ONBOARDING_SCREEN: 'FIRST_ONBOARDING_SCREEN',
  NOTIFICATIONS: 'NOTIFICATIONS',
  BILLING: 'BILLING',
  MY_AUTHENTICATIONS: 'MY_AUTHENTICATIONS',
  MY_LEGITAPP_AUTHENTICATIONS: 'MY_LEGITAPP_AUTHENTICATIONS',
  LEGITAPP_ORDER_DETAIL: 'LEGITAPP_ORDER_DETAIL',
  ALLBRAND_CATEGORIES: 'ALLBRAND_CATEGORIES',
  ALL_BRANDS: 'ALL_BRANDS',
  ALL_CATEGORIES: 'ALL_CATEGORIES',
  SINGLE_BRAND_SCREEN: 'SINGLE_BRAND_SCREEN',
  SINGLE_CATEGORY_SCREEN: 'SINGLE_CATEGORY_SCREEN',
  ADD_PHOTOS: 'ADD_PHOTOS',
  USER_PROFILE: 'USER_PROFILE',
  SINGLE_LISTING: 'SINGLE_LISTING',
  TRANSACTIONS: 'TRANSACTIONS',
  CREDIT_CARDS: 'CREDIT_CARDS',
  SECURITY: 'SECURITY',
  PERSONAL_INFORMATION: 'PERSONAL_INFORMATION',
  ADD_DESCRIPTION: 'ADD_DESCRIPTION',
  PAYMENT_METHOD: 'PAYMENT_METHOD',
  BRAND_PICKER: 'BRAND_PICKER',
  EDIT_CREDIT_CARD: 'EDIT_CREDIT_CARD',
  CHECKOUT: 'CHECKOUT',
  LEGITAPPCHECKOUT: 'LEGITAPPCHECKOUT',
  AUTH_SUCCESSFUL: 'AUTH_SUCCESSFUL',
  FOLLOWING_LISTING: 'FOLLOWING_LISTING',
  CHAT_SCREEN: 'CHAT_SCREEN',
  MESSENGER: 'MESSAGES',
  EXPERT_FEEDBACK_OPTIONS: 'EXPERT_FEEDBACK_OPTIONS',
  VOTES: 'VOTES',
  EXPERTS_PROFILE: 'EXPERTS_PROFILE',
  OUR_EXPERTS: 'OUR_EXPERTS',
  PUBLIC_PROFILES: 'PUBLIC_PROFILES',
  COMMENTS: 'COMMENTS',
  ADD_CUSTOM_BRAND: 'ADD_CUSTOM_BRAND',
  EXAMPLES: 'EXAMPLES',
  FORCE_LOGOUT: 'FORCE_LOGOUT',
  ALL_LISTINGS: 'ALL_LISTINGS',
  FOLLOWERS_LISTING: 'FOLLOWERS_LISTING',
  SEARCH_SCREEN: 'SEARCH_SCREEN',
  NOTIFICATION_MESSAGES: 'NOTIFICATION_MESSAGES',
  EDIT_INFORMATION: 'EDIT_INFORMATION',
  ADD_CARD: 'ADD_CARD',
  POST_GALLERY: 'POST_GALLERY',
  POST_IMAGE_COMMENT: 'POST_IMAGE_COMMENT',
  AUTHENTICATION_ADD_CARD: 'AUTHENTICATION_ADD_CARD',
  CONNECT_WALLET: 'CONNECT_WALLET',
  CONTACT_US: 'CONTACT_US',
  USER_SEARCH: 'USER_SEARCH',
  TSHIRT_SEARCH: 'TSHIRT_SEARCH',
  FREQUENTLY_ASKED_QUESTIONS: 'FREQUENTLY_ASKED_QUESTIONS',
  POST_LIKES: 'POST_LIKES',
  AIRESULT: 'AIRESULT',
  AITAGRESULT: 'AITAGRESULT',
  QR_CODE_SCREEN: 'QR_CODE_SCREEN',
  AI_SCAN: 'AI_SCAN',
  AI_SCAN_SCREEN: 'AI_SCAN_SCREEN',
  LEGIT_APP_ALLBRAND_CATEGORIES: 'LEGIT_APP_ALLBRAND_CATEGORIES',
  LEGIT_APP_ALLBRAND: 'LEGIT_APP_ALLBRAND',
  LEGIT_APP_ALLMODEL: 'LEGIT_APP_ALLMODEL',
  LEGIT_APP_NEW_AUTHENTICATION: 'LEGIT_APP_NEW_AUTHENTICATION',
  LEGIT_APP_GUIDELINE: 'LEGIT_APP_GUIDELINE',
};
export const icons = {
  FRONT_IMAGE: 'front_image',
  CLOSEUP_FRONT_IMAGE: 'closeup_front_image',
  BACK_IMAGE: 'back_image',
  FRONT_TAG_IMAGE: 'front_tag_image',
  BACK_TAG_IMAGE: 'back_tag_image',
  COPYRIGHT_IMAGE: 'copyright_image',
  CLOSEUP_BACK_IMAGE: 'closeup_back_image',
  LOWER_HEM_STITCHING_IMAGE: 'lower_hem_stitching_image',
  ARM_HEM_STITCHING_IMAGE: 'arm_hem_stitching_image',
  MESSENGER_UNREAD: 'messenger_unread',
  MESSENGER_READ: 'messenger_read',
  MAGNIFYING_GLASS: 'magnifying_glass',
  EXPERT_CERTIFIED_NFT: 'nftCertification',
  EXPERT_CERTIFIED: 'expertCertified',
  NOTIFICATIONS_READ: 'notifications_read',
  NOTIFICATIONS_UNREAD: 'notifications_unread',
  FAVICON: 'favicon',
  DASH: 'dash',
  CANCEL_X: 'cancel_x',
  CANCEL_X_2: 'cancel_x_2',
  PASS_ICON: 'pass_icon',
  PASS_ICON_2: 'pass_icon_2',
  PEOPLE_OUTLINE: 'people_outline',
  PRICE_TAG: 'price_tag',
  HOW_IT_WORKS: 'how_it_works',
  SEND: 'send',
  CHECKED_RADIO: 'checked_radio',
  UNCHECKED_RADIO: 'unchecked_radio',
  OUR_EXPERTS: 'our_experts',
  ADD: 'add',
  AUTHENTICATE_ICON: 'authenticate_icon',
  COMMENT_ICON: 'comment_icon',
  COMMUNITY_ICON: ' community_icon',
  CREDIT_CARD: 'credit_card',
  DRAW_MENU: 'draw_menu',
  EDIT_ICON: 'edit_icon',
  ELIPSIS_HORIZONTAL: 'elipsis_horizontal',
  ELIPSIS_VERTICAL: 'elipsis_vertical',
  HOME_ICON: 'home_icon',
  LOGOUT: 'logout',
  OPEN_EYE: 'open_eye',
  PERSON_ICON: 'person_icon',
  QUESTIONMARK_CIRCLE: 'questionmark_circle',
  RIGHT_PHOTO_ICON: 'right_photo_icon',
  SEARCH_ICON: 'search_icon',
  SHARE_ICON: 'share_icon',
  SUCCESS_CHECK_ICON: 'success_check_icon',
  TRASH_ICON: 'trash_icon',
  WALLET: 'wallet',
  WRONG_PHOTO_ICON: 'wrong_photo_icon',
  ARROW_RIGHT: 'arrow_right',
  ARROW_LEFT: 'arrow_left',
  EYE_OPEN: 'eye_open',
  EYE_CLOSED: 'eye_closed',
  PASSWORD_CORRECT: 'password_correct_icon',
  PASSWORD_FALSE: 'password_false_icon',
  CHEVRON_DOWN: 'chevron_down',
  TAKE_PHOTO: 'take_photo',
  UPLOAD_PHOTO: 'upload_photo',
  FEATURED_ICON: 'featured_icon',
  CHAT_BUBBLE: 'chatBubble',
};

export const passwordErrors = {
  MINIMUM_CHARACTERS: 'Password must be 8 characters or more',
  INCLUDE_NUMBER: 'Password must include a number',
  INCLUDE_SPECIAL_CHARACTER: 'Password must include a special Character',
  NO_SPACES: 'no spaces',
  REQUIRED: 'Password is required',
  CHECK_MINIMUM_CHARACTERS: '8 characters or more',
  CHECK_INCLUDE_NUMBER: 'at least 1 number',
  CHECK_INCLUDE_SPECIAL_CHARACTER: '1 special character or more',
  CHECK_NO_SPACES: 'no spaces',
};
export const logo = {
  Darkbg: require('./Images/logo-secondary.png'),
  Lightbg: require('./Images/logo.png'),
  DefunkdLogo: require('./Images/defunkdheaderlogo.png'),
  logoPrimary: require('./Images/logo-primary.png'),
  logoSecondary: require('./Images/logo-secondary.png'),
  legitAppLogo: require('./Images/logo-legit-app.png'),
  legitAppLogoText: require('./Images/ligit_app_text.jpg'),
};
export const images = {
  PLACEHOLDER: require('./Images/imageplaceholder.jpg'),
  PROFILE_PLACEHOLDER: require('./Images/avatar.png'),
  logo1: require('./Images/logo-only-2.jpg'),
  logo2: require('./Images/logo-only.png'),
  primaryPlaceholder: require('./Images/imageplaceholder.jpg'),
};
export const navLabels = {
  HOME_TABS: 'Home',
  CONNECT_WALLET: 'Connect wallet',
  MY_STUFF: 'My Wallet',
  MY_LEGITAPP_STUFF: 'LegitApp Wallet',
  AUTHENTICATE_TAB: 'Authenticate',
  AUTHENTICATE: 'Authentication Type',
  WALLET: 'Wallet',
  BILLING: 'Billing',
  HOWITWORKS: 'How It Works',
  PROFILE: 'Profile',
  LOGOUT: 'Logout',
  DELETE: 'Delete Account',
  ALL_ACTIVITY: 'Recent activity',
  FOLLOWING_FEED: 'Following',
  MY_SUBMISSIONS: 'My Submissions',
  MY_VOTES: 'My Votes',
  NOTIFICATIONS: 'Notifications',
  MY_AUTHENTICATIONS: 'My authentications',
  MY_LEGIT_APP_AUTHENTICATIONS: 'Legit App authentications',
  LEGITAPP_ORDER_DETAIL: 'Order Details',
  ALLBRAND_CATEGORIES: 'Brands and Categories',
  ALL_BRANDS: 'Brands',
  EXPERT_FEEDBACK_OPTIONS: 'Expert Feedback Options',
  ALL_CATEGORIES: 'Categories',
  ADD_PHOTOS: 'Add Photos',
  USER_PROFILE: 'My profile',
  TRANSACTIONS: 'Transactions',
  CREDIT_CARDS: 'Credit cards',
  SECURITY: 'Password Change',
  PERSONAL_INFORMATION: 'Personal information',
  ADD_DESCRIPTION: 'Add Description',
  PAYMENT_METHOD: 'Payment Method',
  EDIT_CREDIT_CARD: 'Edit Card',
  CHECKOUT: 'Checkout',
  FOLLOWING_LISTING: 'Following',
  AUTH_SUCCESSFUL: 'Authenticate (4/4)',
  CHAT_SCREEN: 'Message',
  MESSENGER: 'Message',
  OUR_EXPERTS: 'Our Experts',
  VOTES: 'Votes',
  EXPERTS_PROFILE: 'Experts Profile',
  PUBLIC_PROFILES: 'Profile',
  COMMENTS: 'Comments',
  ADD_CUSTOM_BRAND: 'Add a Brand',
  EXAMPLES: 'Images Examples',
  FORCE_LOGOUT: 'Force Logout',
  ALL_LISTINGS: 'All Listings',
  FOLLOWERS_LISTING: 'Followers',
  SEARCH_SCREEN: 'Search',
  NOTIFICATION_MESSAGES: 'Notifications',
  BRAND_PICKER: 'Select brand',
  EDIT_INFORMATION: 'Edit',
  ADD_CARD: 'Add card',
  POST_GALLERY: 'Gallery',
  POST_IMAGE_COMMENT: 'Comments',
  AUTHENTICATION_ADD_CARD: 'Add card',
  CONTACT_US: 'Contact Us',
  LIKES: 'Likes',
  AI_LENS_BETA: 'AI Lens Beta',
  SCAN_AI: 'Tag Scan',
  LEGIT_APP_CATEGORIES: 'Choose a Category',
};
export const allActivityScreenHeaders = {
  RECENT_BRANDS: 'Recent Brands',
  SEE_ALL_BRANDS: 'See All',
  LATEST_LISTINGS: 'Latest Listings',
  SEE_ALL_ACTIVITIES: 'See All',
  POPULAR_CATEGORIES: 'Popular Categories',
  SEE_ALL_CATEGORIES: 'See All',
  COMPLETE_PROFILE: 'Complete my profile',
  VISIT_HOW_IT_WORKS: `Visit 'How It Works'`,
  WELCOME_TO_AUTH8: 'Welcome to Legiteem8 Community!',
  ACCOUNT_CREATED: 'Account Created!',
  SKIP_FOR_NOW: 'Skip for now',
};
export const billingScreen = {
  CURRENCY: ' $',
  NO_TRANSACTIONS_YET: 'Your transaction history will appear here',
};

export const myVotedPostsScreen = {
  NO_POSTS: 'You have no posts',
};
export const ourExpertScreen = {
  NO_POSTS: 'There are no experts available at the moment.',
};
export const notificationsScreen = {
  IN_APP_NOTIFICATIONS: 'In-app notifications',
  PUSH_NOTIFICATIONS: 'Push notifications',
};
export const editCreditCardScreen = {
  ARE_YOU_SURE: 'Are you sure you want to delete this card?',
  YES_DELETE: 'Yes, delete',
  NO_CANCEL: 'No, Cancel',
  CARD_NAME: `Card holder's name`,
  SET_DEFAULT: 'Set as default',
  BRAND: 'Brand',
  CARD_NUMBER: 'Card number',
  EXPIRY_DATE: 'Expiry date',
  SAVE_CHANGES: 'Save changes',
  NAME_PLACEHOLDER: 'Type name here',
  SAVE_CARD: 'Save card',
  SOMETHING_WENT_WRONG: 'Something went wrong',
};
export const myAuthenticationScreen = {
  NO_POSTS: 'Your authentications will appear here',
};
export const messengerScreen = {
  NO_MESSGES: 'You have no chats',
  PROMPT: 'Are you sure you want to delete this chat?',
  CANCEL: 'Cancel',
  DELETE: 'Delete',
};
export const messageListingScreen = {
  SEPARATOR: '% accuracy |',
};
export const creditCardScreen = {
  HEADER: 'Saved credit cards',
  BUTTON_LABEL: 'View/Edit',
  DEFAULT: ' Default',
  ASTERISK: '* ',
  CANCEL: 'Cancel',
  NAME: ' Name',
  CARD_DETAILS: 'Card Details',
  CARD_NUMBER_PLACEHOLDER: '4242 4242 4242 4242',
  SAVE: 'Save',
  ADD_CARD: 'Add Card',
  NO_CARDS_SAVED: 'You have no cards saved. Please add one to see it here.',
};
export const loginScreen = {
  HEADER: 'Sign in',
  EMAIL_LABEL: 'Email',
  PASSWORD_LABEL: 'Password',
  BUTTON_LABEL: 'Sign in',
  REGISTER_BUTTON: 'Register',
  FORGOT_PASSWORD_BUTTON: 'Forgot Password?',
};
export const authenticationTypeScreen = {
  HEADER: 'Submit your T-shirt within 3 minutes.',
  SUBTITLE:
    "You'll need photos of the t-shirt or you can take them during the submission process. For our certified tiers, we require that you have the tee in your possession for photos.",
  HEADING_ONE: 'Select authentication type:',
  HEADING_TWO: 'Contribute to our database',
  UPLOAD_IMAGE_TITLE: 'Upload photos or take pictures for Expert verification:',
  VIEW_EXAMPLE: 'Best Practices for Photo Submissions',
  NEXT_BUTTON: 'Next: Add description',
  REQUIRED_PHOTOS: 'Required photos:',
  OPTIONAL_PHOTOS: 'Optional photos:',
  ASTERISk: '*',
  ADD_IMAGE: ' + Add',
  SEARCH_PLACEHOLDER: 'Search',
  PASS: 'Pass',
  FAIL: 'Fail',
  YOUR_APPRAISAL: 'Your appraisal',
  COMMENT: 'Comment',
  SUBMIT: 'Submit',
  PAYMENT_METHOD: 'Payment method',
  CHANGE: 'Change',
  PAY_NOW: 'Pay now',
  TOTAL: 'Total',
  TAX: 'Tax',
  FREE: 'Free',
  CANCEL: 'Cancel',
  NAME_ON_CARD: 'Name on card',
  CARD_DETAILS: 'Card Details',
  CARD_NUMBER_PLACEHOLDER: '4242 4242 4242 4242',
  SAVE: 'Save',
  ADD_CARD: 'Add Card',
  NO_CARDS_SAVED: 'You have no cards saved. Please add one to see it here.',
  AUTHENTICATION_SUCCESSFUL_MESSAGE: [
    'Your submission has been received and now appears in your Legiteem8 wallet.',
    "However, it must be reviewed by an admin prior to being visible to the rest of the community. This usually takes about 2 hours and you'll receive a notification once it appears in our feed.",
  ],
  AUTHENTICATION_RESUBMISSION_SUCCESSFUL_MESSAGE: [
    'Your re-submission has been received and now appears in your Legiteem8 wallet.',
    "However, it must be reviewed by an admin prior to being visible to the rest of the community. This usually takes about 2 hours and you'll receive a notification once it appears in our feed.",
  ],
  AUTHENTICATION_SUCCESSFUL_MESSAGE_EXPERT: [
    'Your paid submission has been received and now appears in your Legiteem8 wallet.',
    "It will be thoroughly reviewed by an expert prior to becoming public (unless you requested a private service.) This usually transpires within 24 hours and you'll receive a notification once the process is complete.",
  ],
  AUTHENTICATION_RESUBMISSION_SUCCESSFUL_MESSAGE_EXPERT: [
    'Your paid submission has been received and now appears in your Legiteem8 wallet.',
    "It will be thoroughly reviewed by an expert prior to becoming public (unless you requested a private service.) This usually transpires within 24 hours and you'll receive a notification once the process is complete.",
  ],
  AUTHENTICATION_SUCCESSFUL_MESSAGE_TAG: [
    "Your contribution has been received and will be reviewed by an admin prior to being visible to the rest of the community. This usually takes about 2 hours and you'll receive a notification once your tag appears in our database.",
  ],
  AUTHENTICATION_RESUBMISSION_SUCCESSFUL_MESSAGE_TAG: [
    "Your contribution has been received and will be reviewed by an admin prior to being visible to the rest of the community. This usually takes about 2 hours and you'll receive a notification once your tag appears in our database.",
  ],
  TRANSACTION_SUCCESSFUL: 'Submission Successful!',
  CONFIRMATION: 'Confirmation: ',
  BACK_TO_HOME: 'Back to Home screen',
  ADD_NEW_CARD: 'Add new credit card',
};
export const addDescriptionScreen = {
  HEADER: 'Add product description:',
  BUTTON_LABEL: 'Submit',
  NAME_OF_SHIRT: 'Name of shirt',
  BRAND: 'Brand',
  PIT_TO_PIT: 'Pit to Pit (Inches)',
  REAR_TO_BOTTOM: 'Rear Collar to Bottom',
  STITCHING: 'Stitching',
  RN_NUMBER: 'RN Number',
  CATEGORY: 'Category',
  DECADE: 'Decade',
  CONDITION: 'Condition',
  LABEL_SIZE: 'Label size',
  MATERIAL: 'Material',
  CONDITION_DESCRIPTION: 'Condition description',
  PROVENANCE: 'Provenance',
  ASTERISK: '*',
  SINGLE: 'Single',
  DOUBLE: 'Double',
  SOMETHING_WENT_WRONG: 'Something went wrong',
  DUAL: 'Dual',
  OPTIONAL: ' (optional)',
  TYPE_TO_SEARCH: 'Search',
  FIELD_ERROR_MESSAGES: {
    NAME_REQUIRED: 'The name of the shirt is required',
    BRAND_REQUIRED: 'The Brand of the shirt is required',
    PIT_TO_PIT_REQUIRED: 'The pit to pit measurement of the shirt is required',
    REAR_COLLAR_REQUIRED:
      'The react collar to bottom measurement of the shirt is required',
    STITCHING_REQUIRED: 'The react stitching of the shirt is required',
    CATEGORY_REQUIRED: 'The react category of the shirt is required',
    DECADE_REQUIRED: 'The decade of when the shirt was released is required',
    CONDITION_REQUIRED: 'The condition of the shirt is required',
    PROVENANCE_REQUIRED: 'The provenance of the shirt  is required',
  },
};
export const registerScreen = {
  SCREEN1_HEADER: 'Create Account (1/3)',
  SCREEN2_HEADER: 'Create Account (2/3)',
  SCREEN3_HEADER: 'Create Account (3/3)',
  SCREEN1_TITLE: 'Start with email',
  SCREEN2_TITLE: 'Email Verification',
  SCREEN3_TITLE: 'Log in info',
  USERNAME: 'Username',
  SCREEN1_SUBTITLE:
    'We\'ll send a verification code to your email. Please check your spam folders and be sure to mark it "not spam".',
  SCREEN2_SUBTITLE:
    'We’ve emailed you a 6-digit long verification code. Please check your inbox.',
  SCREEN3_SUBTITLE: 'Set Password',
  SCREEN1_EMAIL_LABEL: 'Your Email',
  SCREEN2_EMAIL_LABEL: 'Verification Code',
  SCREEN1_BUTTON_LABEL: 'Verify Email',
  SCREEN2_BUTTON_LABEL: 'Continue',
  SCREEN3_BUTTON_LABEL: 'Create Account',
  CODE_EXPIRES: '(Code expires in 15 minutes)',
  NOT_RECEIVED_CODE: `Didn't get the code?`,
  RESEND_CODE: 'Resend',
};

export const forgotPasswordScreen = {
  HEADER: 'Forgot Password',
  EMAIL_LABEL: 'Email',
  SUBTITLE: 'We will send you an email with a code to validate your email',
  BUTTON_LABEL: 'Send Email Verification Code',
  SCREEN1_TITLE: 'Verify Email',
};

export const onBoardingScreen = {
  SCREEN_ONE_TITLE:
    'Join a growing community of people passionate about T-Shirts',
  SCREEN_TWO_TITLE:
    'Have your t-shirt verified by the community and our experts',
  SCREEN_THREE_TITLE: `Contribute by voting and estimating other's products authenticity and value`,
  NEXT_BUTTON: 'Next',
  BACK_BUTTON: 'Back',
  SKIP_BUTTON: 'Skip',
};
export const resetPasswordScreen = {
  HEADER: 'Password Change',
  PASSWORD: 'New Password',
  CONFIRM_PASSWORD: 'Re-type New Password',
  SUBTITLE: 'Password change successful!',
  BUTTON_LABEL: 'Change Password',
  PROCEED_TO_LOGIN_BUTTON: 'Go to Login',
  TITLE: 'Change Password',
};
export const validateCodeScreen = {
  HEADER: 'Confirm Code',
  TITLE: 'Verify Email',
  SUBTITLE:
    'Please check your inbox for a 8-digit long code and fill it below.',
  NEXT_BUTTON: 'Next: Password Reset',
  PLACEHOLDER: 'Type here',
  CODE: 'Code',
};
export const fa2ValidateCodeScreen = {
  HEADER: '2 Factor Verification',
  TITLE: 'Verify Email',
  SUBTITLE:
    'Please check your inbox for a 8-digit long code and fill it below.',
  NEXT_BUTTON: 'Next: Login',
  ENABLE2FA_BUTTON: 'Enable 2FA',
  PLACEHOLDER: 'Type here',
  CODE: 'Code',
};
export const initialScreen = {
  POWERED_BY: 'Powered By:',
  CREATE_ACCOUNT: 'Create Account',
  ALREADY_HAVE_ACCOUNT: 'Already have an account?',
  SIGN_IN: 'Sign in',
};
export const logoutScreen = {
  PROMPT: 'Are you sure you want to log out?',
  BUTTON_LABEL: 'Yes, log me out',
  MY_PUBLIC_PROFILE: 'My Public Profile',
  UPDATE_YOUR_PROFILE: 'Please update your profile',
};
export const followingFeedScreen = {
  REAL: 'Real',
  FAKE: 'Fake',
  EXPERT_APPRAISAL: 'Appraisal: $',
  COMMUNITY_APPRAISAL: 'Avg est: $',
  VOTE: 'Vote: ',
  VOTED_ON_A_SHIRT: 'voted on a t-shirt',
  NO_POSTS: 'Posts from your followings will appear here',
  UPLOADED_A_SHIRT: 'uploaded a t-shirt',
};
export const searchScreen = {
  HEADER: 'Search',
  CANCEL: 'Cancel',
  PLACEHOLDER: 'Search',
};
export const voteScreen = {
  HEADER: 'Feed Votes',
  SEE_ALL_ACTIVITIES: 'See All Activity',
};
export const mySubmissionsScreen = {
  HEADER: 'Submissions',
};
export const userProfileScreen = {
  FOLLOWING: 'Following',
  FOLLOWERS: 'Followers',
  ACCURACY: 'Accuracy',
  FOLLOW: 'Follow',
  MESSAGE: 'Message',
  NO_POSTS_YET: 'You have no posts yet.',
  AUTHENTICATION_COUNT: 'Authentications',
  PLACEHOLDER: 'Reply',
  SOMETHING_WENT_WRONG: 'Something went wrong',
};
export const profileSettingsScreen = {
  HEADER: 'Profile',
  PERSONAL_INFORMATION: 'Personal information',
  SECURITY: 'Security',
  ENABLE_2FA: 'Enable 2FA',
  PASSWORD: 'New Password',
  CURRENT_PASSWORD: 'Type Current Password',
  CONFIRM_PASSWORD: 'Re-Type New Password',
  SUBTITLE: 'Password change successful!',
  BUTTON_LABEL: 'Update password',
  PROCEED_TO_LOGIN_BUTTON: 'Ok',
  FIRST_NAME: 'First name',
  LAST_NAME: 'Last name',
  USERNAME: 'Username',
  EMAIL: 'Email',
  SAVE_CHANGES: 'Save Changes',
  CANCEL: 'Cancel',
  EDIT: 'Edit',
  CHANGE: 'Change',
  REMOVE: 'Remove',
  BIO: 'Bio',
  PHONE: 'Phone',
  COUNTRY_CODE: 'Country code',
  SOCIAL_MEDIA_LINK: 'Social media link',
};

export const singleBrandScreen = {
  NO_POSTS: 'There are no posts yet.',
  SELECT_ALL: 'Select all',
  PENDING: 'undecided',
  PASS: 'Pass',
  FAIL: 'Fail',
  UNDETERMINED: 'Undetermined',
};
export const singleCategoryScreen = {
  NO_POSTS: 'There are not posts yet',
  SELECT_ALL: 'Select all',
  PENDING: 'Pending',
  PASS: 'Pass',
  FAIL: 'Fail',
  UNDETERMINED: 'Undetermined',
};
export const singleListingScreen = {
  BRAND: 'Brand',
  DESCRIPTION_HEADER: 'Description',
  CONDITION_DESCRIPTION: 'Condition Description',
  CONDITION: 'Condition',
  PIT_TO_PIT: 'Pit to Pit',
  REAR_COLLAR: 'Rear to Collar',
  LABEL_SIZE: 'Label size',
  STITCHING: 'Stitching',
  MATERIAL: 'Material',
  CATEGORY: 'Category',
  DECADE: 'Decade',
  PROVENANCE: 'Provenance',
  INCHES: ' inches',
  VOTE: 'Vote',
  AVG_COMMUNITY_APPRAISAL: 'Average Community Appraisal',
  AVG_COMMUNITY_VOTE: 'Average Community Vote',
  EXPERT_VOTES: 'Experts Voted: ',
  CURRENCY: '$',
  ID: 'ID:',
  AT: '@',
  BY: 'By:',
  UPLOADED: 'Uploaded: ',
  VIEW_GALLERY: 'View Gallery ',
  COMMENTS: ' comments',
  PASS: 'Pass',
  FAIL: 'Fail',
  GET_NFT: 'Get NFT',
  YOU_VOTED: 'You Voted ',
  YOUR_VOTE: 'Your Vote',
  YOUR_APPRAISAL: 'Your Appraisal',
  COMMENT: 'Comment',
  SUBMIT: 'Submit',
  SOMETHING_WENT_WRONG: 'Something went wrong.',
};
export const errorCodes = {
  EMAIL_ADDRESS_NOT_FOUND: 'EMAIL_ADDRESS_NOT_FOUND',
  EMAIL_ADDRESS_ALREADY_EXIST: 'EMAIL_ADDRESS_ALREADY_EXIST',
  ACCOUNT_DOES_NOT_EXISTS: 'ACCOUNT_DOES_NOT_EXISTS',
  INVALID_CODE: 'INVALID_CODE',
  INVALID_EMAIL_OR_PASSWORD: 'INVALID_EMAIL_OR_PASSWORD',
  CODE_DOES_NOT_EXIST: 'CODE_DOES_NOT_EXIST',
  INVALID_TOKEN: 'INVALID_TOKEN',
  INVALID_REFRESH_TOKEN: 'INVALID_REFRESH_TOKEN',
  IFRAME_BLOCKED: 'IFRAME_BLOCKED',
  INVALID_URL: 'INVALID_URL',
  SYNC_CODE_ALREADY_EXISTS: 'SYNC_CODE_ALREADY_EXISTS',
  ACCOUNT_NOT_VERIFIED: 'ACCOUNT_NOT_VERIFIED',
};

export const persistent = {
  ACCESS_TOKEN: 'ACCESS_TOKEN',
  REFRESH_TOKEN: 'REFRESH_TOKEN',
  USER_ID: 'USER_ID',
  IS_2FA: 'IS_2FA',
  EMAIL_ID: 'EMAIL_ID',
  PLATFORM: 'PLATFORM',
  FCM_TOKEN: 'FCM_TOKEN',
  TEMPORARY_NAVIGATION_STORE: 'TEMPORARY_NAVIGATION_STORE',
  CURRENT_CONVERSATION_USER_ID: 'CURRENT_CONVERSATION_USER_ID',
  IS_CONVERSATION_LIST_FOCUSED: 'IS_CONVERSATION_LIST_FOCUSED',
  CAN_FORCE_LOGOUT: 'CAN_FORCE_LOGOUT',
  ALLOW_DIRECT_MESSAGE_NOTIFICATION: 'ALLOW_DIRECT_MESSAGE_NOTIFICATION',
  ALLOW_POST_REACTION_NOTIFICATION: 'ALLOW_POST_REACTION_NOTIFICATION',
  ALLOW_AUTHENTICATION_SUBMISSION_STATUS_NOTIFICATION:
    'ALLOW_AUTHENTICATION_SUBMISSION_STATUS_NOTIFICATION',
  ALLOW_FOLLOW_STATUS_NOTIFICATION: 'ALLOW_FOLLOW_STATUS_NOTIFICATION',
  ALLOW_IN_APP_DIRECT_MESSAGE_NOTIFICATION:
    'ALLOW_IN_APP_DIRECT_MESSAGE_NOTIFICATION',
  ALLOW_IN_APP_POST_REACTION_NOTIFICATION:
    'ALLOW_IN_APP_POST_REACTION_NOTIFICATION',
  ALLOW_IN_APP_AUTHENTICATION_SUBMISSION_STATUS_NOTIFICATION:
    'ALLOW_IN_APP_AUTHENTICATION_SUBMISSION_STATUS_NOTIFICATION',
  ALLOW_IN_APP_FOLLOW_STATUS_NOTIFICATION:
    'ALLOW_IN_APP_FOLLOW_STATUS_NOTIFICATION',
  WAS_JUST_LOGGED_OUT: 'WAS_JUST_LOGGED_OUT',
  COUPON_CODE: 'COUPON_CODE',
  REDIRECT_NAVIGATION: 'REDIRECT_NAVIGATION',
};
export const apiEndPoint = {
  REFRESH_TOKEN: '/member/api/refresh-token',
  LOGIN: '/member/api/login',
  VERIFY_2FA_LOGIN: '/member/login/2fa-code',
  REGISTER_SEND_CODE: '/member/api/register-send-verification',
  REGISTER_VERIFY_CODE: '/member/api/register-verify-code',
  SIGNUP: '/member/api/register',
  VERIFY_ACCOUNT: 'verify-account',
  FORGOT_PASSWORD: '/member/api/forgot',
  VALIDATE_CODE: '/member/api/validate-reset-code',
  RESET_PASSWORD: '/member/api/reset-password',
  MOBILE_APP_DETAILS: '/global/api/mobile-app-details',
  SEND_2FA_VALIDATE_CODE: '/member/2fa/send-verification-code',
  SEND_2FA_DISABLED: '/member/2fa/disable-2fa',
  SEND_2FA_ENABLED: '/member/2fa/enable/',
  DELETE_ACCOUNT: '/global/api/delete-user',
  EXPERT_QUEUE: '/expert/api/queue/0',
};

export const IMAGE_TYPES = Object.freeze({
  front: 'front',
  back: 'back',
  front_tag: 'front_tag',
  back_tag: 'back_tag',
  closeup_front: 'closeup_front',
  closeup_back: 'closeup_back',
  copyright: 'copyright',
  arm_hem_stitching: 'arm_hem_stitching',
  lower_hem_stitching: 'lower_hem_stitching',
  extra: 'extra',
});

export const APOLLO_CACHE_IDS = Object.freeze({
  getPosts_HOME_TAB: 'getPosts_HOME_TAB',
  getPosts_ALL_LISTINGS: 'getPosts_ALL_LISTINGS',
  getPosts_MY_AUTHENTICATIONS: 'getPosts_MY_AUTHENTICATIONS',
  getPosts_SEARCH: 'getPosts_SEARCH',
  getPosts_POSTS_BY_CATEGORY: 'getPosts_POSTS_BY_CATEGORY',
  getPosts_POSTS_BY_BRAND: 'getPosts_POSTS_BY_BRAND',
  getBrands_USE_PAGINATED_BRANDS: 'getBrands_USE_PAGINATED_BRANDS',
  getBrands_FILTER_BUTTONS: 'getBrands_FILTER_BUTTONS',
  getUsers_SEARCH: 'getUsers_SEARCH',
  getUsers_AUTOCOMPLETE: 'getUsers_AUTOCOMPLETE',
  getPosts_POSTS_BY_USER: 'getPosts_POSTS_BY_USER',
  getPosts_LEGITAPP_AUTHENTICATIONS: 'getPosts_LEGITAPP_AUTHENTICATIONS',
});
