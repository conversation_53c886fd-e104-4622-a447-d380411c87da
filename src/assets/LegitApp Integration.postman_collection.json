{"info": {"_postman_id": "f9430e8c-0a49-40ab-8994-c4b385c5ac34", "name": "LegitApp Integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "305190"}, "item": [{"name": "Get Categories", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetCategories {\n  getLegitAppCategories {\n    success\n    message\n    data {\n      id\n      title\n      description\n    }\n  }\n}"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Get Categories", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetCategories {\n  getLegitAppCategories {\n    success\n    message\n    data {\n      id\n      title\n      description\n    }\n  }\n}"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 12:06:44 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"2d0-Ngyhbo2YD0MuJNeGbbKFi9rBQTg\""}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"getLegitAppCategories\": {\n            \"success\": true,\n            \"message\": null,\n            \"data\": [\n                {\n                    \"id\": 1,\n                    \"title\": \"Sneakers\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 2,\n                    \"title\": \"Streetwear\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 3,\n                    \"title\": \"Toys & Figures\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 4,\n                    \"title\": \"Luxury Handbags\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 5,\n                    \"title\": \"Luxury Clothing\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 6,\n                    \"title\": \"Luxury Shoes\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 7,\n                    \"title\": \"Luxury Accessories\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 8,\n                    \"title\": \"Product Code Checking\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 9,\n                    \"title\": \"Luxury Watches\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 10,\n                    \"title\": \"Trading Cards\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 11,\n                    \"title\": \"Cosmetic Products\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 12,\n                    \"title\": \"Vintage T-Shirts\",\n                    \"description\": null\n                }\n            ]\n        }\n    }\n}"}]}, {"name": "Get Brands", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVkZW50aWFsX2lkIjo3Mzc5LCJ1c2VyIjp7ImlkIjo3Mzc5fSwicm9sZV9pZCI6MiwiaWF0IjoxNzQ2Njk2MTQ1LCJleHAiOjE3NDczMDA5NDV9.DXxjilEmJNNA6Y0-c_W3nFIteGDLKPdQ3_eZwJDMCio", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetBrands($categoryId: Int!) {\\n  getLegitAppBrands(categoryId: $categoryId) {\\n    success\\n    message\\n    data {\\n      id\\n      title\\n      description\\n    }\\n  }\\n}\",\n  \"variables\": {\n    \"categoryId\": 8\n  }\n}"}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Get Brands", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetBrands($categoryId: Int!) {\n  getLegitAppBrands(categoryId: $categoryId) {\n    success\n    message\n    data {\n      id\n      title\n      description\n    }\n  }\n}", "variables": {"categoryId": 1}}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 12:07:01 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"57d-CuPnvehn6ZqSV/StBLSx3UZGdow\""}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"getLegitAppBrands\": {\n            \"success\": true,\n            \"message\": null,\n            \"data\": [\n                {\n                    \"id\": 3,\n                    \"title\": \"Adidas\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1,\n                    \"title\": \"Air Jordan\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 261,\n                    \"title\": \"ANTA\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 155,\n                    \"title\": \"Asics\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 10,\n                    \"title\": \"BAPE\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 5,\n                    \"title\": \"Converse\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 13,\n                    \"title\": \"FEAR OF GOD\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 152,\n                    \"title\": \"Fila\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 159,\n                    \"title\": \"Hoka\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 153,\n                    \"title\": \"Li-Ning\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 157,\n                    \"title\": \"<PERSON><PERSON>\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 209,\n                    \"title\": \"MLB\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 150,\n                    \"title\": \"New Balance\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 2,\n                    \"title\": \"<PERSON>\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 400,\n                    \"title\": \"On\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 262,\n                    \"title\": \"Onitsuka Tiger\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 6,\n                    \"title\": \"Puma\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 7,\n                    \"title\": \"Reebok\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 154,\n                    \"title\": \"Revenge X Storm\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 158,\n                    \"title\": \"Rick Owens\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 236,\n                    \"title\": \"Salomon\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 260,\n                    \"title\": \"Skechers\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 216,\n                    \"title\": \"Timberland\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 151,\n                    \"title\": \"Under Armour\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 8,\n                    \"title\": \"Vans\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 191,\n                    \"title\": \"XVESSEL\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 156,\n                    \"title\": \"Y-3\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 4,\n                    \"title\": \"Yeezy\",\n                    \"description\": null\n                }\n            ]\n        }\n    }\n}"}]}, {"name": "Get Models", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetModels($categoryId: Int!, $brandId: Int!) {\n  getLegitAppModels(categoryId: $categoryId, brandId: $brandId) {\n    success\n    message\n    data {\n      id\n      title\n      description\n    }\n  }\n}", "variables": {"categoryId": 1, "brandId": 1}}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Get Models", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetModels($categoryId: Int!, $brandId: Int!) {\n  getLegitAppModels(categoryId: $categoryId, brandId: $brandId) {\n    success\n    message\n    data {\n      id\n      title\n      description\n    }\n  }\n}", "variables": {"categoryId": 1, "brandId": 1}}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 12:07:31 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"96e-DUzIlTU4ltVOB/nbGG8TtkJ4W4U\""}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"getLegitAppModels\": {\n            \"success\": true,\n            \"message\": null,\n            \"data\": [\n                {\n                    \"id\": 419,\n                    \"title\": \"Future\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1,\n                    \"title\": \"Jordan 1\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 13,\n                    \"title\": \"Jordan 10\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 14,\n                    \"title\": \"Jordan 11\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 15,\n                    \"title\": \"Jordan 12\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 16,\n                    \"title\": \"Jordan 13\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 17,\n                    \"title\": \"Jordan 14\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 18,\n                    \"title\": \"Jordan 15\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 19,\n                    \"title\": \"Jordan 16\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 20,\n                    \"title\": \"Jordan 17\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 21,\n                    \"title\": \"Jordan 18\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 22,\n                    \"title\": \"Jordan 19\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 2,\n                    \"title\": \"Jordan 2\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 23,\n                    \"title\": \"Jordan 20\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 24,\n                    \"title\": \"Jordan 21\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 25,\n                    \"title\": \"<PERSON> 22\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 26,\n                    \"title\": \"Jordan 23\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 27,\n                    \"title\": \"<PERSON> 24\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 28,\n                    \"title\": \"Jordan 25\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 29,\n                    \"title\": \"<PERSON> 26\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 30,\n                    \"title\": \"Jordan 27\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 31,\n                    \"title\": \"Jordan 28\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 32,\n                    \"title\": \"Jordan 29\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 3,\n                    \"title\": \"Jordan 3\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 33,\n                    \"title\": \"Jordan 30\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 34,\n                    \"title\": \"Jordan 31\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 35,\n                    \"title\": \"Jordan 32\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 36,\n                    \"title\": \"Jordan 33\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 37,\n                    \"title\": \"Jordan 34\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1237,\n                    \"title\": \"Jordan 35\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1233,\n                    \"title\": \"Jordan 36\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1238,\n                    \"title\": \"Jordan 37\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1239,\n                    \"title\": \"Jordan 38\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1236,\n                    \"title\": \"Jordan 39\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 7,\n                    \"title\": \"Jordan 4\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 8,\n                    \"title\": \"Jordan 5\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 9,\n                    \"title\": \"Jordan 6\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 10,\n                    \"title\": \"Jordan 7\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 11,\n                    \"title\": \"Jordan 8\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 12,\n                    \"title\": \"Jordan 9\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 420,\n                    \"title\": \"Jordan OFF-WHITE\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1232,\n                    \"title\": \"Jumpman\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 418,\n                    \"title\": \"Legacy 312\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1235,\n                    \"title\": \"Luka\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 38,\n                    \"title\": \"Other\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 416,\n                    \"title\": \"Packs\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 417,\n                    \"title\": \"Spizike\",\n                    \"description\": null\n                },\n                {\n                    \"id\": 1234,\n                    \"title\": \"Tatum\",\n                    \"description\": null\n                }\n            ]\n        }\n    }\n}"}]}, {"name": "Get Authentication set (turn around time)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAuthenticationSet($categoryId: Int!, $brandId: Int!, $modelId: Int!) {\n    getLegitAppAuthenticationSet(categoryId: $categoryId, brandId: $brandId, modelId: $modelId) {\n    success\n    message\n    data {\n      id\n      turn_around_time_list {\n        id\n        title\n        subtitle\n        minute\n        credit\n      }\n      guideline {\n        title\n        subtitle\n        image_urls\n      }\n      placeholder {\n        title\n        image_url\n        example_image_url\n        description\n        required\n      }\n    }\n  }\n}\n\n# Variables:\n# {\n#   \"categoryId\": 1,\n#   \"brandId\": 1,\n#   \"modelId\": 1\n# }", "variables": "{\n    \"categoryId\":8,\n    \"brandId\": 178,\n    \"modelId\":891\n}"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Get Authentication set", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAuthenticationSet($categoryId: Int!, $brandId: Int!, $modelId: Int!) {\n    getLegitAppAuthenticationSet(categoryId: $categoryId, brandId: $brandId, modelId: $modelId) {\n    success\n    message\n    data {\n      id\n      turn_around_time_list {\n        id\n        title\n        subtitle\n        minute\n        credit\n      }\n      guideline {\n        title\n        subtitle\n        image_urls\n      }\n      placeholder {\n        title\n        image_url\n        example_image_url\n        description\n        required\n      }\n    }\n  }\n}\n\n# Variables:\n# {\n#   \"categoryId\": 1,\n#   \"brandId\": 1,\n#   \"modelId\": 1\n# }"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": ""}]}, {"name": "Get Authentications", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVkZW50aWFsX2lkIjo3Mzc5LCJ1c2VyIjp7ImlkIjo3Mzc5fSwicm9sZV9pZCI6MiwiaWF0IjoxNzQ2Njk2MTQ1LCJleHAiOjE3NDczMDA5NDV9.DXxjilEmJNNA6Y0-c_W3nFIteGDLKPdQ3_eZwJDMCio", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAuthentications {\n  getLegitAppAuthentications {\n    success    \n    data {\n      id\n      authentication_id\n      status\n      result\n      created_at\n      request_data\n      images {\n        id\n        s3_url\n        legit_app_url\n        asset_link\n      }\n    }\n  }\n}"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Get Authentications", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAuthentications {\n  getLegitAppAuthentications {\n    success\n    data {\n      id\n      authentication_id\n      status\n      result\n      created_at\n      images {\n        id\n        s3_url\n        legit_app_url\n        asset_link\n      }\n    }\n  }\n}"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 13:32:26 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"390-sxrm+rBxk/tWVtGO8eetpS8NiKw\""}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"getLegitAppAuthentications\": {\n            \"success\": true,\n            \"data\": [\n                {\n                    \"id\": \"1\",\n                    \"authentication_id\": \"8965818319293466\",\n                    \"status\": 1,\n                    \"result\": null,\n                    \"created_at\": \"2025-03-07T13:16:00.000Z\",\n                    \"images\": [\n                        {\n                            \"id\": \"17\",\n                            \"s3_url\": \"https://s3.amazonaws.com/com.images.legiteem8/bf9639ba-0f6d-4846-bbd9-9b7fa5cfbb52-20250307131557%40blurHash%3DSjNJWTIjXzMwMHRSMDB4dQ%3D%3D.png\",\n                            \"legit_app_url\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20250307c8e0b9664c1414aa.png\",\n                            \"asset_link\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20250307c8e0b9664c1414aa.png\"\n                        },\n                        {\n                            \"id\": \"18\",\n                            \"s3_url\": \"https://s3.amazonaws.com/com.images.legiteem8/7ed521c9-b139-4ecc-8438-80cf1af2179c-20250307131559%40blurHash%3DSjNJWTIjXzMwMHRSMDB4dQ%3D%3D.png\",\n                            \"legit_app_url\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202503076caa1033f5f882ed.png\",\n                            \"asset_link\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202503076caa1033f5f882ed.png\"\n                        }\n                    ]\n                }\n            ]\n        }\n    }\n}"}, {"name": "Get Authentications", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAuthentications {\n  getLegitAppAuthentications {\n    success\n    data {\n      id\n      authentication_id\n      status\n      result\n      created_at\n      images {\n        id\n        s3_url\n        legit_app_url\n        asset_link\n      }\n    }\n  }\n}"}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 13:36:11 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"481-xhc7MhyN2mt0FSrhUC7cYk32Kh0\""}, {"key": "Set-<PERSON><PERSON>", "value": "session=s%3ADSTy6dkxNYqkAvcitcMwCk-y1rfDdTkd.UnE1RnzgSPzfx1%2Fmn%2BGEcU6qEh7mG7o3i5uifZIkkhw; Path=/; Expires=Fri, 07 Mar 2025 14:36:11 GMT; HttpOnly; Secure; SameSite=Strict"}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"getLegitAppAuthentications\": {\n            \"success\": true,\n            \"data\": [\n                {\n                    \"id\": \"1\",\n                    \"authentication_id\": \"8965818319293466\",\n                    \"status\": 1,\n                    \"result\": null,\n                    \"created_at\": \"2025-03-07T13:16:00.000Z\",\n                    \"images\": [\n                        {\n                            \"id\": \"17\",\n                            \"s3_url\": \"https://s3.amazonaws.com/com.images.legiteem8/bf9639ba-0f6d-4846-bbd9-9b7fa5cfbb52-20250307131557%40blurHash%3DSjNJWTIjXzMwMHRSMDB4dQ%3D%3D.png\",\n                            \"legit_app_url\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20250307c8e0b9664c1414aa.png\",\n                            \"asset_link\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20250307c8e0b9664c1414aa.png\"\n                        },\n                        {\n                            \"id\": \"18\",\n                            \"s3_url\": \"https://s3.amazonaws.com/com.images.legiteem8/7ed521c9-b139-4ecc-8438-80cf1af2179c-20250307131559%40blurHash%3DSjNJWTIjXzMwMHRSMDB4dQ%3D%3D.png\",\n                            \"legit_app_url\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202503076caa1033f5f882ed.png\",\n                            \"asset_link\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202503076caa1033f5f882ed.png\"\n                        }\n                    ]\n                },\n                {\n                    \"id\": \"2\",\n                    \"authentication_id\": \"7917514365342622\",\n                    \"status\": 2,\n                    \"result\": \"{\\\"event_type\\\":\\\"authentication.completed\\\",\\\"authentication_id\\\":\\\"7917514365342622\\\",\\\"result\\\":\\\"inconclusive\\\"}\",\n                    \"created_at\": \"2025-03-07T13:16:00.000Z\",\n                    \"images\": []\n                }\n            ]\n        }\n    }\n}"}]}, {"name": "Get Single Authentication", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAuthentication($id: ID!) {\n  getLegitAppAuthentication(id: $id) {\n    success\n    data {\n      id\n      authentication_id\n      status\n      result\n      created_at\n      images {\n        id\n        s3_url\n        legit_app_url\n        asset_link\n      }\n    }\n  }\n}", "variables": {"id": "1"}}}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": []}, {"name": "Submit Authentication with Images", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "operations", "value": "{\n  \"query\": \"mutation SubmitAuthentication($input: LegitAppAuthenticationInput!) { submitLegitAppAuthentication(input: $input) { success message data { id authentication_id status created_at images { id s3_url legit_app_url asset_link image_type } } } }\",\n  \"variables\": {\n    \"input\": {\n      \"brand_id\": 1,\n      \"category_id\": 1,\n      \"model_id\": 1,\n\n   \"category_name\":\"name\",\n    \"brand_name\":\"name\",\n    \"model_name\":\"name\",\n\"authentication_set_id\": 5,\n      \"turnaround_time_id\": 48,\n      \"files\": [null, null],\n      \"user_remark\": \"Please verify this item\"\n    }\n  }\n}", "type": "text"}, {"key": "map", "value": "{\n  \"0\": [\"variables.input.files.0\"],\n  \"1\": [\"variables.input.files.1\"]\n}", "type": "text"}, {"key": "0", "type": "file", "src": "MqEN1JWIM/Screenshot 2025-03-07 at 11.47.25.png"}, {"key": "1", "type": "file", "src": "o0zJOnHLn/Screenshot 2025-03-07 at 11.47.25.png"}, {"key": "", "value": "", "type": "text"}]}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Submit Authentication with Images", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "operations", "value": "{\n  \"query\": \"mutation SubmitAuthentication($input: LegitAppAuthenticationInput!) { submitLegitAppAuthentication(input: $input) { success message data { id authentication_id status created_at images { id s3_url legit_app_url asset_link image_type } } } }\",\n  \"variables\": {\n    \"input\": {\n      \"brand_id\": 1,\n      \"category_id\": 1,\n      \"model_id\": 1,\n      \"authentication_set_id\": 5,\n      \"turnaround_time_id\": 48,\n      \"files\": [null, null],\n      \"user_remark\": \"Please verify this item\"\n    }\n  }\n}", "type": "text"}, {"key": "map", "value": "{\n  \"0\": [\"variables.input.files.0\"],\n  \"1\": [\"variables.input.files.1\"]\n}", "type": "text"}, {"key": "0", "type": "file", "src": "MqEN1JWIM/Screenshot 2025-03-07 at 11.47.25.png"}, {"key": "1", "type": "file", "src": "o0zJOnHLn/Screenshot 2025-03-07 at 11.47.25.png"}, {"key": "", "value": "", "type": "text"}]}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 13:16:00 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"dc-Pmo4q/BVspHF8GOccTQpsrZUfrI\""}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"submitLegitAppAuthentication\": {\n            \"success\": true,\n            \"message\": \"Authentication submitted successfully\",\n            \"data\": {\n                \"id\": \"1\",\n                \"authentication_id\": \"8965818319293466\",\n                \"status\": 1,\n                \"created_at\": \"1741353360881\",\n                \"images\": null\n            }\n        }\n    }\n}"}]}, {"name": "Webhook - Authentication Complete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"authentication.completed\",\n  \"data\": {\n    \"authentication_id\": \"auth_123456\",\n    \"status\": \"completed\",\n    \"result\": {\n      \"authenticity\": \"genuine\",\n      \"confidence_score\": 0.95,\n      \"details\": {\n        \"brand_match\": true,\n        \"material_match\": true,\n        \"stitching_quality\": \"authentic\"\n      }\n    },\n    \"result_images\": [\n      {\n        \"url\": \"https://legitapp-cdn.com/results/img1.jpg\",\n        \"type\": \"comparison\",\n        \"notes\": \"Stitching pattern matches authentic sample\"\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url_2}}/admin/legitapp/webhook", "host": ["{{base_url_2}}"], "path": ["admin", "legitapp", "webhook"]}}, "response": []}, {"name": "Webhook - Authentication Rejected", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"authentication.rejected\",\n  \"data\": {\n    \"authentication_id\": \"auth_123456\",\n    \"status\": \"rejected\",\n    \"reason\": \"Insufficient image quality\",\n    \"details\": \"Please provide clearer images of the tag\"\n  }\n}"}, "url": {"raw": "https://dev.legiteem8.app/admin/legitapp/webhook", "protocol": "https", "host": ["dev", "legiteem8", "app"], "path": ["admin", "legitapp", "webhook"]}}, "response": []}, {"name": "Upload Image", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "operations", "value": "{\"query\": \"mutation UploadImage($file: Upload!) { uploadLegitAppImage(file: $file) { success message data { id s3_url legit_app_url asset_link image_type created_at } } }\", \"variables\": { \"file\": null } }", "type": "text"}, {"key": "map", "value": "{\"0\": [\"variables.file\"]}", "type": "text"}, {"key": "0", "value": "", "type": "file"}]}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": [{"name": "Upload Image", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "operations", "value": "{\"query\": \"mutation UploadImage($file: Upload!) { uploadLegitAppImage(file: $file) { success message data { id s3_url legit_app_url asset_link image_type created_at } } }\", \"variables\": { \"file\": null } }", "type": "text"}, {"key": "map", "value": "{\"0\": [\"variables.file\"]}", "type": "text"}, {"key": "0", "type": "file", "src": "PMOsZo-c5/Screenshot 2025-03-07 at 11.47.25.png"}]}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx"}, {"key": "Date", "value": "Fri, 07 Mar 2025 11:20:17 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-DNS-Prefetch-Control", "value": "off"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Strict-Transport-Security", "value": "max-age=15552000; includeSubDomains"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Powered-By", "value": "WordOps"}, {"key": "ETag", "value": "W/\"1f4-bl0Ba8G4MDAN1pHONneNIyo5UWA\""}, {"key": "Referrer-Policy", "value": "no-referrer, strict-origin-when-cross-origin"}, {"key": "Content-Encoding", "value": "gzip"}], "cookie": [], "body": "{\n    \"data\": {\n        \"uploadLegitAppImage\": {\n            \"success\": true,\n            \"message\": \"Upload successful\",\n            \"data\": {\n                \"id\": \"1\",\n                \"s3_url\": \"https://s3.amazonaws.com/com.images.legiteem8/33f92a07-973d-4e06-ba1c-1d1452e12942-20250307112017%40blurHash%3DSjNJWTIjXzMwMHRSMDB4dQ%3D%3D.png\",\n                \"legit_app_url\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2025030798ca3759278aabff.png\",\n                \"asset_link\": \"https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2025030798ca3759278aabff.png\",\n                \"image_type\": \"image/png\",\n                \"created_at\": \"1741346417497\"\n            }\n        }\n    }\n}"}]}, {"name": "Upload Image Copy", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "operations", "value": "{\"query\": \"mutation UploadImage($file: Upload!) { uploadLegitAppImage(file: $file) { success message data { id s3_url legit_app_url asset_link image_type created_at } } }\", \"variables\": { \"file\": null } }", "type": "text"}, {"key": "map", "value": "{\"0\": [\"variables.file\"]}", "type": "text"}, {"key": "0", "value": "", "type": "file"}]}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000"}, {"key": "token", "value": "your-auth-token-here"}]}