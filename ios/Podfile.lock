PODS:
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.17)
  - FBReactNativeSpec (0.72.17):
    - RCT-<PERSON>olly (= 2021.07.22.00)
    - RCTRequired (= 0.72.17)
    - RCTTypeSafety (= 0.72.17)
    - React-Core (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - Firebase/CoreOnly (8.15.0):
    - FirebaseCore (= 8.15.0)
  - Firebase/Messaging (8.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.15.0)
  - FirebaseCore (8.15.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (~> 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (3.4.4)
  - lottie-react-native (5.1.6):
    - lottie-ios (~> 3.4.0)
    - React-Core
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - Permission-Camera (3.10.1):
    - RNPermissions
  - Permission-MediaLibrary (3.10.1):
    - RNPermissions
  - Permission-Microphone (3.10.1):
    - RNPermissions
  - Permission-Notifications (3.10.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.10.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.72.17)
  - RCTTypeSafety (0.72.17):
    - FBLazyVector (= 0.72.17)
    - RCTRequired (= 0.72.17)
    - React-Core (= 0.72.17)
  - React (0.72.17):
    - React-Core (= 0.72.17)
    - React-Core/DevSupport (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-RCTActionSheet (= 0.72.17)
    - React-RCTAnimation (= 0.72.17)
    - React-RCTBlob (= 0.72.17)
    - React-RCTImage (= 0.72.17)
    - React-RCTLinking (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - React-RCTSettings (= 0.72.17)
    - React-RCTText (= 0.72.17)
    - React-RCTVibration (= 0.72.17)
  - React-callinvoker (0.72.17)
  - React-Codegen (0.72.17):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.17)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/CoreModulesHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTBlob
    - React-RCTImage (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-debug (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-jsinspector (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
    - React-runtimeexecutor (= 0.72.17)
  - React-debug (0.72.17)
  - React-jsc (0.72.17):
    - React-jsc/Fabric (= 0.72.17)
    - React-jsi (= 0.72.17)
  - React-jsc/Fabric (0.72.17):
    - React-jsi (= 0.72.17)
  - React-jsi (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.17):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - React-jsinspector (0.72.17)
  - React-logger (0.72.17):
    - glog
  - react-native-blur (0.8.0):
    - React
  - react-native-blurhash (1.1.11):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-cameraroll (5.10.0):
    - React-Core
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-netinfo (6.2.1):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-randombytes (3.6.1):
    - React-Core
  - react-native-safe-area-context (3.2.0):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-NativeModulesApple (0.72.17):
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.17)
  - React-RCTActionSheet (0.72.17):
    - React-Core/RCTActionSheetHeaders (= 0.72.17)
  - React-RCTAnimation (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTAnimationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTAppDelegate (0.72.17):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTBlobHeaders (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTImage (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTImageHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTLinking (0.72.17):
    - React-Codegen (= 0.72.17)
    - React-Core/RCTLinkingHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTNetwork (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTNetworkHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTSettings (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTSettingsHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTText (0.72.17):
    - React-Core/RCTTextHeaders (= 0.72.17)
  - React-RCTVibration (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTVibrationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-rncore (0.72.17)
  - React-runtimeexecutor (0.72.17):
    - React-jsi (= 0.72.17)
  - React-runtimescheduler (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.17):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - ReactCommon/turbomodule/core (0.72.17):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNBootSplash (4.7.5):
    - React-Core
  - RNCAsyncStorage (1.13.4):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNDeviceInfo (8.7.1):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (14.12.0):
    - Firebase/CoreOnly (= 8.15.0)
    - React-Core
  - RNFBMessaging (14.12.0):
    - Firebase/Messaging (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNIap (8.6.7):
    - React-Core
  - RNImageCropPicker (0.37.3):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.37.3)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.37.3):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNNotifee (4.1.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 4.1.0)
  - RNNotifee/NotifeeCore (4.1.0):
    - React-Core
  - RNOS (1.2.6):
    - React
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (3.7.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.20.0):
    - React-Core
    - React-RCTImage
  - RNShare (7.9.1):
    - React-Core
  - RNSVG (9.6.4):
    - React
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - lottie-ios (from `../node_modules/lottie-ios`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-MediaLibrary (from `../node_modules/react-native-permissions/ios/MediaLibrary`)
  - Permission-Microphone (from `../node_modules/react-native-permissions/ios/Microphone`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-blurhash (from `../node_modules/react-native-blurhash`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-randombytes (from `../node_modules/react-native-randombytes`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNBootSplash (from `../node_modules/react-native-bootsplash`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNIap (from `../node_modules/react-native-iap`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNOS (from `../node_modules/react-native-os`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - GoogleDataTransport
    - GoogleUtilities
    - libwebp
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  lottie-ios:
    :path: "../node_modules/lottie-ios"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-MediaLibrary:
    :path: "../node_modules/react-native-permissions/ios/MediaLibrary"
  Permission-Microphone:
    :path: "../node_modules/react-native-permissions/ios/Microphone"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-blurhash:
    :path: "../node_modules/react-native-blurhash"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-randombytes:
    :path: "../node_modules/react-native-randombytes"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNBootSplash:
    :path: "../node_modules/react-native-bootsplash"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNIap:
    :path: "../node_modules/react-native-iap"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNOS:
    :path: "../node_modules/react-native-os"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 66398fc2381d8fa1eee4c0f80d931587a7b927e8
  FBReactNativeSpec: 0f8cecf999d709dba7626bbf565b1b5f8f46a5c1
  Firebase: 5f8193dff4b5b7c5d5ef72ae54bb76c08e2b841d
  FirebaseCore: 5743c5785c074a794d35f2fff7ecc254a91e08b1
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 5e5118a2383b3531e730d974680954c679ca0a13
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: 8f97d3271e155c2d688875c29cd3c74908aef5f8
  lottie-react-native: 8f9d4be452e23f6e5ca0fdc11669dc99ab52be81
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  Permission-Camera: 9b70902f34a83c10e198d2d01f0e453e58842776
  Permission-MediaLibrary: c49ea6e00b4919404ef4fc8e5a541628790cea5a
  Permission-Microphone: 8d045745c0c12aed0370ba89c36b6bc1ba54d7eb
  Permission-Notifications: 817390e18898f34adad940cd4141e771e77086ea
  Permission-PhotoLibrary: 03c52ed95dadfb0f2ba4c7663786cce0c4e0c978
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 01c639ec840ee03928b2d65f5cd5297d737b3834
  RCTTypeSafety: 9623592521a1576363baf3d6ab8d164cfe9062bf
  React: 3c0beeda318c3c515a6bb2c1f197b55bd731aa43
  React-callinvoker: 0cd6ff2cdd80255c82cd4628fc925df1e7133a1a
  React-Codegen: eba4fe2e6cb1a411e1bd9c4bdb229c486f344a2e
  React-Core: 2cf63bca3dbce5e6e3ecac8b923f07460ca500e4
  React-CoreModules: cebd223e814ac07bc1f597bbd2480167a2c7a130
  React-cxxreact: 3b98c9f715f38cba8b72528717b03a7a1f2330dc
  React-debug: 3a5091cbda7ffe5f11ad0443109810fcd1a3e885
  React-jsc: 7c8f243222c43ce677fe107056a99ddaf061d017
  React-jsi: c3abf8d7ec734bc37b790f461c65f8e5101854cd
  React-jsiexecutor: 5a2bcc79d8ebe27cf63601b9e7d13296e9c4dc5c
  React-jsinspector: 853b8631b908636bb09ef77cb217376c38a0c8ff
  React-logger: 9ca44bb5703bf2355f3c2d2e5e67bfe98ca2dc34
  react-native-blur: cad4d93b364f91e7b7931b3fa935455487e5c33c
  react-native-blurhash: a59e6bf8117a0304488ed576abd440f4b0777a8c
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-cameraroll: 4701ae7c3dbcd3f5e9e150ca17f250a276154b35
  react-native-get-random-values: 21325b2244dfa6b58878f51f9aa42821e7ba3d06
  react-native-netinfo: 3d3769f0d65de15c83a9bf1346f8be71de5a24bf
  react-native-pager-view: da490aa1f902c9a5aeecf0909cc975ad0e92e53e
  react-native-randombytes: 421f1c7d48c0af8dbcd471b0324393ebf8fe7846
  react-native-safe-area-context: f0906bf8bc9835ac9a9d3f97e8bde2a997d8da79
  react-native-webview: 9f111dfbcfc826084d6c507f569e5e03342ee1c1
  React-NativeModulesApple: 649702e2b756b907c0c1c35f450a5bae61c33d6c
  React-perflogger: 785b0063af5178298a61b54bb46aae9a19c7bbb5
  React-RCTActionSheet: 84f37b34bd77249263ace75471d6664393c29972
  React-RCTAnimation: 5713910b6223154df4bba80a0bda4e2e671b00f8
  React-RCTAppDelegate: f93dffef18d35a752ef322be1d3fd6c2a1a6c098
  React-RCTBlob: 071e9e4c5f8369016eee855ad1f87ddd4cc4d75f
  React-RCTImage: 2e63a483be5d4e46a80dea3b17c9abee38006feb
  React-RCTLinking: e3ff685ee62187f8f61e938357307c1f890125b5
  React-RCTNetwork: a35842997a403edfdc1ec25b61a0e10a0526368d
  React-RCTSettings: aef81e0ac54268d2928ad31c4f91056cc75e5ce9
  React-RCTText: 7becec5f53f03b20da11f4b7e40e6bcfd476d134
  React-RCTVibration: defaae8016de9b3351a2a67ee8ef3fbdd643b0e1
  React-rncore: dfd20469cfad38e48b1c3cc9c4367db63f5231d7
  React-runtimeexecutor: 448409b5ae5a01b7793239f630051960c7dd39f9
  React-runtimescheduler: 54af986f214ce82ff2b746c4552c3ae1d90b815a
  React-utils: 7959d4553163b61e01bbe83dbd80e58ca420aecb
  ReactCommon: cd970cbd9e212b78dca50ca47fea4dafa485f674
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNBootSplash: 85f6b879c080e958afdb4c62ee04497b05fd7552
  RNCAsyncStorage: 44539979e1f234262d64d3ce86ec21cceb1b2c5e
  RNCClipboard: 41d8d918092ae8e676f18adada19104fa3e68495
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNDeviceInfo: aad3c663b25752a52bf8fce93f2354001dd185aa
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBApp: e4439717c23252458da2b41b81b4b475c86f90c4
  RNFBMessaging: 40dac204b4197a2661dec5be964780c6ec39bf65
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: a479ebd5ed4221a810967000735517df0d2db211
  RNIap: 9bc008662cc46865ca0919267fb2f54afb99ebd1
  RNImageCropPicker: 44e2807bc410741f35d4c45b6586aedfe3da39d2
  RNNotifee: f1ba270897bd7491f889a1a257d0c038a43376f7
  RNOS: 6f2f9a70895bbbfbdad7196abd952e7b01d45027
  RNPermissions: 4e3714e18afe7141d000beae3755e5b5fb2f5e05
  RNReanimated: ecb91ff4632995ac36c21e32a777a425953cffc3
  RNScreens: 218801c16a2782546d30bd2026bb625c0302d70f
  RNShare: a5dc3b9c53ddc73e155b8cd9a94c70c91913c43c
  RNSVG: a53a5de0d90ec96a6502cc3a4534dfcc0c130622
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: ef534101bb891fb09bae657417f34d399c1efe38

PODFILE CHECKSUM: aae9ae35e1ef028cd5cbb583271f6e555ce2930c

COCOAPODS: 1.16.2
