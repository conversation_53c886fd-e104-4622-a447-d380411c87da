diff --git a/node_modules/react-native-svg/android/.classpath b/node_modules/react-native-svg/android/.classpath
new file mode 100644
index 0000000..bbe97e5
--- /dev/null
+++ b/node_modules/react-native-svg/android/.classpath
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<classpath>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
+	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
+	<classpathentry kind="output" path="bin/default"/>
+</classpath>
diff --git a/node_modules/react-native-svg/android/.project b/node_modules/react-native-svg/android/.project
new file mode 100644
index 0000000..2efce57
--- /dev/null
+++ b/node_modules/react-native-svg/android/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-svg</name>
+	<comment>Project react-native-svg created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819028</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-svg/android/.settings/org.eclipse.buildship.core.prefs b/node_modules/react-native-svg/android/.settings/org.eclipse.buildship.core.prefs
new file mode 100644
index 0000000..1675490
--- /dev/null
+++ b/node_modules/react-native-svg/android/.settings/org.eclipse.buildship.core.prefs
@@ -0,0 +1,2 @@
+connection.project.dir=../../../android
+eclipse.preferences.version=1
diff --git a/node_modules/react-native-svg/android/bin/.project b/node_modules/react-native-svg/android/bin/.project
new file mode 100644
index 0000000..2efce57
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-svg</name>
+	<comment>Project react-native-svg created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819028</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-svg/android/bin/build.gradle b/node_modules/react-native-svg/android/bin/build.gradle
new file mode 100644
index 0000000..da29800
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build.gradle
@@ -0,0 +1,46 @@
+buildscript {
+    ext.safeExtGet = {prop, fallback ->
+        rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
+    }
+    repositories {
+        google()
+        jcenter()
+    }
+
+    dependencies {
+        //noinspection GradleDependency
+        classpath("com.android.tools.build:gradle:${safeExtGet('gradlePluginVersion', '3.4.1')}")
+    }
+}
+
+apply plugin: 'com.android.library'
+
+android {
+    compileSdkVersion safeExtGet('compileSdkVersion', 28)
+    //noinspection GradleDependency
+    buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')
+
+    defaultConfig {
+        minSdkVersion safeExtGet('minSdkVersion', 16)
+        //noinspection OldTargetApi
+        targetSdkVersion safeExtGet('targetSdkVersion', 28)
+    }
+    lintOptions {
+        abortOnError false
+    }
+}
+
+repositories {
+    mavenLocal()
+    google()
+    jcenter()
+    maven {
+        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
+        url "$rootDir/../node_modules/react-native/android"
+    }
+}
+
+dependencies {
+    //noinspection GradleDynamicVersion
+    implementation "com.facebook.react:react-native:${safeExtGet('reactnativeVersion', '+')}"
+}
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-svg/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..fd8f2f1
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.horcrux.svg" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-svg/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..8fa1366
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.horcrux.svg",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-svg/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-svg/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..c90a353
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sat Mar 22 07:28:40 IST 2025
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-svg/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..e67231f
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-svg/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..a3a874a
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,8 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.horcrux.svg" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+5-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+6
+7</manifest>
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-svg/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..fd8f2f1
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.horcrux.svg" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-svg/android/bin/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-svg/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/react-native-svg/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..7c44b88
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.horcrux.svg",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-svg/android/bin/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-svg/android/bin/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..23bd80c
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml:1:1-2:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml:1:1-2:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml:1:1-2:12
+	package
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml:1:11-36
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml:1:1-2:12
+uses-sdk
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-svg/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-svg/android/bin/gradlew b/node_modules/react-native-svg/android/bin/gradlew
new file mode 100644
index 0000000..9d82f78
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/gradlew
@@ -0,0 +1,160 @@
+#!/usr/bin/env bash
+
+##############################################################################
+##
+##  Gradle start up script for UN*X
+##
+##############################################################################
+
+# Add default JVM options here. You can also use JAVA_OPTS and GRADLE_OPTS to pass JVM options to this script.
+DEFAULT_JVM_OPTS=""
+
+APP_NAME="Gradle"
+APP_BASE_NAME=`basename "$0"`
+
+# Use the maximum available, or set MAX_FD != -1 to use that value.
+MAX_FD="maximum"
+
+warn ( ) {
+    echo "$*"
+}
+
+die ( ) {
+    echo
+    echo "$*"
+    echo
+    exit 1
+}
+
+# OS specific support (must be 'true' or 'false').
+cygwin=false
+msys=false
+darwin=false
+case "`uname`" in
+  CYGWIN* )
+    cygwin=true
+    ;;
+  Darwin* )
+    darwin=true
+    ;;
+  MINGW* )
+    msys=true
+    ;;
+esac
+
+# Attempt to set APP_HOME
+# Resolve links: $0 may be a link
+PRG="$0"
+# Need this for relative symlinks.
+while [ -h "$PRG" ] ; do
+    ls=`ls -ld "$PRG"`
+    link=`expr "$ls" : '.*-> \(.*\)$'`
+    if expr "$link" : '/.*' > /dev/null; then
+        PRG="$link"
+    else
+        PRG=`dirname "$PRG"`"/$link"
+    fi
+done
+SAVED="`pwd`"
+cd "`dirname \"$PRG\"`/" >/dev/null
+APP_HOME="`pwd -P`"
+cd "$SAVED" >/dev/null
+
+CLASSPATH=$APP_HOME/gradle/wrapper/gradle-wrapper.jar
+
+# Determine the Java command to use to start the JVM.
+if [ -n "$JAVA_HOME" ] ; then
+    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
+        # IBM's JDK on AIX uses strange locations for the executables
+        JAVACMD="$JAVA_HOME/jre/sh/java"
+    else
+        JAVACMD="$JAVA_HOME/bin/java"
+    fi
+    if [ ! -x "$JAVACMD" ] ; then
+        die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME
+
+Please set the JAVA_HOME variable in your environment to match the
+location of your Java installation."
+    fi
+else
+    JAVACMD="java"
+    which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
+
+Please set the JAVA_HOME variable in your environment to match the
+location of your Java installation."
+fi
+
+# Increase the maximum file descriptors if we can.
+if [ "$cygwin" = "false" -a "$darwin" = "false" ] ; then
+    MAX_FD_LIMIT=`ulimit -H -n`
+    if [ $? -eq 0 ] ; then
+        if [ "$MAX_FD" = "maximum" -o "$MAX_FD" = "max" ] ; then
+            MAX_FD="$MAX_FD_LIMIT"
+        fi
+        ulimit -n $MAX_FD
+        if [ $? -ne 0 ] ; then
+            warn "Could not set maximum file descriptor limit: $MAX_FD"
+        fi
+    else
+        warn "Could not query maximum file descriptor limit: $MAX_FD_LIMIT"
+    fi
+fi
+
+# For Darwin, add options to specify how the application appears in the dock
+if $darwin; then
+    GRADLE_OPTS="$GRADLE_OPTS \"-Xdock:name=$APP_NAME\" \"-Xdock:icon=$APP_HOME/media/gradle.icns\""
+fi
+
+# For Cygwin, switch paths to Windows format before running java
+if $cygwin ; then
+    APP_HOME=`cygpath --path --mixed "$APP_HOME"`
+    CLASSPATH=`cygpath --path --mixed "$CLASSPATH"`
+    JAVACMD=`cygpath --unix "$JAVACMD"`
+
+    # We build the pattern for arguments to be converted via cygpath
+    ROOTDIRSRAW=`find -L / -maxdepth 1 -mindepth 1 -type d 2>/dev/null`
+    SEP=""
+    for dir in $ROOTDIRSRAW ; do
+        ROOTDIRS="$ROOTDIRS$SEP$dir"
+        SEP="|"
+    done
+    OURCYGPATTERN="(^($ROOTDIRS))"
+    # Add a user-defined pattern to the cygpath arguments
+    if [ "$GRADLE_CYGPATTERN" != "" ] ; then
+        OURCYGPATTERN="$OURCYGPATTERN|($GRADLE_CYGPATTERN)"
+    fi
+    # Now convert the arguments - kludge to limit ourselves to /bin/sh
+    i=0
+    for arg in "$@" ; do
+        CHECK=`echo "$arg"|egrep -c "$OURCYGPATTERN" -`
+        CHECK2=`echo "$arg"|egrep -c "^-"`                                 ### Determine if an option
+
+        if [ $CHECK -ne 0 ] && [ $CHECK2 -eq 0 ] ; then                    ### Added a condition
+            eval `echo args$i`=`cygpath --path --ignore --mixed "$arg"`
+        else
+            eval `echo args$i`="\"$arg\""
+        fi
+        i=$((i+1))
+    done
+    case $i in
+        (0) set -- ;;
+        (1) set -- "$args0" ;;
+        (2) set -- "$args0" "$args1" ;;
+        (3) set -- "$args0" "$args1" "$args2" ;;
+        (4) set -- "$args0" "$args1" "$args2" "$args3" ;;
+        (5) set -- "$args0" "$args1" "$args2" "$args3" "$args4" ;;
+        (6) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" ;;
+        (7) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" "$args6" ;;
+        (8) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" "$args6" "$args7" ;;
+        (9) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" "$args6" "$args7" "$args8" ;;
+    esac
+fi
+
+# Split up the JVM_OPTS And GRADLE_OPTS values into an array, following the shell quoting and substitution rules
+function splitJvmOpts() {
+    JVM_OPTS=("$@")
+}
+eval splitJvmOpts $DEFAULT_JVM_OPTS $JAVA_OPTS $GRADLE_OPTS
+JVM_OPTS[${#JVM_OPTS[*]}]="-Dorg.gradle.appname=$APP_BASE_NAME"
+
+exec "$JAVACMD" "${JVM_OPTS[@]}" -classpath "$CLASSPATH" org.gradle.wrapper.GradleWrapperMain "$@"
diff --git a/node_modules/react-native-svg/android/bin/gradlew.bat b/node_modules/react-native-svg/android/bin/gradlew.bat
new file mode 100644
index 0000000..8a0b282
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/gradlew.bat
@@ -0,0 +1,90 @@
+@if "%DEBUG%" == "" @echo off
+@rem ##########################################################################
+@rem
+@rem  Gradle startup script for Windows
+@rem
+@rem ##########################################################################
+
+@rem Set local scope for the variables with windows NT shell
+if "%OS%"=="Windows_NT" setlocal
+
+@rem Add default JVM options here. You can also use JAVA_OPTS and GRADLE_OPTS to pass JVM options to this script.
+set DEFAULT_JVM_OPTS=
+
+set DIRNAME=%~dp0
+if "%DIRNAME%" == "" set DIRNAME=.
+set APP_BASE_NAME=%~n0
+set APP_HOME=%DIRNAME%
+
+@rem Find java.exe
+if defined JAVA_HOME goto findJavaFromJavaHome
+
+set JAVA_EXE=java.exe
+%JAVA_EXE% -version >NUL 2>&1
+if "%ERRORLEVEL%" == "0" goto init
+
+echo.
+echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
+echo.
+echo Please set the JAVA_HOME variable in your environment to match the
+echo location of your Java installation.
+
+goto fail
+
+:findJavaFromJavaHome
+set JAVA_HOME=%JAVA_HOME:"=%
+set JAVA_EXE=%JAVA_HOME%/bin/java.exe
+
+if exist "%JAVA_EXE%" goto init
+
+echo.
+echo ERROR: JAVA_HOME is set to an invalid directory: %JAVA_HOME%
+echo.
+echo Please set the JAVA_HOME variable in your environment to match the
+echo location of your Java installation.
+
+goto fail
+
+:init
+@rem Get command-line arguments, handling Windowz variants
+
+if not "%OS%" == "Windows_NT" goto win9xME_args
+if "%@eval[2+2]" == "4" goto 4NT_args
+
+:win9xME_args
+@rem Slurp the command line arguments.
+set CMD_LINE_ARGS=
+set _SKIP=2
+
+:win9xME_args_slurp
+if "x%~1" == "x" goto execute
+
+set CMD_LINE_ARGS=%*
+goto execute
+
+:4NT_args
+@rem Get arguments from the 4NT Shell from JP Software
+set CMD_LINE_ARGS=%$
+
+:execute
+@rem Setup the command line
+
+set CLASSPATH=%APP_HOME%\gradle\wrapper\gradle-wrapper.jar
+
+@rem Execute Gradle
+"%JAVA_EXE%" %DEFAULT_JVM_OPTS% %JAVA_OPTS% %GRADLE_OPTS% "-Dorg.gradle.appname=%APP_BASE_NAME%" -classpath "%CLASSPATH%" org.gradle.wrapper.GradleWrapperMain %CMD_LINE_ARGS%
+
+:end
+@rem End local scope for the variables with windows NT shell
+if "%ERRORLEVEL%"=="0" goto mainEnd
+
+:fail
+rem Set variable GRADLE_EXIT_CONSOLE if you need the _script_ return code instead of
+rem the _cmd.exe /c_ return code!
+if  not "" == "%GRADLE_EXIT_CONSOLE%" exit 1
+exit /b 1
+
+:mainEnd
+if "%OS%"=="Windows_NT" endlocal
+
+:omega
diff --git a/node_modules/react-native-svg/android/bin/src/main/AndroidManifest.xml b/node_modules/react-native-svg/android/bin/src/main/AndroidManifest.xml
new file mode 100644
index 0000000..d85bd62
--- /dev/null
+++ b/node_modules/react-native-svg/android/bin/src/main/AndroidManifest.xml
@@ -0,0 +1,2 @@
+<manifest package="com.horcrux.svg">
+</manifest>
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush$BrushType.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush$BrushType.class
new file mode 100644
index 0000000..08c6611
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush$BrushType.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush$BrushUnits.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush$BrushUnits.class
new file mode 100644
index 0000000..8a37cda
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush$BrushUnits.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush.class
new file mode 100644
index 0000000..71526e3
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/Brush.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/CircleView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/CircleView.class
new file mode 100644
index 0000000..b6080e6
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/CircleView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ClipPathView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ClipPathView.class
new file mode 100644
index 0000000..a0f1793
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ClipPathView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/DefinitionView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/DefinitionView.class
new file mode 100644
index 0000000..9e49376
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/DefinitionView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/DefsView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/DefsView.class
new file mode 100644
index 0000000..eee42fb
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/DefsView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/EllipseView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/EllipseView.class
new file mode 100644
index 0000000..dd98c98
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/EllipseView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/FontData$AbsoluteFontWeight.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/FontData$AbsoluteFontWeight.class
new file mode 100644
index 0000000..a468c0d
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/FontData$AbsoluteFontWeight.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/FontData.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/FontData.class
new file mode 100644
index 0000000..c8f729c
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/FontData.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GlyphContext.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GlyphContext.class
new file mode 100644
index 0000000..5bcc842
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GlyphContext.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GlyphPathBag.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GlyphPathBag.class
new file mode 100644
index 0000000..3f4615d
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GlyphPathBag.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GroupView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GroupView.class
new file mode 100644
index 0000000..67ef735
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/GroupView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ImageView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ImageView.class
new file mode 100644
index 0000000..80cd705
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ImageView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/LineView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/LineView.class
new file mode 100644
index 0000000..a0fe4c6
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/LineView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/LinearGradientView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/LinearGradientView.class
new file mode 100644
index 0000000..41d6be4
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/LinearGradientView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/MaskView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/MaskView.class
new file mode 100644
index 0000000..d94921b
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/MaskView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PathView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PathView.class
new file mode 100644
index 0000000..4191c09
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PathView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PatternView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PatternView.class
new file mode 100644
index 0000000..1a7f4a5
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PatternView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PropHelper$PathParser.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PropHelper$PathParser.class
new file mode 100644
index 0000000..88a9397
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PropHelper$PathParser.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PropHelper.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PropHelper.class
new file mode 100644
index 0000000..d130e46
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/PropHelper.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RadialGradientView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RadialGradientView.class
new file mode 100644
index 0000000..4677926
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RadialGradientView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RectView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RectView.class
new file mode 100644
index 0000000..6e52acd
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RectView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableView.class
new file mode 100644
index 0000000..6607ad9
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$CircleViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$CircleViewManager.class
new file mode 100644
index 0000000..cc0e435
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$CircleViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$ClipPathViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$ClipPathViewManager.class
new file mode 100644
index 0000000..2f2de55
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$ClipPathViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$DefsViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$DefsViewManager.class
new file mode 100644
index 0000000..c1dcae5
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$DefsViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$EllipseViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$EllipseViewManager.class
new file mode 100644
index 0000000..6e56cae
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$EllipseViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$GroupViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$GroupViewManager.class
new file mode 100644
index 0000000..1a6b77d
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$GroupViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$ImageViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$ImageViewManager.class
new file mode 100644
index 0000000..bc1f5a4
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$ImageViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$LineViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$LineViewManager.class
new file mode 100644
index 0000000..dfef298
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$LineViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$LinearGradientManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$LinearGradientManager.class
new file mode 100644
index 0000000..74f35e5
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$LinearGradientManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$MaskManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$MaskManager.class
new file mode 100644
index 0000000..c39e3ef
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$MaskManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$MatrixDecompositionContext.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$MatrixDecompositionContext.class
new file mode 100644
index 0000000..7057c56
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$MatrixDecompositionContext.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$PathViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$PathViewManager.class
new file mode 100644
index 0000000..5616938
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$PathViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$PatternManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$PatternManager.class
new file mode 100644
index 0000000..af2889b
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$PatternManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RadialGradientManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RadialGradientManager.class
new file mode 100644
index 0000000..a871132
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RadialGradientManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RectViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RectViewManager.class
new file mode 100644
index 0000000..65616e4
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RectViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RenderableShadowNode.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RenderableShadowNode.class
new file mode 100644
index 0000000..bc20b2b
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$RenderableShadowNode.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$SVGClass.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$SVGClass.class
new file mode 100644
index 0000000..2060d96
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$SVGClass.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$SymbolManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$SymbolManager.class
new file mode 100644
index 0000000..708bcde
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$SymbolManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TSpanViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TSpanViewManager.class
new file mode 100644
index 0000000..c337ca5
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TSpanViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TextPathViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TextPathViewManager.class
new file mode 100644
index 0000000..18d506b
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TextPathViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TextViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TextViewManager.class
new file mode 100644
index 0000000..de1c4df
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$TextViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$UseViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$UseViewManager.class
new file mode 100644
index 0000000..ef81fd4
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager$UseViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager.class
new file mode 100644
index 0000000..3b71e80
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/RenderableViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SVGLength$UnitType.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SVGLength$UnitType.class
new file mode 100644
index 0000000..8f3e838
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SVGLength$UnitType.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SVGLength.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SVGLength.class
new file mode 100644
index 0000000..fbe4be3
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SVGLength.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgPackage.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgPackage.class
new file mode 100644
index 0000000..d927472
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgPackage.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgView$Events.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgView$Events.class
new file mode 100644
index 0000000..cb5d687
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgView$Events.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgView.class
new file mode 100644
index 0000000..c5e2000
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgViewManager.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgViewManager.class
new file mode 100644
index 0000000..8041335
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgViewManager.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgViewModule.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgViewModule.class
new file mode 100644
index 0000000..92c3f7c
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SvgViewModule.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SymbolView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SymbolView.class
new file mode 100644
index 0000000..089cbe8
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/SymbolView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TSpanView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TSpanView.class
new file mode 100644
index 0000000..5ba34a6
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TSpanView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm$CharacterInformation.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm$CharacterInformation.class
new file mode 100644
index 0000000..4c11d27
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm$CharacterInformation.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm$LayoutInput.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm$LayoutInput.class
new file mode 100644
index 0000000..9644ccc
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm$LayoutInput.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm.class
new file mode 100644
index 0000000..a9a3255
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextLayoutAlgorithm.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextPathView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextPathView.class
new file mode 100644
index 0000000..664823d
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextPathView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$AlignmentBaseline.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$AlignmentBaseline.class
new file mode 100644
index 0000000..ff0e5e1
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$AlignmentBaseline.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$Direction.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$Direction.class
new file mode 100644
index 0000000..3f35b1c
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$Direction.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontStyle.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontStyle.class
new file mode 100644
index 0000000..c31720c
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontStyle.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontVariantLigatures.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontVariantLigatures.class
new file mode 100644
index 0000000..db20b8d
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontVariantLigatures.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontWeight.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontWeight.class
new file mode 100644
index 0000000..56a2541
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$FontWeight.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextAnchor.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextAnchor.class
new file mode 100644
index 0000000..b3422aa
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextAnchor.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextDecoration.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextDecoration.class
new file mode 100644
index 0000000..ac7ef1b
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextDecoration.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextLengthAdjust.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextLengthAdjust.class
new file mode 100644
index 0000000..fd56f2a
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextLengthAdjust.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathMethod.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathMethod.class
new file mode 100644
index 0000000..92ed6da
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathMethod.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathMidLine.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathMidLine.class
new file mode 100644
index 0000000..f452611
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathMidLine.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathSide.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathSide.class
new file mode 100644
index 0000000..635be43
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathSide.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathSpacing.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathSpacing.class
new file mode 100644
index 0000000..3e5a447
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties$TextPathSpacing.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties.class
new file mode 100644
index 0000000..508bd6a
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextProperties.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextView.class
new file mode 100644
index 0000000..a2b9b4f
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/TextView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/UseView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/UseView.class
new file mode 100644
index 0000000..3f18f3e
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/UseView.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ViewBox.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ViewBox.class
new file mode 100644
index 0000000..3b973d7
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/ViewBox.class differ
diff --git a/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/VirtualView.class b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/VirtualView.class
new file mode 100644
index 0000000..2e06627
Binary files /dev/null and b/node_modules/react-native-svg/android/bin/src/main/java/com/horcrux/svg/VirtualView.class differ
diff --git a/node_modules/react-native-svg/android/build.gradle b/node_modules/react-native-svg/android/build.gradle
index da29800..9a5568e 100644
--- a/node_modules/react-native-svg/android/build.gradle
+++ b/node_modules/react-native-svg/android/build.gradle
@@ -18,6 +18,7 @@ apply plugin: 'com.android.library'
 android {
     compileSdkVersion safeExtGet('compileSdkVersion', 28)
     //noinspection GradleDependency
+    namespace "com.horcrux.svg"
     buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')
 
     defaultConfig {
