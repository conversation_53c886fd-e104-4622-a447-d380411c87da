diff --git a/node_modules/@react-native-community/clipboard/android/.classpath b/node_modules/@react-native-community/clipboard/android/.classpath
index eb19361..bbe97e5 100644
--- a/node_modules/@react-native-community/clipboard/android/.classpath
+++ b/node_modules/@react-native-community/clipboard/android/.classpath
@@ -1,6 +1,6 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <classpath>
-	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8/"/>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
 	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
 	<classpathentry kind="output" path="bin/default"/>
 </classpath>
diff --git a/node_modules/@react-native-community/clipboard/android/.project b/node_modules/@react-native-community/clipboard/android/.project
index 3865e0f..4169437 100644
--- a/node_modules/@react-native-community/clipboard/android/.project
+++ b/node_modules/@react-native-community/clipboard/android/.project
@@ -20,4 +20,15 @@
 		<nature>org.eclipse.jdt.core.javanature</nature>
 		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
 	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819007</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
 </projectDescription>
diff --git a/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.buildship.core.prefs b/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.buildship.core.prefs
index 67a1397..ad1ed1e 100644
--- a/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.buildship.core.prefs
+++ b/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.buildship.core.prefs
@@ -2,7 +2,7 @@ arguments=
 auto.sync=false
 build.scans.enabled=false
 connection.gradle.distribution=GRADLE_DISTRIBUTION(VERSION(6.3))
-connection.project.dir=
+connection.project.dir=../../../../android
 eclipse.preferences.version=1
 gradle.user.home=
 java.home=/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home
diff --git a/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.jdt.core.prefs b/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.jdt.core.prefs
new file mode 100644
index 0000000..626e0e1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/.settings/org.eclipse.jdt.core.prefs
@@ -0,0 +1,4 @@
+eclipse.preferences.version=1
+org.eclipse.jdt.core.compiler.codegen.targetPlatform=17
+org.eclipse.jdt.core.compiler.compliance=17
+org.eclipse.jdt.core.compiler.source=17
diff --git a/node_modules/@react-native-community/clipboard/android/build.gradle b/node_modules/@react-native-community/clipboard/android/build.gradle
index f04ea69..0631282 100644
--- a/node_modules/@react-native-community/clipboard/android/build.gradle
+++ b/node_modules/@react-native-community/clipboard/android/build.gradle
@@ -22,8 +22,9 @@ apply plugin: 'com.android.library'
 android {
     compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
     buildToolsVersion getExtOrDefault('buildToolsVersion')
-
+    namespace "com.reactnativecommunity.clipboard"
     defaultConfig {
+        
         minSdkVersion getExtOrIntegerDefault('minSdkVersion')
         targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
     }
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/results.bin
new file mode 100644
index 0000000..7ed749e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/results.bin
@@ -0,0 +1 @@
+o/bundleLibRuntimeToDirDebug
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/BuildConfig.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/BuildConfig.dex
new file mode 100644
index 0000000..aca2dbe
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/BuildConfig.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardModule.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardModule.dex
new file mode 100644
index 0000000..93ada11
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardModule.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardPackage.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardPackage.dex
new file mode 100644
index 0000000..69e195d
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardPackage.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/09dd5ae88dfcf0760c2dac9e47d16c77/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex
new file mode 100644
index 0000000..2d6a081
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex
new file mode 100644
index 0000000..782b156
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex
new file mode 100644
index 0000000..fc39a49
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/desugar_graph.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/3121c7a0ae71bd6c1e9aeadb0ba71c5b/transformed/desugar_graph.bin differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/59f5d89bcb629d351490f30d085f7b65/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/59f5d89bcb629d351490f30d085f7b65/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/59f5d89bcb629d351490f30d085f7b65/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/59f5d89bcb629d351490f30d085f7b65/transformed/classes/classes.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/59f5d89bcb629d351490f30d085f7b65/transformed/classes/classes.dex
new file mode 100644
index 0000000..474ab65
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/59f5d89bcb629d351490f30d085f7b65/transformed/classes/classes.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex
new file mode 100644
index 0000000..e66b637
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex
new file mode 100644
index 0000000..8d3acd4
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex
new file mode 100644
index 0000000..91e2923
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/desugar_graph.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8126299051d69ad969bfe4ec5c0fdb08/transformed/desugar_graph.bin differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/86be79452f910003073f30c5536c25e9/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/86be79452f910003073f30c5536c25e9/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/86be79452f910003073f30c5536c25e9/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex
new file mode 100644
index 0000000..2d6a081
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex
new file mode 100644
index 0000000..782b156
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex
new file mode 100644
index 0000000..fc39a49
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/desugar_graph.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/8dc6c3097c3bb460c2cc1b008eafcd87/transformed/desugar_graph.bin differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/a3e1b663ff0a628d1e78a430150b0eb5/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/a3e1b663ff0a628d1e78a430150b0eb5/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/a3e1b663ff0a628d1e78a430150b0eb5/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex
new file mode 100644
index 0000000..2d6a081
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/BuildConfig.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex
new file mode 100644
index 0000000..782b156
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/ClipboardModule.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex
new file mode 100644
index 0000000..fc39a49
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/debug/com/reactnativecommunity/clipboard/ClipboardPackage.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/desugar_graph.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/bb9e7831f1f8db0b2f89f2ed30009a3d/transformed/desugar_graph.bin differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/c027f3b3f10045bc56c9b8cba76327f9/results.bin b/node_modules/@react-native-community/clipboard/android/build/.transforms/c027f3b3f10045bc56c9b8cba76327f9/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/.transforms/c027f3b3f10045bc56c9b8cba76327f9/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@react-native-community/clipboard/android/build/.transforms/c027f3b3f10045bc56c9b8cba76327f9/transformed/classes/classes.dex b/node_modules/@react-native-community/clipboard/android/build/.transforms/c027f3b3f10045bc56c9b8cba76327f9/transformed/classes/classes.dex
new file mode 100644
index 0000000..ef8a122
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/.transforms/c027f3b3f10045bc56c9b8cba76327f9/transformed/classes/classes.dex differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/generated/source/buildConfig/debug/com/reactnativecommunity/clipboard/BuildConfig.java b/node_modules/@react-native-community/clipboard/android/build/generated/source/buildConfig/debug/com/reactnativecommunity/clipboard/BuildConfig.java
new file mode 100644
index 0000000..4ad094e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/generated/source/buildConfig/debug/com/reactnativecommunity/clipboard/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.reactnativecommunity.clipboard;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.reactnativecommunity.clipboard";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/@react-native-community/clipboard/android/build/generated/source/buildConfig/release/com/reactnativecommunity/clipboard/BuildConfig.java b/node_modules/@react-native-community/clipboard/android/build/generated/source/buildConfig/release/com/reactnativecommunity/clipboard/BuildConfig.java
new file mode 100644
index 0000000..efeb9b9
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/generated/source/buildConfig/release/com/reactnativecommunity/clipboard/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.reactnativecommunity.clipboard;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "com.reactnativecommunity.clipboard";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..71948e2
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativecommunity.clipboard" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..be48ba1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativecommunity.clipboard",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..71948e2
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativecommunity.clipboard" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..be48ba1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativecommunity.clipboard",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..deb9320
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativecommunity.clipboard" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="33" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..e46fa4c
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,16 @@
+{
+  "version": 2,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativecommunity.clipboard",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ]
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..d8560bd
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,2 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar
new file mode 100644
index 0000000..69e73c7
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..7a4a50a
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..5d0b86b
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..1e9bac1
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..1e9bac1
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..b091341
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..a8801e1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,1789 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr background 0x0
+int attr backgroundImage 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr barLength 0x0
+int attr borderlessButtonStyle 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedTextViewStyle 0x0
+int attr closeIcon 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr controlBackground 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr customNavigationLayout 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableSize 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr height 0x0
+int attr hideOnContentScroll 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr icon 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr isLightTheme 0x0
+int attr itemPadding 0x0
+int attr keylines 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr layout 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr lineHeight 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr maxButtonHeight 0x0
+int attr measureWithLargestChild 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr radioButtonStyle 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr singleChoiceItemLayout 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr state_above_anchor 0x0
+int attr statusBarBackground 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr theme 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_allow_stacked_button_bar 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_input_method_navigation_guard 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_star_black_16dp 0x0
+int drawable abc_ic_star_black_36dp 0x0
+int drawable abc_ic_star_black_48dp 0x0
+int drawable abc_ic_star_half_black_16dp 0x0
+int drawable abc_ic_star_half_black_36dp 0x0
+int drawable abc_ic_star_half_black_48dp 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl_dark 0x0
+int drawable abc_text_select_handle_left_mtrl_light 0x0
+int drawable abc_text_select_handle_middle_mtrl_dark 0x0
+int drawable abc_text_select_handle_middle_mtrl_light 0x0
+int drawable abc_text_select_handle_right_mtrl_dark 0x0
+int drawable abc_text_select_handle_right_mtrl_light 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id accessibility_actions 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id alertTitle 0x0
+int id async 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id buttonPanel 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id chronometer 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id edit_query 0x0
+int id end 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id group_divider 0x0
+int id home 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id message 0x0
+int id multiply 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id parentPanel 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id shortcut 0x0
+int id spacer 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id start 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id top 0x0
+int id topPanel 0x0
+int id uniform 0x0
+int id up 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_font_family_body_1_material 0x0
+int string abc_font_family_body_2_material 0x0
+int string abc_font_family_button_material 0x0
+int string abc_font_family_caption_material 0x0
+int string abc_font_family_display_1_material 0x0
+int string abc_font_family_display_2_material 0x0
+int string abc_font_family_display_3_material 0x0
+int string abc_font_family_display_4_material 0x0
+int string abc_font_family_headline_material 0x0
+int string abc_font_family_menu_material 0x0
+int string abc_font_family_subhead_material 0x0
+int string abc_font_family_title_material 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string button_description 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string combobox_description 0x0
+int string header_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int style redboxButton 0x0
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_firstBaselineToTopHeight 6
+int styleable AppCompatTextView_fontFamily 7
+int styleable AppCompatTextView_lastBaselineToBottomHeight 8
+int styleable AppCompatTextView_lineHeight 9
+int styleable AppCompatTextView_textAllCaps 10
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseDrawable 17
+int styleable AppCompatTheme_actionModeCopyDrawable 18
+int styleable AppCompatTheme_actionModeCutDrawable 19
+int styleable AppCompatTheme_actionModeFindDrawable 20
+int styleable AppCompatTheme_actionModePasteDrawable 21
+int styleable AppCompatTheme_actionModePopupWindowStyle 22
+int styleable AppCompatTheme_actionModeSelectAllDrawable 23
+int styleable AppCompatTheme_actionModeShareDrawable 24
+int styleable AppCompatTheme_actionModeSplitBackground 25
+int styleable AppCompatTheme_actionModeStyle 26
+int styleable AppCompatTheme_actionModeWebSearchDrawable 27
+int styleable AppCompatTheme_actionOverflowButtonStyle 28
+int styleable AppCompatTheme_actionOverflowMenuStyle 29
+int styleable AppCompatTheme_activityChooserViewStyle 30
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 31
+int styleable AppCompatTheme_alertDialogCenterButtons 32
+int styleable AppCompatTheme_alertDialogStyle 33
+int styleable AppCompatTheme_alertDialogTheme 34
+int styleable AppCompatTheme_android_windowAnimationStyle 35
+int styleable AppCompatTheme_android_windowIsFloating 36
+int styleable AppCompatTheme_autoCompleteTextViewStyle 37
+int styleable AppCompatTheme_borderlessButtonStyle 38
+int styleable AppCompatTheme_buttonBarButtonStyle 39
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
+int styleable AppCompatTheme_buttonBarStyle 43
+int styleable AppCompatTheme_buttonStyle 44
+int styleable AppCompatTheme_buttonStyleSmall 45
+int styleable AppCompatTheme_checkboxStyle 46
+int styleable AppCompatTheme_checkedTextViewStyle 47
+int styleable AppCompatTheme_colorAccent 48
+int styleable AppCompatTheme_colorBackgroundFloating 49
+int styleable AppCompatTheme_colorButtonNormal 50
+int styleable AppCompatTheme_colorControlActivated 51
+int styleable AppCompatTheme_colorControlHighlight 52
+int styleable AppCompatTheme_colorControlNormal 53
+int styleable AppCompatTheme_colorError 54
+int styleable AppCompatTheme_colorPrimary 55
+int styleable AppCompatTheme_colorPrimaryDark 56
+int styleable AppCompatTheme_colorSwitchThumbNormal 57
+int styleable AppCompatTheme_controlBackground 58
+int styleable AppCompatTheme_dialogCornerRadius 59
+int styleable AppCompatTheme_dialogPreferredPadding 60
+int styleable AppCompatTheme_dialogTheme 61
+int styleable AppCompatTheme_dividerHorizontal 62
+int styleable AppCompatTheme_dividerVertical 63
+int styleable AppCompatTheme_dropDownListViewStyle 64
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
+int styleable AppCompatTheme_editTextBackground 66
+int styleable AppCompatTheme_editTextColor 67
+int styleable AppCompatTheme_editTextStyle 68
+int styleable AppCompatTheme_homeAsUpIndicator 69
+int styleable AppCompatTheme_imageButtonStyle 70
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
+int styleable AppCompatTheme_listDividerAlertDialog 72
+int styleable AppCompatTheme_listMenuViewStyle 73
+int styleable AppCompatTheme_listPopupWindowStyle 74
+int styleable AppCompatTheme_listPreferredItemHeight 75
+int styleable AppCompatTheme_listPreferredItemHeightLarge 76
+int styleable AppCompatTheme_listPreferredItemHeightSmall 77
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
+int styleable AppCompatTheme_listPreferredItemPaddingRight 79
+int styleable AppCompatTheme_panelBackground 80
+int styleable AppCompatTheme_panelMenuListTheme 81
+int styleable AppCompatTheme_panelMenuListWidth 82
+int styleable AppCompatTheme_popupMenuStyle 83
+int styleable AppCompatTheme_popupWindowStyle 84
+int styleable AppCompatTheme_radioButtonStyle 85
+int styleable AppCompatTheme_ratingBarStyle 86
+int styleable AppCompatTheme_ratingBarStyleIndicator 87
+int styleable AppCompatTheme_ratingBarStyleSmall 88
+int styleable AppCompatTheme_searchViewStyle 89
+int styleable AppCompatTheme_seekBarStyle 90
+int styleable AppCompatTheme_selectableItemBackground 91
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
+int styleable AppCompatTheme_spinnerDropDownItemStyle 93
+int styleable AppCompatTheme_spinnerStyle 94
+int styleable AppCompatTheme_switchStyle 95
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
+int styleable AppCompatTheme_textAppearanceListItem 97
+int styleable AppCompatTheme_textAppearanceListItemSecondary 98
+int styleable AppCompatTheme_textAppearanceListItemSmall 99
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
+int styleable AppCompatTheme_textColorAlertDialogListItem 104
+int styleable AppCompatTheme_textColorSearchUrl 105
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
+int styleable AppCompatTheme_toolbarStyle 107
+int styleable AppCompatTheme_tooltipForegroundColor 108
+int styleable AppCompatTheme_tooltipFrameBackground 109
+int styleable AppCompatTheme_viewInflaterClass 110
+int styleable AppCompatTheme_windowActionBar 111
+int styleable AppCompatTheme_windowActionBarOverlay 112
+int styleable AppCompatTheme_windowActionModeOverlay 113
+int styleable AppCompatTheme_windowFixedHeightMajor 114
+int styleable AppCompatTheme_windowFixedHeightMinor 115
+int styleable AppCompatTheme_windowFixedWidthMajor 116
+int styleable AppCompatTheme_windowFixedWidthMinor 117
+int styleable AppCompatTheme_windowMinWidthMajor 118
+int styleable AppCompatTheme_windowMinWidthMinor 119
+int styleable AppCompatTheme_windowNoTitle 120
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonTint 1
+int styleable CompoundButton_buttonTintMode 2
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textSize 8
+int styleable TextAppearance_android_textStyle 9
+int styleable TextAppearance_android_typeface 10
+int styleable TextAppearance_fontFamily 11
+int styleable TextAppearance_textAllCaps 12
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_navigationContentDescription 14
+int styleable Toolbar_navigationIcon 15
+int styleable Toolbar_popupTheme 16
+int styleable Toolbar_subtitle 17
+int styleable Toolbar_subtitleTextAppearance 18
+int styleable Toolbar_subtitleTextColor 19
+int styleable Toolbar_title 20
+int styleable Toolbar_titleMargin 21
+int styleable Toolbar_titleMarginBottom 22
+int styleable Toolbar_titleMarginEnd 23
+int styleable Toolbar_titleMarginStart 24
+int styleable Toolbar_titleMarginTop 25
+int styleable Toolbar_titleMargins 26
+int styleable Toolbar_titleTextAppearance 27
+int styleable Toolbar_titleTextColor 28
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..a8801e1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
@@ -0,0 +1,1789 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr background 0x0
+int attr backgroundImage 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr barLength 0x0
+int attr borderlessButtonStyle 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedTextViewStyle 0x0
+int attr closeIcon 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr controlBackground 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr customNavigationLayout 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableSize 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr height 0x0
+int attr hideOnContentScroll 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr icon 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr isLightTheme 0x0
+int attr itemPadding 0x0
+int attr keylines 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr layout 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr lineHeight 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr maxButtonHeight 0x0
+int attr measureWithLargestChild 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr radioButtonStyle 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr singleChoiceItemLayout 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr state_above_anchor 0x0
+int attr statusBarBackground 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr theme 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_allow_stacked_button_bar 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_input_method_navigation_guard 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_star_black_16dp 0x0
+int drawable abc_ic_star_black_36dp 0x0
+int drawable abc_ic_star_black_48dp 0x0
+int drawable abc_ic_star_half_black_16dp 0x0
+int drawable abc_ic_star_half_black_36dp 0x0
+int drawable abc_ic_star_half_black_48dp 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl_dark 0x0
+int drawable abc_text_select_handle_left_mtrl_light 0x0
+int drawable abc_text_select_handle_middle_mtrl_dark 0x0
+int drawable abc_text_select_handle_middle_mtrl_light 0x0
+int drawable abc_text_select_handle_right_mtrl_dark 0x0
+int drawable abc_text_select_handle_right_mtrl_light 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id accessibility_actions 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id alertTitle 0x0
+int id async 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id buttonPanel 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id chronometer 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id edit_query 0x0
+int id end 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id group_divider 0x0
+int id home 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id message 0x0
+int id multiply 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id parentPanel 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id shortcut 0x0
+int id spacer 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id start 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id top 0x0
+int id topPanel 0x0
+int id uniform 0x0
+int id up 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_font_family_body_1_material 0x0
+int string abc_font_family_body_2_material 0x0
+int string abc_font_family_button_material 0x0
+int string abc_font_family_caption_material 0x0
+int string abc_font_family_display_1_material 0x0
+int string abc_font_family_display_2_material 0x0
+int string abc_font_family_display_3_material 0x0
+int string abc_font_family_display_4_material 0x0
+int string abc_font_family_headline_material 0x0
+int string abc_font_family_menu_material 0x0
+int string abc_font_family_subhead_material 0x0
+int string abc_font_family_title_material 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string button_description 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string combobox_description 0x0
+int string header_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int style redboxButton 0x0
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_firstBaselineToTopHeight 6
+int styleable AppCompatTextView_fontFamily 7
+int styleable AppCompatTextView_lastBaselineToBottomHeight 8
+int styleable AppCompatTextView_lineHeight 9
+int styleable AppCompatTextView_textAllCaps 10
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseDrawable 17
+int styleable AppCompatTheme_actionModeCopyDrawable 18
+int styleable AppCompatTheme_actionModeCutDrawable 19
+int styleable AppCompatTheme_actionModeFindDrawable 20
+int styleable AppCompatTheme_actionModePasteDrawable 21
+int styleable AppCompatTheme_actionModePopupWindowStyle 22
+int styleable AppCompatTheme_actionModeSelectAllDrawable 23
+int styleable AppCompatTheme_actionModeShareDrawable 24
+int styleable AppCompatTheme_actionModeSplitBackground 25
+int styleable AppCompatTheme_actionModeStyle 26
+int styleable AppCompatTheme_actionModeWebSearchDrawable 27
+int styleable AppCompatTheme_actionOverflowButtonStyle 28
+int styleable AppCompatTheme_actionOverflowMenuStyle 29
+int styleable AppCompatTheme_activityChooserViewStyle 30
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 31
+int styleable AppCompatTheme_alertDialogCenterButtons 32
+int styleable AppCompatTheme_alertDialogStyle 33
+int styleable AppCompatTheme_alertDialogTheme 34
+int styleable AppCompatTheme_android_windowAnimationStyle 35
+int styleable AppCompatTheme_android_windowIsFloating 36
+int styleable AppCompatTheme_autoCompleteTextViewStyle 37
+int styleable AppCompatTheme_borderlessButtonStyle 38
+int styleable AppCompatTheme_buttonBarButtonStyle 39
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
+int styleable AppCompatTheme_buttonBarStyle 43
+int styleable AppCompatTheme_buttonStyle 44
+int styleable AppCompatTheme_buttonStyleSmall 45
+int styleable AppCompatTheme_checkboxStyle 46
+int styleable AppCompatTheme_checkedTextViewStyle 47
+int styleable AppCompatTheme_colorAccent 48
+int styleable AppCompatTheme_colorBackgroundFloating 49
+int styleable AppCompatTheme_colorButtonNormal 50
+int styleable AppCompatTheme_colorControlActivated 51
+int styleable AppCompatTheme_colorControlHighlight 52
+int styleable AppCompatTheme_colorControlNormal 53
+int styleable AppCompatTheme_colorError 54
+int styleable AppCompatTheme_colorPrimary 55
+int styleable AppCompatTheme_colorPrimaryDark 56
+int styleable AppCompatTheme_colorSwitchThumbNormal 57
+int styleable AppCompatTheme_controlBackground 58
+int styleable AppCompatTheme_dialogCornerRadius 59
+int styleable AppCompatTheme_dialogPreferredPadding 60
+int styleable AppCompatTheme_dialogTheme 61
+int styleable AppCompatTheme_dividerHorizontal 62
+int styleable AppCompatTheme_dividerVertical 63
+int styleable AppCompatTheme_dropDownListViewStyle 64
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
+int styleable AppCompatTheme_editTextBackground 66
+int styleable AppCompatTheme_editTextColor 67
+int styleable AppCompatTheme_editTextStyle 68
+int styleable AppCompatTheme_homeAsUpIndicator 69
+int styleable AppCompatTheme_imageButtonStyle 70
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
+int styleable AppCompatTheme_listDividerAlertDialog 72
+int styleable AppCompatTheme_listMenuViewStyle 73
+int styleable AppCompatTheme_listPopupWindowStyle 74
+int styleable AppCompatTheme_listPreferredItemHeight 75
+int styleable AppCompatTheme_listPreferredItemHeightLarge 76
+int styleable AppCompatTheme_listPreferredItemHeightSmall 77
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
+int styleable AppCompatTheme_listPreferredItemPaddingRight 79
+int styleable AppCompatTheme_panelBackground 80
+int styleable AppCompatTheme_panelMenuListTheme 81
+int styleable AppCompatTheme_panelMenuListWidth 82
+int styleable AppCompatTheme_popupMenuStyle 83
+int styleable AppCompatTheme_popupWindowStyle 84
+int styleable AppCompatTheme_radioButtonStyle 85
+int styleable AppCompatTheme_ratingBarStyle 86
+int styleable AppCompatTheme_ratingBarStyleIndicator 87
+int styleable AppCompatTheme_ratingBarStyleSmall 88
+int styleable AppCompatTheme_searchViewStyle 89
+int styleable AppCompatTheme_seekBarStyle 90
+int styleable AppCompatTheme_selectableItemBackground 91
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
+int styleable AppCompatTheme_spinnerDropDownItemStyle 93
+int styleable AppCompatTheme_spinnerStyle 94
+int styleable AppCompatTheme_switchStyle 95
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
+int styleable AppCompatTheme_textAppearanceListItem 97
+int styleable AppCompatTheme_textAppearanceListItemSecondary 98
+int styleable AppCompatTheme_textAppearanceListItemSmall 99
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
+int styleable AppCompatTheme_textColorAlertDialogListItem 104
+int styleable AppCompatTheme_textColorSearchUrl 105
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
+int styleable AppCompatTheme_toolbarStyle 107
+int styleable AppCompatTheme_tooltipForegroundColor 108
+int styleable AppCompatTheme_tooltipFrameBackground 109
+int styleable AppCompatTheme_viewInflaterClass 110
+int styleable AppCompatTheme_windowActionBar 111
+int styleable AppCompatTheme_windowActionBarOverlay 112
+int styleable AppCompatTheme_windowActionModeOverlay 113
+int styleable AppCompatTheme_windowFixedHeightMajor 114
+int styleable AppCompatTheme_windowFixedHeightMinor 115
+int styleable AppCompatTheme_windowFixedWidthMajor 116
+int styleable AppCompatTheme_windowFixedWidthMinor 117
+int styleable AppCompatTheme_windowMinWidthMajor 118
+int styleable AppCompatTheme_windowMinWidthMinor 119
+int styleable AppCompatTheme_windowNoTitle 120
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonTint 1
+int styleable CompoundButton_buttonTintMode 2
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textSize 8
+int styleable TextAppearance_android_textStyle 9
+int styleable TextAppearance_android_typeface 10
+int styleable TextAppearance_fontFamily 11
+int styleable TextAppearance_textAllCaps 12
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_navigationContentDescription 14
+int styleable Toolbar_navigationIcon 15
+int styleable Toolbar_popupTheme 16
+int styleable Toolbar_subtitle 17
+int styleable Toolbar_subtitleTextAppearance 18
+int styleable Toolbar_subtitleTextColor 19
+int styleable Toolbar_title 20
+int styleable Toolbar_titleMargin 21
+int styleable Toolbar_titleMarginBottom 22
+int styleable Toolbar_titleMarginEnd 23
+int styleable Toolbar_titleMarginStart 24
+int styleable Toolbar_titleMarginTop 25
+int styleable Toolbar_titleMargins 26
+int styleable Toolbar_titleTextAppearance 27
+int styleable Toolbar_titleTextColor 28
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..a8801e1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/compile_symbol_list/release/R.txt
@@ -0,0 +1,1789 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr background 0x0
+int attr backgroundImage 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr barLength 0x0
+int attr borderlessButtonStyle 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedTextViewStyle 0x0
+int attr closeIcon 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr controlBackground 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr customNavigationLayout 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableSize 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr height 0x0
+int attr hideOnContentScroll 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr icon 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr isLightTheme 0x0
+int attr itemPadding 0x0
+int attr keylines 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr layout 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr lineHeight 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr maxButtonHeight 0x0
+int attr measureWithLargestChild 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr radioButtonStyle 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr singleChoiceItemLayout 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr state_above_anchor 0x0
+int attr statusBarBackground 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr theme 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_allow_stacked_button_bar 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_input_method_navigation_guard 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_star_black_16dp 0x0
+int drawable abc_ic_star_black_36dp 0x0
+int drawable abc_ic_star_black_48dp 0x0
+int drawable abc_ic_star_half_black_16dp 0x0
+int drawable abc_ic_star_half_black_36dp 0x0
+int drawable abc_ic_star_half_black_48dp 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl_dark 0x0
+int drawable abc_text_select_handle_left_mtrl_light 0x0
+int drawable abc_text_select_handle_middle_mtrl_dark 0x0
+int drawable abc_text_select_handle_middle_mtrl_light 0x0
+int drawable abc_text_select_handle_right_mtrl_dark 0x0
+int drawable abc_text_select_handle_right_mtrl_light 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id accessibility_actions 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id alertTitle 0x0
+int id async 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id buttonPanel 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id chronometer 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id edit_query 0x0
+int id end 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id group_divider 0x0
+int id home 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id message 0x0
+int id multiply 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id parentPanel 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id shortcut 0x0
+int id spacer 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id start 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id top 0x0
+int id topPanel 0x0
+int id uniform 0x0
+int id up 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_font_family_body_1_material 0x0
+int string abc_font_family_body_2_material 0x0
+int string abc_font_family_button_material 0x0
+int string abc_font_family_caption_material 0x0
+int string abc_font_family_display_1_material 0x0
+int string abc_font_family_display_2_material 0x0
+int string abc_font_family_display_3_material 0x0
+int string abc_font_family_display_4_material 0x0
+int string abc_font_family_headline_material 0x0
+int string abc_font_family_menu_material 0x0
+int string abc_font_family_subhead_material 0x0
+int string abc_font_family_title_material 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string button_description 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string combobox_description 0x0
+int string header_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int style redboxButton 0x0
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_firstBaselineToTopHeight 6
+int styleable AppCompatTextView_fontFamily 7
+int styleable AppCompatTextView_lastBaselineToBottomHeight 8
+int styleable AppCompatTextView_lineHeight 9
+int styleable AppCompatTextView_textAllCaps 10
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseDrawable 17
+int styleable AppCompatTheme_actionModeCopyDrawable 18
+int styleable AppCompatTheme_actionModeCutDrawable 19
+int styleable AppCompatTheme_actionModeFindDrawable 20
+int styleable AppCompatTheme_actionModePasteDrawable 21
+int styleable AppCompatTheme_actionModePopupWindowStyle 22
+int styleable AppCompatTheme_actionModeSelectAllDrawable 23
+int styleable AppCompatTheme_actionModeShareDrawable 24
+int styleable AppCompatTheme_actionModeSplitBackground 25
+int styleable AppCompatTheme_actionModeStyle 26
+int styleable AppCompatTheme_actionModeWebSearchDrawable 27
+int styleable AppCompatTheme_actionOverflowButtonStyle 28
+int styleable AppCompatTheme_actionOverflowMenuStyle 29
+int styleable AppCompatTheme_activityChooserViewStyle 30
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 31
+int styleable AppCompatTheme_alertDialogCenterButtons 32
+int styleable AppCompatTheme_alertDialogStyle 33
+int styleable AppCompatTheme_alertDialogTheme 34
+int styleable AppCompatTheme_android_windowAnimationStyle 35
+int styleable AppCompatTheme_android_windowIsFloating 36
+int styleable AppCompatTheme_autoCompleteTextViewStyle 37
+int styleable AppCompatTheme_borderlessButtonStyle 38
+int styleable AppCompatTheme_buttonBarButtonStyle 39
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
+int styleable AppCompatTheme_buttonBarStyle 43
+int styleable AppCompatTheme_buttonStyle 44
+int styleable AppCompatTheme_buttonStyleSmall 45
+int styleable AppCompatTheme_checkboxStyle 46
+int styleable AppCompatTheme_checkedTextViewStyle 47
+int styleable AppCompatTheme_colorAccent 48
+int styleable AppCompatTheme_colorBackgroundFloating 49
+int styleable AppCompatTheme_colorButtonNormal 50
+int styleable AppCompatTheme_colorControlActivated 51
+int styleable AppCompatTheme_colorControlHighlight 52
+int styleable AppCompatTheme_colorControlNormal 53
+int styleable AppCompatTheme_colorError 54
+int styleable AppCompatTheme_colorPrimary 55
+int styleable AppCompatTheme_colorPrimaryDark 56
+int styleable AppCompatTheme_colorSwitchThumbNormal 57
+int styleable AppCompatTheme_controlBackground 58
+int styleable AppCompatTheme_dialogCornerRadius 59
+int styleable AppCompatTheme_dialogPreferredPadding 60
+int styleable AppCompatTheme_dialogTheme 61
+int styleable AppCompatTheme_dividerHorizontal 62
+int styleable AppCompatTheme_dividerVertical 63
+int styleable AppCompatTheme_dropDownListViewStyle 64
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
+int styleable AppCompatTheme_editTextBackground 66
+int styleable AppCompatTheme_editTextColor 67
+int styleable AppCompatTheme_editTextStyle 68
+int styleable AppCompatTheme_homeAsUpIndicator 69
+int styleable AppCompatTheme_imageButtonStyle 70
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
+int styleable AppCompatTheme_listDividerAlertDialog 72
+int styleable AppCompatTheme_listMenuViewStyle 73
+int styleable AppCompatTheme_listPopupWindowStyle 74
+int styleable AppCompatTheme_listPreferredItemHeight 75
+int styleable AppCompatTheme_listPreferredItemHeightLarge 76
+int styleable AppCompatTheme_listPreferredItemHeightSmall 77
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
+int styleable AppCompatTheme_listPreferredItemPaddingRight 79
+int styleable AppCompatTheme_panelBackground 80
+int styleable AppCompatTheme_panelMenuListTheme 81
+int styleable AppCompatTheme_panelMenuListWidth 82
+int styleable AppCompatTheme_popupMenuStyle 83
+int styleable AppCompatTheme_popupWindowStyle 84
+int styleable AppCompatTheme_radioButtonStyle 85
+int styleable AppCompatTheme_ratingBarStyle 86
+int styleable AppCompatTheme_ratingBarStyleIndicator 87
+int styleable AppCompatTheme_ratingBarStyleSmall 88
+int styleable AppCompatTheme_searchViewStyle 89
+int styleable AppCompatTheme_seekBarStyle 90
+int styleable AppCompatTheme_selectableItemBackground 91
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
+int styleable AppCompatTheme_spinnerDropDownItemStyle 93
+int styleable AppCompatTheme_spinnerStyle 94
+int styleable AppCompatTheme_switchStyle 95
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
+int styleable AppCompatTheme_textAppearanceListItem 97
+int styleable AppCompatTheme_textAppearanceListItemSecondary 98
+int styleable AppCompatTheme_textAppearanceListItemSmall 99
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
+int styleable AppCompatTheme_textColorAlertDialogListItem 104
+int styleable AppCompatTheme_textColorSearchUrl 105
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
+int styleable AppCompatTheme_toolbarStyle 107
+int styleable AppCompatTheme_tooltipForegroundColor 108
+int styleable AppCompatTheme_tooltipFrameBackground 109
+int styleable AppCompatTheme_viewInflaterClass 110
+int styleable AppCompatTheme_windowActionBar 111
+int styleable AppCompatTheme_windowActionBarOverlay 112
+int styleable AppCompatTheme_windowActionModeOverlay 113
+int styleable AppCompatTheme_windowFixedHeightMajor 114
+int styleable AppCompatTheme_windowFixedHeightMinor 115
+int styleable AppCompatTheme_windowFixedWidthMajor 116
+int styleable AppCompatTheme_windowFixedWidthMinor 117
+int styleable AppCompatTheme_windowMinWidthMajor 118
+int styleable AppCompatTheme_windowMinWidthMinor 119
+int styleable AppCompatTheme_windowNoTitle 120
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonTint 1
+int styleable CompoundButton_buttonTintMode 2
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textSize 8
+int styleable TextAppearance_android_textStyle 9
+int styleable TextAppearance_android_typeface 10
+int styleable TextAppearance_fontFamily 11
+int styleable TextAppearance_textAllCaps 12
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_navigationContentDescription 14
+int styleable Toolbar_navigationIcon 15
+int styleable Toolbar_popupTheme 16
+int styleable Toolbar_subtitle 17
+int styleable Toolbar_subtitleTextAppearance 18
+int styleable Toolbar_subtitleTextColor 19
+int styleable Toolbar_title 20
+int styleable Toolbar_titleMargin 21
+int styleable Toolbar_titleMarginBottom 22
+int styleable Toolbar_titleMarginEnd 23
+int styleable Toolbar_titleMarginStart 24
+int styleable Toolbar_titleMarginTop 25
+int styleable Toolbar_titleMargins 26
+int styleable Toolbar_titleTextAppearance 27
+int styleable Toolbar_titleTextColor 28
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..ed8f46e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sun Mar 09 23:04:53 IST 2025
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..c64010a
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..678a322
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..9ac23c2
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..b67f842
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..574c701
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..a8cafb9
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..76041ee
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sun Mar 09 19:19:41 IST 2025
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..8d3a5a3
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..04983f1
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/assets"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..a8b8cb6
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Feb 12 17:57:15 IST 2025
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..a24e942
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/rs/release"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/rs/release"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/BuildConfig.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/BuildConfig.class
new file mode 100644
index 0000000..89f3b15
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/BuildConfig.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/ClipboardModule.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/ClipboardModule.class
new file mode 100644
index 0000000..c39aa16
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/ClipboardModule.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class
new file mode 100644
index 0000000..b72d37c
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/BuildConfig.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/BuildConfig.class
new file mode 100644
index 0000000..9da7ba8
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/BuildConfig.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/ClipboardModule.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/ClipboardModule.class
new file mode 100644
index 0000000..99946b0
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/ClipboardModule.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class
new file mode 100644
index 0000000..b599d51
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/BuildConfig.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/BuildConfig.class
new file mode 100644
index 0000000..dc41f6f
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/BuildConfig.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/ClipboardModule.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/ClipboardModule.class
new file mode 100644
index 0000000..83a7d47
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/ClipboardModule.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class
new file mode 100644
index 0000000..88d2d88
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/javac/release/classes/com/reactnativecommunity/clipboard/ClipboardPackage.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..72effa3
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,8 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.reactnativecommunity.clipboard" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+5-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+6
+7</manifest>
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..68afb5d
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,7 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.reactnativecommunity.clipboard" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+6
+7</manifest>
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..421eb03
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.reactnativecommunity.clipboard" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="33" />
+7-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..71948e2
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativecommunity.clipboard" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..71948e2
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativecommunity.clipboard" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..deb9320
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativecommunity.clipboard" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="33" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..3bd7cd9
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativecommunity.clipboard",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/@react-native-community/clipboard/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..145bb16
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,16 @@
+{
+  "version": 2,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativecommunity.clipboard",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ]
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/BuildConfig.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/BuildConfig.class
new file mode 100644
index 0000000..9da7ba8
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/BuildConfig.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardModule.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardModule.class
new file mode 100644
index 0000000..99946b0
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardModule.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardPackage.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardPackage.class
new file mode 100644
index 0000000..b599d51
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/reactnativecommunity/clipboard/ClipboardPackage.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/BuildConfig.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/BuildConfig.class
new file mode 100644
index 0000000..89f3b15
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/BuildConfig.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/ClipboardModule.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/ClipboardModule.class
new file mode 100644
index 0000000..c39aa16
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/ClipboardModule.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/ClipboardPackage.class b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/ClipboardPackage.class
new file mode 100644
index 0000000..b72d37c
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_dir/debug/com/reactnativecommunity/clipboard/ClipboardPackage.class differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..8a43b98
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..754a5bf
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..571949e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1,1315 @@
+com.reactnativecommunity.clipboard
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr background
+attr backgroundImage
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr barLength
+attr borderlessButtonStyle
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr checkboxStyle
+attr checkedTextViewStyle
+attr closeIcon
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorPrimary
+attr colorPrimaryDark
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr controlBackground
+attr coordinatorLayoutStyle
+attr customNavigationLayout
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableSize
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr expandActivityOverflowButtonDrawable
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr firstBaselineToTopHeight
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr gapBetweenBars
+attr goIcon
+attr height
+attr hideOnContentScroll
+attr homeAsUpIndicator
+attr homeLayout
+attr icon
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr isLightTheme
+attr itemPadding
+attr keylines
+attr lastBaselineToBottomHeight
+attr layout
+attr layout_anchor
+attr layout_anchorGravity
+attr layout_behavior
+attr layout_dodgeInsetEdges
+attr layout_insetEdge
+attr layout_keyline
+attr lineHeight
+attr listChoiceBackgroundIndicator
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr logo
+attr logoDescription
+attr maxButtonHeight
+attr measureWithLargestChild
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr radioButtonStyle
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr retryImage
+attr retryImageScaleType
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr showAsAction
+attr showDividers
+attr showText
+attr showTitle
+attr singleChoiceItemLayout
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr state_above_anchor
+attr statusBarBackground
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr textAllCaps
+attr textAppearanceLargePopupMenu
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr theme
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+bool abc_action_bar_embed_tabs
+bool abc_allow_stacked_button_bar
+bool abc_config_actionMenuItemAllCaps
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_input_method_navigation_guard
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color catalyst_logbox_background
+color catalyst_redbox_background
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color tooltip_background_dark
+color tooltip_background_light
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_star_black_16dp
+drawable abc_ic_star_black_36dp
+drawable abc_ic_star_black_48dp
+drawable abc_ic_star_half_black_16dp
+drawable abc_ic_star_half_black_36dp
+drawable abc_ic_star_half_black_48dp
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl_dark
+drawable abc_text_select_handle_left_mtrl_light
+drawable abc_text_select_handle_middle_mtrl_dark
+drawable abc_text_select_handle_middle_mtrl_light
+drawable abc_text_select_handle_right_mtrl_dark
+drawable abc_text_select_handle_right_mtrl_light
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id accessibility_actions
+id accessibility_hint
+id accessibility_label
+id accessibility_role
+id accessibility_state
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id alertTitle
+id async
+id blocking
+id bottom
+id buttonPanel
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id chronometer
+id content
+id contentPanel
+id custom
+id customPanel
+id decor_content_parent
+id default_activity_button
+id edit_query
+id end
+id expand_activities_button
+id expanded_menu
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id focusCrop
+id forever
+id fps_text
+id group_divider
+id home
+id icon
+id icon_group
+id image
+id info
+id italic
+id left
+id line1
+id line3
+id listMode
+id list_item
+id message
+id multiply
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id parentPanel
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id shortcut
+id spacer
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id start
+id submenuarrow
+id submit_area
+id tabMode
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id top
+id topPanel
+id uniform
+id up
+id view_tag_instance_handle
+id view_tag_native_id
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer status_bar_notification_info_maxnum
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout dev_loading_view
+layout fps_view
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_font_family_body_1_material
+string abc_font_family_body_2_material
+string abc_font_family_button_material
+string abc_font_family_caption_material
+string abc_font_family_display_1_material
+string abc_font_family_display_2_material
+string abc_font_family_display_3_material
+string abc_font_family_display_4_material
+string abc_font_family_headline_material
+string abc_font_family_menu_material
+string abc_font_family_subhead_material
+string abc_font_family_title_material
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string button_description
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string combobox_description
+string header_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style DialogAnimationFade
+style DialogAnimationSlide
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style Widget_Support_CoordinatorLayout
+style redboxButton
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType firstBaselineToTopHeight fontFamily lastBaselineToBottomHeight lineHeight textAllCaps
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingLeft listPreferredItemPaddingRight panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable ButtonBarLayout allowStacking
+styleable ColorStateListItem alpha android_alpha android_color
+styleable CompoundButton android_button buttonTint buttonTintMode
+styleable CoordinatorLayout keylines statusBarBackground
+styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textSize android_textStyle android_typeface fontFamily textAllCaps
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..571949e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,1315 @@
+com.reactnativecommunity.clipboard
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr background
+attr backgroundImage
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr barLength
+attr borderlessButtonStyle
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr checkboxStyle
+attr checkedTextViewStyle
+attr closeIcon
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorPrimary
+attr colorPrimaryDark
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr controlBackground
+attr coordinatorLayoutStyle
+attr customNavigationLayout
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableSize
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr expandActivityOverflowButtonDrawable
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr firstBaselineToTopHeight
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr gapBetweenBars
+attr goIcon
+attr height
+attr hideOnContentScroll
+attr homeAsUpIndicator
+attr homeLayout
+attr icon
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr isLightTheme
+attr itemPadding
+attr keylines
+attr lastBaselineToBottomHeight
+attr layout
+attr layout_anchor
+attr layout_anchorGravity
+attr layout_behavior
+attr layout_dodgeInsetEdges
+attr layout_insetEdge
+attr layout_keyline
+attr lineHeight
+attr listChoiceBackgroundIndicator
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr logo
+attr logoDescription
+attr maxButtonHeight
+attr measureWithLargestChild
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr radioButtonStyle
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr retryImage
+attr retryImageScaleType
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr showAsAction
+attr showDividers
+attr showText
+attr showTitle
+attr singleChoiceItemLayout
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr state_above_anchor
+attr statusBarBackground
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr textAllCaps
+attr textAppearanceLargePopupMenu
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr theme
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+bool abc_action_bar_embed_tabs
+bool abc_allow_stacked_button_bar
+bool abc_config_actionMenuItemAllCaps
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_input_method_navigation_guard
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color catalyst_logbox_background
+color catalyst_redbox_background
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color tooltip_background_dark
+color tooltip_background_light
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_star_black_16dp
+drawable abc_ic_star_black_36dp
+drawable abc_ic_star_black_48dp
+drawable abc_ic_star_half_black_16dp
+drawable abc_ic_star_half_black_36dp
+drawable abc_ic_star_half_black_48dp
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl_dark
+drawable abc_text_select_handle_left_mtrl_light
+drawable abc_text_select_handle_middle_mtrl_dark
+drawable abc_text_select_handle_middle_mtrl_light
+drawable abc_text_select_handle_right_mtrl_dark
+drawable abc_text_select_handle_right_mtrl_light
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id accessibility_actions
+id accessibility_hint
+id accessibility_label
+id accessibility_role
+id accessibility_state
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id alertTitle
+id async
+id blocking
+id bottom
+id buttonPanel
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id chronometer
+id content
+id contentPanel
+id custom
+id customPanel
+id decor_content_parent
+id default_activity_button
+id edit_query
+id end
+id expand_activities_button
+id expanded_menu
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id focusCrop
+id forever
+id fps_text
+id group_divider
+id home
+id icon
+id icon_group
+id image
+id info
+id italic
+id left
+id line1
+id line3
+id listMode
+id list_item
+id message
+id multiply
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id parentPanel
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id shortcut
+id spacer
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id start
+id submenuarrow
+id submit_area
+id tabMode
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id top
+id topPanel
+id uniform
+id up
+id view_tag_instance_handle
+id view_tag_native_id
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer status_bar_notification_info_maxnum
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout dev_loading_view
+layout fps_view
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_font_family_body_1_material
+string abc_font_family_body_2_material
+string abc_font_family_button_material
+string abc_font_family_caption_material
+string abc_font_family_display_1_material
+string abc_font_family_display_2_material
+string abc_font_family_display_3_material
+string abc_font_family_display_4_material
+string abc_font_family_headline_material
+string abc_font_family_menu_material
+string abc_font_family_subhead_material
+string abc_font_family_title_material
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string button_description
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string combobox_description
+string header_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style DialogAnimationFade
+style DialogAnimationSlide
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style Widget_Support_CoordinatorLayout
+style redboxButton
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType firstBaselineToTopHeight fontFamily lastBaselineToBottomHeight lineHeight textAllCaps
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingLeft listPreferredItemPaddingRight panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable ButtonBarLayout allowStacking
+styleable ColorStateListItem alpha android_alpha android_color
+styleable CompoundButton android_button buttonTint buttonTintMode
+styleable CoordinatorLayout keylines statusBarBackground
+styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textSize android_textStyle android_typeface fontFamily textAllCaps
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
diff --git a/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..571949e
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1,1315 @@
+com.reactnativecommunity.clipboard
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr background
+attr backgroundImage
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr barLength
+attr borderlessButtonStyle
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr checkboxStyle
+attr checkedTextViewStyle
+attr closeIcon
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorPrimary
+attr colorPrimaryDark
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr controlBackground
+attr coordinatorLayoutStyle
+attr customNavigationLayout
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableSize
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr expandActivityOverflowButtonDrawable
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr firstBaselineToTopHeight
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr gapBetweenBars
+attr goIcon
+attr height
+attr hideOnContentScroll
+attr homeAsUpIndicator
+attr homeLayout
+attr icon
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr isLightTheme
+attr itemPadding
+attr keylines
+attr lastBaselineToBottomHeight
+attr layout
+attr layout_anchor
+attr layout_anchorGravity
+attr layout_behavior
+attr layout_dodgeInsetEdges
+attr layout_insetEdge
+attr layout_keyline
+attr lineHeight
+attr listChoiceBackgroundIndicator
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr logo
+attr logoDescription
+attr maxButtonHeight
+attr measureWithLargestChild
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr radioButtonStyle
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr retryImage
+attr retryImageScaleType
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr showAsAction
+attr showDividers
+attr showText
+attr showTitle
+attr singleChoiceItemLayout
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr state_above_anchor
+attr statusBarBackground
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr textAllCaps
+attr textAppearanceLargePopupMenu
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr theme
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+bool abc_action_bar_embed_tabs
+bool abc_allow_stacked_button_bar
+bool abc_config_actionMenuItemAllCaps
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_input_method_navigation_guard
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color catalyst_logbox_background
+color catalyst_redbox_background
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color tooltip_background_dark
+color tooltip_background_light
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_star_black_16dp
+drawable abc_ic_star_black_36dp
+drawable abc_ic_star_black_48dp
+drawable abc_ic_star_half_black_16dp
+drawable abc_ic_star_half_black_36dp
+drawable abc_ic_star_half_black_48dp
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl_dark
+drawable abc_text_select_handle_left_mtrl_light
+drawable abc_text_select_handle_middle_mtrl_dark
+drawable abc_text_select_handle_middle_mtrl_light
+drawable abc_text_select_handle_right_mtrl_dark
+drawable abc_text_select_handle_right_mtrl_light
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id accessibility_actions
+id accessibility_hint
+id accessibility_label
+id accessibility_role
+id accessibility_state
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id alertTitle
+id async
+id blocking
+id bottom
+id buttonPanel
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id chronometer
+id content
+id contentPanel
+id custom
+id customPanel
+id decor_content_parent
+id default_activity_button
+id edit_query
+id end
+id expand_activities_button
+id expanded_menu
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id focusCrop
+id forever
+id fps_text
+id group_divider
+id home
+id icon
+id icon_group
+id image
+id info
+id italic
+id left
+id line1
+id line3
+id listMode
+id list_item
+id message
+id multiply
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id parentPanel
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id shortcut
+id spacer
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id start
+id submenuarrow
+id submit_area
+id tabMode
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id top
+id topPanel
+id uniform
+id up
+id view_tag_instance_handle
+id view_tag_native_id
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer status_bar_notification_info_maxnum
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout dev_loading_view
+layout fps_view
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_font_family_body_1_material
+string abc_font_family_body_2_material
+string abc_font_family_button_material
+string abc_font_family_caption_material
+string abc_font_family_display_1_material
+string abc_font_family_display_2_material
+string abc_font_family_display_3_material
+string abc_font_family_display_4_material
+string abc_font_family_headline_material
+string abc_font_family_menu_material
+string abc_font_family_subhead_material
+string abc_font_family_title_material
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string button_description
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string combobox_description
+string header_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style DialogAnimationFade
+style DialogAnimationSlide
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style Widget_Support_CoordinatorLayout
+style redboxButton
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType firstBaselineToTopHeight fontFamily lastBaselineToBottomHeight lineHeight textAllCaps
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingLeft listPreferredItemPaddingRight panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable ButtonBarLayout allowStacking
+styleable ColorStateListItem alpha android_alpha android_color
+styleable CompoundButton android_button buttonTint buttonTintMode
+styleable CoordinatorLayout keylines statusBarBackground
+styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textSize android_textStyle android_typeface fontFamily textAllCaps
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
diff --git a/node_modules/@react-native-community/clipboard/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@react-native-community/clipboard/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..dc61765
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,17 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:1-5:12
+	package
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:3:11-55
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:11-69
+uses-sdk
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@react-native-community/clipboard/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/@react-native-community/clipboard/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..d410732
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:1-5:12
+	package
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:3:11-55
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml:2:11-69
+uses-sdk
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/@react-native-community/clipboard/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@react-native-community/clipboard/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/@react-native-community/clipboard/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..3fd5b06
Binary files /dev/null and b/node_modules/@react-native-community/clipboard/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@react-native-community/clipboard/android/build/tmp/compileDebugJavaWithJavac/source-classes-mapping.txt b/node_modules/@react-native-community/clipboard/android/build/tmp/compileDebugJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..bca4b93
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/tmp/compileDebugJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,6 @@
+com/reactnativecommunity/clipboard/ClipboardModule.java
+ com.reactnativecommunity.clipboard.ClipboardModule
+com/reactnativecommunity/clipboard/ClipboardPackage.java
+ com.reactnativecommunity.clipboard.ClipboardPackage
+com/reactnativecommunity/clipboard/BuildConfig.java
+ com.reactnativecommunity.clipboard.BuildConfig
diff --git a/node_modules/@react-native-community/clipboard/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/@react-native-community/clipboard/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..bca4b93
--- /dev/null
+++ b/node_modules/@react-native-community/clipboard/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,6 @@
+com/reactnativecommunity/clipboard/ClipboardModule.java
+ com.reactnativecommunity.clipboard.ClipboardModule
+com/reactnativecommunity/clipboard/ClipboardPackage.java
+ com.reactnativecommunity.clipboard.ClipboardPackage
+com/reactnativecommunity/clipboard/BuildConfig.java
+ com.reactnativecommunity.clipboard.BuildConfig
