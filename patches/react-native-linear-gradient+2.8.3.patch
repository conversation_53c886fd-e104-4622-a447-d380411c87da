diff --git a/node_modules/react-native-linear-gradient/android/.classpath b/node_modules/react-native-linear-gradient/android/.classpath
new file mode 100644
index 0000000..bbe97e5
--- /dev/null
+++ b/node_modules/react-native-linear-gradient/android/.classpath
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<classpath>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
+	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
+	<classpathentry kind="output" path="bin/default"/>
+</classpath>
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/8.9/checksums/checksums.lock b/node_modules/react-native-linear-gradient/android/.gradle/8.9/checksums/checksums.lock
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/8.9/dependencies-accessors/gc.properties b/node_modules/react-native-linear-gradient/android/.gradle/8.9/dependencies-accessors/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/8.9/fileChanges/last-build.bin b/node_modules/react-native-linear-gradient/android/.gradle/8.9/fileChanges/last-build.bin
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/8.9/fileHashes/fileHashes.lock b/node_modules/react-native-linear-gradient/android/.gradle/8.9/fileHashes/fileHashes.lock
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/8.9/gc.properties b/node_modules/react-native-linear-gradient/android/.gradle/8.9/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/buildOutputCleanup/buildOutputCleanup.lock b/node_modules/react-native-linear-gradient/android/.gradle/buildOutputCleanup/buildOutputCleanup.lock
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/buildOutputCleanup/cache.properties b/node_modules/react-native-linear-gradient/android/.gradle/buildOutputCleanup/cache.properties
new file mode 100644
index 0000000..9e50fdd
--- /dev/null
+++ b/node_modules/react-native-linear-gradient/android/.gradle/buildOutputCleanup/cache.properties
@@ -0,0 +1,2 @@
+#Sun Mar 09 22:53:29 IST 2025
+gradle.version=8.9
diff --git a/node_modules/react-native-linear-gradient/android/.gradle/vcs-1/gc.properties b/node_modules/react-native-linear-gradient/android/.gradle/vcs-1/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-linear-gradient/android/.project b/node_modules/react-native-linear-gradient/android/.project
new file mode 100644
index 0000000..619a4a8
--- /dev/null
+++ b/node_modules/react-native-linear-gradient/android/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-linear-gradient</name>
+	<comment>Project react-native-linear-gradient created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819019</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-linear-gradient/android/.settings/org.eclipse.buildship.core.prefs b/node_modules/react-native-linear-gradient/android/.settings/org.eclipse.buildship.core.prefs
new file mode 100644
index 0000000..0fbcc01
--- /dev/null
+++ b/node_modules/react-native-linear-gradient/android/.settings/org.eclipse.buildship.core.prefs
@@ -0,0 +1,13 @@
+arguments=--init-script /var/folders/fl/yx6lbnxx56g0gw339cg1qtm00000gn/T/db3b08fc4a9ef609cb16b96b200fa13e563f396e9bb1ed0905fdab7bc3bc513b.gradle --init-script /var/folders/fl/yx6lbnxx56g0gw339cg1qtm00000gn/T/52cde0cfcf3e28b8b7510e992210d9614505e0911af0c190bd590d7158574963.gradle
+auto.sync=true
+build.scans.enabled=false
+connection.gradle.distribution=GRADLE_DISTRIBUTION(VERSION(8.9))
+connection.project.dir=../../../android
+eclipse.preferences.version=1
+gradle.user.home=
+java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.12/Contents/Home
+jvm.arguments=
+offline.mode=false
+override.workspace.settings=true
+show.console.view=true
+show.executions.view=true
diff --git a/node_modules/react-native-linear-gradient/android/.settings/org.eclipse.jdt.core.prefs b/node_modules/react-native-linear-gradient/android/.settings/org.eclipse.jdt.core.prefs
new file mode 100644
index 0000000..626e0e1
--- /dev/null
+++ b/node_modules/react-native-linear-gradient/android/.settings/org.eclipse.jdt.core.prefs
@@ -0,0 +1,4 @@
+eclipse.preferences.version=1
+org.eclipse.jdt.core.compiler.codegen.targetPlatform=17
+org.eclipse.jdt.core.compiler.compliance=17
+org.eclipse.jdt.core.compiler.source=17
diff --git a/node_modules/react-native-linear-gradient/android/build.gradle b/node_modules/react-native-linear-gradient/android/build.gradle
index e3aacde..27ffc96 100644
--- a/node_modules/react-native-linear-gradient/android/build.gradle
+++ b/node_modules/react-native-linear-gradient/android/build.gradle
@@ -21,6 +21,7 @@ apply plugin: 'com.android.library'
 
 android {
     compileSdkVersion safeExtGet('compileSdkVersion', 31).toInteger()
+    namespace "com.BV.LinearGradient"
     defaultConfig {
         minSdkVersion safeExtGet('minSdkVersion', 21)
         targetSdkVersion safeExtGet('targetSdkVersion', 31)
