diff --git a/node_modules/react-native-safe-area-context/android/.classpath b/node_modules/react-native-safe-area-context/android/.classpath
new file mode 100644
index 0000000..bbe97e5
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/.classpath
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<classpath>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
+	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
+	<classpathentry kind="output" path="bin/default"/>
+</classpath>
diff --git a/node_modules/react-native-safe-area-context/android/.project b/node_modules/react-native-safe-area-context/android/.project
new file mode 100644
index 0000000..2b5d4f0
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-safe-area-context</name>
+	<comment>Project react-native-safe-area-context created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819025</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-safe-area-context/android/.settings/org.eclipse.buildship.core.prefs b/node_modules/react-native-safe-area-context/android/.settings/org.eclipse.buildship.core.prefs
new file mode 100644
index 0000000..1675490
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/.settings/org.eclipse.buildship.core.prefs
@@ -0,0 +1,2 @@
+connection.project.dir=../../../android
+eclipse.preferences.version=1
diff --git a/node_modules/react-native-safe-area-context/android/bin/.project b/node_modules/react-native-safe-area-context/android/bin/.project
new file mode 100644
index 0000000..2b5d4f0
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-safe-area-context</name>
+	<comment>Project react-native-safe-area-context created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819025</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-safe-area-context/android/bin/build.gradle b/node_modules/react-native-safe-area-context/android/bin/build.gradle
new file mode 100644
index 0000000..512702a
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build.gradle
@@ -0,0 +1,46 @@
+def getExtOrDefault(name, defaultValue) {
+  return rootProject.ext.has(name) ? rootProject.ext.get(name) : defaultValue
+}
+
+buildscript {
+    // The Android Gradle plugin is only required when opening the android folder stand-alone.
+    // This avoids unnecessary downloads and potential conflicts when the library is included as a
+    // module dependency in an application project.
+    if (project == rootProject) {
+        repositories {
+            google()
+            jcenter()
+        }
+        dependencies {
+            classpath 'com.android.tools.build:gradle:4.1.2'
+        }
+    }
+}
+
+apply plugin: 'com.android.library'
+
+android {
+  compileSdkVersion getExtOrDefault('compileSdkVersion', 28)
+
+  defaultConfig {
+    minSdkVersion getExtOrDefault('minSdkVersion', 16)
+    targetSdkVersion getExtOrDefault('targetSdkVersion', 28)
+  }
+  lintOptions{
+    abortOnError false
+  }
+}
+
+repositories {
+    google()
+    maven {
+        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
+        url "$rootDir/../node_modules/react-native/android"
+    }
+    jcenter()
+}
+
+dependencies {
+  //noinspection GradleDynamicVersion
+  implementation 'com.facebook.react:react-native:+'
+}
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..8eec9aa
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.th3rdwave.safeareacontext" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..597d6e0
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.th3rdwave.safeareacontext",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..c90a353
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sat Mar 22 07:28:40 IST 2025
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..830a6de
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..db67ad1
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,8 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.th3rdwave.safeareacontext" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+5-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+6
+7</manifest>
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..8eec9aa
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.th3rdwave.safeareacontext" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..ae73171
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.th3rdwave.safeareacontext",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-safe-area-context/android/bin/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-safe-area-context/android/bin/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..f3df02f
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml:2:1-6:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml:2:1-6:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml:2:1-6:12
+	package
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml:4:2-41
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml:3:2-60
+uses-sdk
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/AndroidManifest.xml b/node_modules/react-native-safe-area-context/android/bin/src/main/AndroidManifest.xml
new file mode 100644
index 0000000..479a914
--- /dev/null
+++ b/node_modules/react-native-safe-area-context/android/bin/src/main/AndroidManifest.xml
@@ -0,0 +1,6 @@
+
+<manifest
+	xmlns:android="http://schemas.android.com/apk/res/android"
+	package="com.th3rdwave.safeareacontext">
+
+</manifest>
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/EdgeInsets.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/EdgeInsets.class
new file mode 100644
index 0000000..2422653
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/EdgeInsets.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/InsetsChangeEvent.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/InsetsChangeEvent.class
new file mode 100644
index 0000000..8279a29
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/InsetsChangeEvent.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/Rect.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/Rect.class
new file mode 100644
index 0000000..4d469ca
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/Rect.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.class
new file mode 100644
index 0000000..96cd5ad
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProvider$OnInsetsChangeListener.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProvider$OnInsetsChangeListener.class
new file mode 100644
index 0000000..e69835c
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProvider$OnInsetsChangeListener.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProvider.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProvider.class
new file mode 100644
index 0000000..63d2e01
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProvider.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProviderManager.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProviderManager.class
new file mode 100644
index 0000000..f0a8091
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProviderManager.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaUtils.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaUtils.class
new file mode 100644
index 0000000..1f2da91
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaUtils.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaView.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaView.class
new file mode 100644
index 0000000..1ea2456
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaView.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewEdges.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewEdges.class
new file mode 100644
index 0000000..d7e21b4
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewEdges.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewLocalData.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewLocalData.class
new file mode 100644
index 0000000..d32e417
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewLocalData.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewManager.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewManager.class
new file mode 100644
index 0000000..29e8476
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewManager.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewMode.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewMode.class
new file mode 100644
index 0000000..6c95eb7
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewMode.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewShadowNode.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewShadowNode.class
new file mode 100644
index 0000000..c6f9a11
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewShadowNode.class differ
diff --git a/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SerializationUtils.class b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SerializationUtils.class
new file mode 100644
index 0000000..0e20ba7
Binary files /dev/null and b/node_modules/react-native-safe-area-context/android/bin/src/main/java/com/th3rdwave/safeareacontext/SerializationUtils.class differ
diff --git a/node_modules/react-native-safe-area-context/android/build.gradle b/node_modules/react-native-safe-area-context/android/build.gradle
index 512702a..d2b6f8c 100644
--- a/node_modules/react-native-safe-area-context/android/build.gradle
+++ b/node_modules/react-native-safe-area-context/android/build.gradle
@@ -21,6 +21,7 @@ apply plugin: 'com.android.library'
 
 android {
   compileSdkVersion getExtOrDefault('compileSdkVersion', 28)
+  namespace "com.th3rdwave.safeareacontext"
 
   defaultConfig {
     minSdkVersion getExtOrDefault('minSdkVersion', 16)
