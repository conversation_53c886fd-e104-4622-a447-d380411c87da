diff --git a/node_modules/react-native-reanimated/android/.project b/node_modules/react-native-reanimated/android/.project
new file mode 100644
index 0000000..f17663c
--- /dev/null
+++ b/node_modules/react-native-reanimated/android/.project
@@ -0,0 +1,28 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-reanimated</name>
+	<comment>Project react-native-reanimated created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819024</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-reanimated/android/gradle.properties b/node_modules/react-native-reanimated/android/gradle.properties
index a67829d..2320943 100644
--- a/node_modules/react-native-reanimated/android/gradle.properties
+++ b/node_modules/react-native-reanimated/android/gradle.properties
@@ -21,5 +21,7 @@ org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryErro
 # http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
 # org.gradle.parallel=true
 
+hermesEnabled=false
+
 android.useAndroidX=true
 android.enableJetifier=true
