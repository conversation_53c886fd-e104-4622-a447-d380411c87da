diff --git a/node_modules/react-native-screens/android/.classpath b/node_modules/react-native-screens/android/.classpath
new file mode 100644
index 0000000..bbe97e5
--- /dev/null
+++ b/node_modules/react-native-screens/android/.classpath
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<classpath>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
+	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
+	<classpathentry kind="output" path="bin/default"/>
+</classpath>
diff --git a/node_modules/react-native-screens/android/.project b/node_modules/react-native-screens/android/.project
new file mode 100644
index 0000000..42b93ef
--- /dev/null
+++ b/node_modules/react-native-screens/android/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-screens</name>
+	<comment>Project react-native-screens created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819026</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-screens/android/.settings/org.eclipse.buildship.core.prefs b/node_modules/react-native-screens/android/.settings/org.eclipse.buildship.core.prefs
new file mode 100644
index 0000000..1675490
--- /dev/null
+++ b/node_modules/react-native-screens/android/.settings/org.eclipse.buildship.core.prefs
@@ -0,0 +1,2 @@
+connection.project.dir=../../../android
+eclipse.preferences.version=1
diff --git a/node_modules/react-native-screens/android/bin/.project b/node_modules/react-native-screens/android/bin/.project
new file mode 100644
index 0000000..42b93ef
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/.project
@@ -0,0 +1,34 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>react-native-screens</name>
+	<comment>Project react-native-screens created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+	<filteredResources>
+		<filter>
+			<id>1728050819026</id>
+			<name></name>
+			<type>30</type>
+			<matcher>
+				<id>org.eclipse.core.resources.regexFilterMatcher</id>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+			</matcher>
+		</filter>
+	</filteredResources>
+</projectDescription>
diff --git a/node_modules/react-native-screens/android/bin/build.gradle b/node_modules/react-native-screens/android/bin/build.gradle
new file mode 100644
index 0000000..4acf712
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build.gradle
@@ -0,0 +1,42 @@
+apply plugin: 'com.android.library'
+
+def safeExtGet(prop, fallback) {
+    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
+}
+
+android {
+    compileSdkVersion safeExtGet('compileSdkVersion', 28)
+
+    defaultConfig {
+        minSdkVersion safeExtGet('minSdkVersion', 16)
+        targetSdkVersion safeExtGet('targetSdkVersion', 22)
+        versionCode 1
+        versionName "1.0"
+    }
+    lintOptions {
+        abortOnError false
+    }
+}
+
+repositories {
+    maven {
+        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
+        // Matches the RN Hello World template
+        // https://github.com/facebook/react-native/blob/1e8f3b11027fe0a7514b4fc97d0798d3c64bc895/local-cli/templates/HelloWorld/android/build.gradle#L21
+        url "$projectDir/../node_modules/react-native/android"
+    }
+    mavenCentral()
+    mavenLocal()
+    google()
+    jcenter()
+
+}
+
+dependencies {
+    implementation 'com.facebook.react:react-native:+'
+    implementation 'androidx.appcompat:appcompat:1.1.0'
+    implementation 'androidx.fragment:fragment:1.2.1'
+    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.1.0'
+    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.0.0'
+    implementation 'com.google.android.material:material:1.1.0'
+}
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-screens/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..d64ed47
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.swmansion.rnscreens" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-screens/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..37294a0
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.swmansion.rnscreens",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-screens/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-screens/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..a8a883d
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,5 @@
+#Sat Mar 22 07:28:40 IST 2025
+com.swmansion.rnscreens.react-native-screens-main-7\:/anim/rns_slide_in_from_right.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml
+com.swmansion.rnscreens.react-native-screens-main-7\:/anim/rns_slide_out_to_right.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml
+com.swmansion.rnscreens.react-native-screens-main-7\:/anim/rns_slide_in_from_left.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml
+com.swmansion.rnscreens.react-native-screens-main-7\:/anim/rns_slide_out_to_left.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-screens/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..3902a79
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res"><file name="rns_slide_in_from_right" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_in_from_right.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_right" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_out_to_right.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_left" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_out_to_left.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_left" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_in_from_left.xml" qualifiers="" type="anim"/></source><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-screens/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..496a8b8
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,8 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.swmansion.rnscreens" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+5-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+6
+7</manifest>
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-screens/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..d64ed47
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.swmansion.rnscreens" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-screens/android/bin/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..402190a
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.swmansion.rnscreens",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml
new file mode 100644
index 0000000..939110f
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="-100%"
+    android:toXDelta="0%" />
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml
new file mode 100644
index 0000000..428eb9b
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="100%"
+    android:toXDelta="0%" />
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml
new file mode 100644
index 0000000..400a202
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="0%"
+    android:toXDelta="-100%"/>
diff --git a/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml
new file mode 100644
index 0000000..a00332b
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="0%"
+    android:toXDelta="100%"/>
diff --git a/node_modules/react-native-screens/android/bin/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-screens/android/bin/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..61fd3a4
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:1-5:12
+	package
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:3:11-44
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:11-69
+uses-sdk
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-screens/android/bin/src/main/AndroidManifest.xml b/node_modules/react-native-screens/android/bin/src/main/AndroidManifest.xml
new file mode 100644
index 0000000..2de74e8
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/src/main/AndroidManifest.xml
@@ -0,0 +1,5 @@
+
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+          package="com.swmansion.rnscreens">
+
+</manifest>
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/LifecycleHelper.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/LifecycleHelper.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/RNScreensPackage.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/RNScreensPackage.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$ActivityState.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$ActivityState.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$ReplaceAnimation.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$ReplaceAnimation.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$StackAnimation.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$StackAnimation.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$StackPresentation.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$StackPresentation.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$WindowTraits.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen$WindowTraits.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/Screen.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenAppearEvent.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenAppearEvent.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenContainer.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenContainer.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenContainerViewManager.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenContainerViewManager.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenDisappearEvent.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenDisappearEvent.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenDismissedEvent.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenDismissedEvent.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenFragment.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenFragment.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStack.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStack.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfig.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfig.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubview.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubview.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackViewManager.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenStackViewManager.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenViewManager.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenViewManager.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenWillAppearEvent.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenWillAppearEvent.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenWillDisappearEvent.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenWillDisappearEvent.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/StackFinishTransitioningEvent.class b/node_modules/react-native-screens/android/bin/src/main/java/com/swmansion/rnscreens/StackFinishTransitioningEvent.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_in_from_left.xml b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_in_from_left.xml
new file mode 100644
index 0000000..939110f
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_in_from_left.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="-100%"
+    android:toXDelta="0%" />
diff --git a/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_in_from_right.xml b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_in_from_right.xml
new file mode 100644
index 0000000..428eb9b
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_in_from_right.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="100%"
+    android:toXDelta="0%" />
diff --git a/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_out_to_left.xml b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_out_to_left.xml
new file mode 100644
index 0000000..400a202
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_out_to_left.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="0%"
+    android:toXDelta="-100%"/>
diff --git a/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_out_to_right.xml b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_out_to_right.xml
new file mode 100644
index 0000000..a00332b
--- /dev/null
+++ b/node_modules/react-native-screens/android/bin/src/main/res/anim/rns_slide_out_to_right.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="0%"
+    android:toXDelta="100%"/>
diff --git a/node_modules/react-native-screens/android/build.gradle b/node_modules/react-native-screens/android/build.gradle
index 4acf712..3d2dc62 100644
--- a/node_modules/react-native-screens/android/build.gradle
+++ b/node_modules/react-native-screens/android/build.gradle
@@ -6,7 +6,7 @@ def safeExtGet(prop, fallback) {
 
 android {
     compileSdkVersion safeExtGet('compileSdkVersion', 28)
-
+    namespace "com.swmansion.rnscreens"
     defaultConfig {
         minSdkVersion safeExtGet('minSdkVersion', 16)
         targetSdkVersion safeExtGet('targetSdkVersion', 22)
diff --git a/node_modules/react-native-screens/android/build/.transforms/2d2ee7815fa8c9cd2948166cfc2e3168/results.bin b/node_modules/react-native-screens/android/build/.transforms/2d2ee7815fa8c9cd2948166cfc2e3168/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/.transforms/2d2ee7815fa8c9cd2948166cfc2e3168/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/results.bin b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/BuildConfig.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/BuildConfig.dex
new file mode 100644
index 0000000..4c6fd56
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/BuildConfig.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/LifecycleHelper$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/LifecycleHelper$1.dex
new file mode 100644
index 0000000..572daf8
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/LifecycleHelper$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/LifecycleHelper.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/LifecycleHelper.dex
new file mode 100644
index 0000000..9905e8d
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/LifecycleHelper.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/RNScreensPackage.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/RNScreensPackage.dex
new file mode 100644
index 0000000..af21530
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/RNScreensPackage.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$1.dex
new file mode 100644
index 0000000..d10fb56
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$2.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$2.dex
new file mode 100644
index 0000000..e51e765
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$2.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$ActivityState.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$ActivityState.dex
new file mode 100644
index 0000000..983592c
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$ActivityState.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$ReplaceAnimation.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$ReplaceAnimation.dex
new file mode 100644
index 0000000..50cd1ee
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$ReplaceAnimation.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$StackAnimation.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$StackAnimation.dex
new file mode 100644
index 0000000..c859480
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$StackAnimation.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$StackPresentation.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$StackPresentation.dex
new file mode 100644
index 0000000..abac7a0
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$StackPresentation.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$WindowTraits.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$WindowTraits.dex
new file mode 100644
index 0000000..6eb676d
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen$WindowTraits.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen.dex
new file mode 100644
index 0000000..e3810ae
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/Screen.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenAppearEvent.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenAppearEvent.dex
new file mode 100644
index 0000000..2c61b4b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenAppearEvent.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$1.dex
new file mode 100644
index 0000000..0a519bd
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$2.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$2.dex
new file mode 100644
index 0000000..383dc67
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$2.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$3.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$3.dex
new file mode 100644
index 0000000..8063c09
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer$3.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer.dex
new file mode 100644
index 0000000..7407754
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainer.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainerViewManager.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainerViewManager.dex
new file mode 100644
index 0000000..fd436d4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenContainerViewManager.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenDisappearEvent.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenDisappearEvent.dex
new file mode 100644
index 0000000..a287ee9
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenDisappearEvent.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenDismissedEvent.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenDismissedEvent.dex
new file mode 100644
index 0000000..afbf4d5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenDismissedEvent.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment$1.dex
new file mode 100644
index 0000000..f7666cb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment$2.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment$2.dex
new file mode 100644
index 0000000..60e6916
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment$2.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment.dex
new file mode 100644
index 0000000..c11f9bb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenFragment.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$1.dex
new file mode 100644
index 0000000..8d63be6
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$2.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$2.dex
new file mode 100644
index 0000000..a5a3a5a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$2.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$3.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$3.dex
new file mode 100644
index 0000000..65439ec
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$3.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$4.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$4.dex
new file mode 100644
index 0000000..9a51661
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack$4.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack.dex
new file mode 100644
index 0000000..25f6d1b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStack.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$1.dex
new file mode 100644
index 0000000..ea6c26e
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.dex
new file mode 100644
index 0000000..d025ee5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.dex
new file mode 100644
index 0000000..c4cc6a7
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment.dex
new file mode 100644
index 0000000..0331145
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackFragment.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.dex
new file mode 100644
index 0000000..2f0920b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.dex
new file mode 100644
index 0000000..0f780b7
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.dex
new file mode 100644
index 0000000..898c8bb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig.dex
new file mode 100644
index 0000000..69d1e6c
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.dex
new file mode 100644
index 0000000..e329c85
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.dex
new file mode 100644
index 0000000..f4bdff6
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview.dex
new file mode 100644
index 0000000..6b4b144
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.dex
new file mode 100644
index 0000000..0964d11
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackViewManager.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackViewManager.dex
new file mode 100644
index 0000000..a7d347a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenStackViewManager.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenViewManager.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenViewManager.dex
new file mode 100644
index 0000000..e4dd453
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenViewManager.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWillAppearEvent.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWillAppearEvent.dex
new file mode 100644
index 0000000..4698b72
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWillAppearEvent.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWillDisappearEvent.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWillDisappearEvent.dex
new file mode 100644
index 0000000..32b2b14
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWillDisappearEvent.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$1$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$1$1.dex
new file mode 100644
index 0000000..a2ebbd5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$1$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$1.dex
new file mode 100644
index 0000000..3d101c2
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$2.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$2.dex
new file mode 100644
index 0000000..3a5edf5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$2.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$3$1.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$3$1.dex
new file mode 100644
index 0000000..af29db8
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$3$1.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$3.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$3.dex
new file mode 100644
index 0000000..557205f
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$3.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$4.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$4.dex
new file mode 100644
index 0000000..b73fd02
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$4.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$5.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$5.dex
new file mode 100644
index 0000000..dd64d34
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits$5.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits.dex
new file mode 100644
index 0000000..cd85e77
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/ScreenWindowTraits.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/StackFinishTransitioningEvent.dex b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/StackFinishTransitioningEvent.dex
new file mode 100644
index 0000000..8f85966
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/debug/com/swmansion/rnscreens/StackFinishTransitioningEvent.dex differ
diff --git a/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/desugar_graph.bin b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/desugar_graph.bin
new file mode 100644
index 0000000..f535008
Binary files /dev/null and b/node_modules/react-native-screens/android/build/.transforms/85100762422088ccfe44cdbc9649f540/transformed/desugar_graph.bin differ
diff --git a/node_modules/react-native-screens/android/build/generated/source/buildConfig/debug/com/swmansion/rnscreens/BuildConfig.java b/node_modules/react-native-screens/android/build/generated/source/buildConfig/debug/com/swmansion/rnscreens/BuildConfig.java
new file mode 100644
index 0000000..392ba85
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/generated/source/buildConfig/debug/com/swmansion/rnscreens/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.swmansion.rnscreens;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.swmansion.rnscreens";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-screens/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-screens/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..d64ed47
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.swmansion.rnscreens" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-screens/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..37294a0
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.swmansion.rnscreens",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-screens/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-screens/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-screens/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-screens/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..a5d07b9
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-screens/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..ebd3ba7
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-screens/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..03be0c1
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,3642 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim btn_checkbox_to_checked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_checked_box_outer_merged_animation 0x0
+int anim btn_checkbox_to_checked_icon_null_animation 0x0
+int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_icon_null_animation 0x0
+int anim btn_radio_to_off_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x0
+int anim btn_radio_to_on_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int anim design_bottom_sheet_slide_in 0x0
+int anim design_bottom_sheet_slide_out 0x0
+int anim design_snackbar_in 0x0
+int anim design_snackbar_out 0x0
+int anim fragment_fast_out_extra_slow_in 0x0
+int anim mtrl_bottom_sheet_slide_in 0x0
+int anim mtrl_bottom_sheet_slide_out 0x0
+int anim mtrl_card_lowers_interpolator 0x0
+int anim rns_slide_in_from_left 0x0
+int anim rns_slide_in_from_right 0x0
+int anim rns_slide_out_to_left 0x0
+int anim rns_slide_out_to_right 0x0
+int animator design_appbar_state_list_animator 0x0
+int animator design_fab_hide_motion_spec 0x0
+int animator design_fab_show_motion_spec 0x0
+int animator fragment_close_enter 0x0
+int animator fragment_close_exit 0x0
+int animator fragment_fade_enter 0x0
+int animator fragment_fade_exit 0x0
+int animator fragment_open_enter 0x0
+int animator fragment_open_exit 0x0
+int animator mtrl_btn_state_list_anim 0x0
+int animator mtrl_btn_unelevated_state_list_anim 0x0
+int animator mtrl_card_state_list_anim 0x0
+int animator mtrl_chip_state_list_anim 0x0
+int animator mtrl_extended_fab_change_size_motion_spec 0x0
+int animator mtrl_extended_fab_hide_motion_spec 0x0
+int animator mtrl_extended_fab_show_motion_spec 0x0
+int animator mtrl_extended_fab_state_list_animator 0x0
+int animator mtrl_fab_hide_motion_spec 0x0
+int animator mtrl_fab_show_motion_spec 0x0
+int animator mtrl_fab_transformation_sheet_collapse_spec 0x0
+int animator mtrl_fab_transformation_sheet_expand_spec 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseContentDescription 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeTheme 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionTextColorAlpha 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr animationMode 0x0
+int attr appBarLayoutStyle 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr autofillInlineSuggestionChip 0x0
+int attr autofillInlineSuggestionEndIconStyle 0x0
+int attr autofillInlineSuggestionStartIconStyle 0x0
+int attr autofillInlineSuggestionSubtitle 0x0
+int attr autofillInlineSuggestionTitle 0x0
+int attr background 0x0
+int attr backgroundColor 0x0
+int attr backgroundImage 0x0
+int attr backgroundInsetBottom 0x0
+int attr backgroundInsetEnd 0x0
+int attr backgroundInsetStart 0x0
+int attr backgroundInsetTop 0x0
+int attr backgroundOverlayColorAlpha 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr badgeGravity 0x0
+int attr badgeStyle 0x0
+int attr badgeTextColor 0x0
+int attr barLength 0x0
+int attr behavior_autoHide 0x0
+int attr behavior_autoShrink 0x0
+int attr behavior_expandedOffset 0x0
+int attr behavior_fitToContents 0x0
+int attr behavior_halfExpandedRatio 0x0
+int attr behavior_hideable 0x0
+int attr behavior_overlapTop 0x0
+int attr behavior_peekHeight 0x0
+int attr behavior_saveFlags 0x0
+int attr behavior_skipCollapsed 0x0
+int attr borderWidth 0x0
+int attr borderlessButtonStyle 0x0
+int attr bottomAppBarStyle 0x0
+int attr bottomNavigationStyle 0x0
+int attr bottomSheetDialogTheme 0x0
+int attr bottomSheetStyle 0x0
+int attr boxBackgroundColor 0x0
+int attr boxBackgroundMode 0x0
+int attr boxCollapsedPaddingTop 0x0
+int attr boxCornerRadiusBottomEnd 0x0
+int attr boxCornerRadiusBottomStart 0x0
+int attr boxCornerRadiusTopEnd 0x0
+int attr boxCornerRadiusTopStart 0x0
+int attr boxStrokeColor 0x0
+int attr boxStrokeWidth 0x0
+int attr boxStrokeWidthFocused 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonCompat 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr cardBackgroundColor 0x0
+int attr cardCornerRadius 0x0
+int attr cardElevation 0x0
+int attr cardForegroundColor 0x0
+int attr cardMaxElevation 0x0
+int attr cardPreventCornerOverlap 0x0
+int attr cardUseCompatPadding 0x0
+int attr cardViewStyle 0x0
+int attr checkMarkCompat 0x0
+int attr checkMarkTint 0x0
+int attr checkMarkTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedButton 0x0
+int attr checkedChip 0x0
+int attr checkedIcon 0x0
+int attr checkedIconEnabled 0x0
+int attr checkedIconTint 0x0
+int attr checkedIconVisible 0x0
+int attr checkedTextViewStyle 0x0
+int attr chipBackgroundColor 0x0
+int attr chipCornerRadius 0x0
+int attr chipEndPadding 0x0
+int attr chipGroupStyle 0x0
+int attr chipIcon 0x0
+int attr chipIconEnabled 0x0
+int attr chipIconSize 0x0
+int attr chipIconTint 0x0
+int attr chipIconVisible 0x0
+int attr chipMinHeight 0x0
+int attr chipMinTouchTargetSize 0x0
+int attr chipSpacing 0x0
+int attr chipSpacingHorizontal 0x0
+int attr chipSpacingVertical 0x0
+int attr chipStandaloneStyle 0x0
+int attr chipStartPadding 0x0
+int attr chipStrokeColor 0x0
+int attr chipStrokeWidth 0x0
+int attr chipStyle 0x0
+int attr chipSurfaceColor 0x0
+int attr closeIcon 0x0
+int attr closeIconEnabled 0x0
+int attr closeIconEndPadding 0x0
+int attr closeIconSize 0x0
+int attr closeIconStartPadding 0x0
+int attr closeIconTint 0x0
+int attr closeIconVisible 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr collapsedTitleGravity 0x0
+int attr collapsedTitleTextAppearance 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorOnBackground 0x0
+int attr colorOnError 0x0
+int attr colorOnPrimary 0x0
+int attr colorOnPrimarySurface 0x0
+int attr colorOnSecondary 0x0
+int attr colorOnSurface 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorPrimarySurface 0x0
+int attr colorPrimaryVariant 0x0
+int attr colorSecondary 0x0
+int attr colorSecondaryVariant 0x0
+int attr colorSurface 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr contentPadding 0x0
+int attr contentPaddingBottom 0x0
+int attr contentPaddingLeft 0x0
+int attr contentPaddingRight 0x0
+int attr contentPaddingTop 0x0
+int attr contentScrim 0x0
+int attr controlBackground 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr cornerFamily 0x0
+int attr cornerFamilyBottomLeft 0x0
+int attr cornerFamilyBottomRight 0x0
+int attr cornerFamilyTopLeft 0x0
+int attr cornerFamilyTopRight 0x0
+int attr cornerRadius 0x0
+int attr cornerSize 0x0
+int attr cornerSizeBottomLeft 0x0
+int attr cornerSizeBottomRight 0x0
+int attr cornerSizeTopLeft 0x0
+int attr cornerSizeTopRight 0x0
+int attr counterEnabled 0x0
+int attr counterMaxLength 0x0
+int attr counterOverflowTextAppearance 0x0
+int attr counterOverflowTextColor 0x0
+int attr counterTextAppearance 0x0
+int attr counterTextColor 0x0
+int attr customNavigationLayout 0x0
+int attr dayInvalidStyle 0x0
+int attr daySelectedStyle 0x0
+int attr dayStyle 0x0
+int attr dayTodayStyle 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableBottomCompat 0x0
+int attr drawableEndCompat 0x0
+int attr drawableLeftCompat 0x0
+int attr drawableRightCompat 0x0
+int attr drawableSize 0x0
+int attr drawableStartCompat 0x0
+int attr drawableTint 0x0
+int attr drawableTintMode 0x0
+int attr drawableTopCompat 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr elevationOverlayColor 0x0
+int attr elevationOverlayEnabled 0x0
+int attr emojiCompatEnabled 0x0
+int attr endIconCheckable 0x0
+int attr endIconContentDescription 0x0
+int attr endIconDrawable 0x0
+int attr endIconMode 0x0
+int attr endIconTint 0x0
+int attr endIconTintMode 0x0
+int attr enforceMaterialTheme 0x0
+int attr enforceTextAppearance 0x0
+int attr ensureMinTouchTargetSize 0x0
+int attr errorEnabled 0x0
+int attr errorIconDrawable 0x0
+int attr errorIconTint 0x0
+int attr errorIconTintMode 0x0
+int attr errorTextAppearance 0x0
+int attr errorTextColor 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr expanded 0x0
+int attr expandedTitleGravity 0x0
+int attr expandedTitleMargin 0x0
+int attr expandedTitleMarginBottom 0x0
+int attr expandedTitleMarginEnd 0x0
+int attr expandedTitleMarginStart 0x0
+int attr expandedTitleMarginTop 0x0
+int attr expandedTitleTextAppearance 0x0
+int attr extendMotionSpec 0x0
+int attr extendedFloatingActionButtonStyle 0x0
+int attr fabAlignmentMode 0x0
+int attr fabAnimationMode 0x0
+int attr fabCradleMargin 0x0
+int attr fabCradleRoundedCornerRadius 0x0
+int attr fabCradleVerticalOffset 0x0
+int attr fabCustomSize 0x0
+int attr fabSize 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr fastScrollEnabled 0x0
+int attr fastScrollHorizontalThumbDrawable 0x0
+int attr fastScrollHorizontalTrackDrawable 0x0
+int attr fastScrollVerticalThumbDrawable 0x0
+int attr fastScrollVerticalTrackDrawable 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr floatingActionButtonStyle 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontProviderSystemFontFamily 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr foregroundInsidePadding 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr headerLayout 0x0
+int attr height 0x0
+int attr helperText 0x0
+int attr helperTextEnabled 0x0
+int attr helperTextTextAppearance 0x0
+int attr helperTextTextColor 0x0
+int attr hideMotionSpec 0x0
+int attr hideOnContentScroll 0x0
+int attr hideOnScroll 0x0
+int attr hintAnimationEnabled 0x0
+int attr hintEnabled 0x0
+int attr hintTextAppearance 0x0
+int attr hintTextColor 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr hoveredFocusedTranslationZ 0x0
+int attr icon 0x0
+int attr iconEndPadding 0x0
+int attr iconGravity 0x0
+int attr iconPadding 0x0
+int attr iconSize 0x0
+int attr iconStartPadding 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr insetForeground 0x0
+int attr isAutofillInlineSuggestionTheme 0x0
+int attr isLightTheme 0x0
+int attr isMaterialTheme 0x0
+int attr itemBackground 0x0
+int attr itemFillColor 0x0
+int attr itemHorizontalPadding 0x0
+int attr itemHorizontalTranslationEnabled 0x0
+int attr itemIconPadding 0x0
+int attr itemIconSize 0x0
+int attr itemIconTint 0x0
+int attr itemMaxLines 0x0
+int attr itemPadding 0x0
+int attr itemRippleColor 0x0
+int attr itemShapeAppearance 0x0
+int attr itemShapeAppearanceOverlay 0x0
+int attr itemShapeFillColor 0x0
+int attr itemShapeInsetBottom 0x0
+int attr itemShapeInsetEnd 0x0
+int attr itemShapeInsetStart 0x0
+int attr itemShapeInsetTop 0x0
+int attr itemSpacing 0x0
+int attr itemStrokeColor 0x0
+int attr itemStrokeWidth 0x0
+int attr itemTextAppearance 0x0
+int attr itemTextAppearanceActive 0x0
+int attr itemTextAppearanceInactive 0x0
+int attr itemTextColor 0x0
+int attr keylines 0x0
+int attr lStar 0x0
+int attr labelVisibilityMode 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr layout 0x0
+int attr layoutManager 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_collapseMode 0x0
+int attr layout_collapseParallaxMultiplier 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr layout_scrollFlags 0x0
+int attr layout_scrollInterpolator 0x0
+int attr liftOnScroll 0x0
+int attr liftOnScrollTargetViewId 0x0
+int attr lineHeight 0x0
+int attr lineSpacing 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listChoiceIndicatorMultipleAnimated 0x0
+int attr listChoiceIndicatorSingleAnimated 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingEnd 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr listPreferredItemPaddingStart 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr materialAlertDialogBodyTextStyle 0x0
+int attr materialAlertDialogTheme 0x0
+int attr materialAlertDialogTitleIconStyle 0x0
+int attr materialAlertDialogTitlePanelStyle 0x0
+int attr materialAlertDialogTitleTextStyle 0x0
+int attr materialButtonOutlinedStyle 0x0
+int attr materialButtonStyle 0x0
+int attr materialButtonToggleGroupStyle 0x0
+int attr materialCalendarDay 0x0
+int attr materialCalendarFullscreenTheme 0x0
+int attr materialCalendarHeaderConfirmButton 0x0
+int attr materialCalendarHeaderDivider 0x0
+int attr materialCalendarHeaderLayout 0x0
+int attr materialCalendarHeaderSelection 0x0
+int attr materialCalendarHeaderTitle 0x0
+int attr materialCalendarHeaderToggleButton 0x0
+int attr materialCalendarStyle 0x0
+int attr materialCalendarTheme 0x0
+int attr materialCardViewStyle 0x0
+int attr materialThemeOverlay 0x0
+int attr maxActionInlineWidth 0x0
+int attr maxButtonHeight 0x0
+int attr maxCharacterCount 0x0
+int attr maxImageSize 0x0
+int attr measureWithLargestChild 0x0
+int attr menu 0x0
+int attr minTouchTargetSize 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr navigationViewStyle 0x0
+int attr nestedScrollViewStyle 0x0
+int attr number 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr passwordToggleContentDescription 0x0
+int attr passwordToggleDrawable 0x0
+int attr passwordToggleEnabled 0x0
+int attr passwordToggleTint 0x0
+int attr passwordToggleTintMode 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuBackground 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr pressedTranslationZ 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr queryPatterns 0x0
+int attr radioButtonStyle 0x0
+int attr rangeFillColor 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr recyclerViewStyle 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr reverseLayout 0x0
+int attr rippleColor 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr scrimAnimationDuration 0x0
+int attr scrimBackground 0x0
+int attr scrimVisibleHeightTrigger 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr shapeAppearance 0x0
+int attr shapeAppearanceLargeComponent 0x0
+int attr shapeAppearanceMediumComponent 0x0
+int attr shapeAppearanceOverlay 0x0
+int attr shapeAppearanceSmallComponent 0x0
+int attr shortcutMatchRequired 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showMotionSpec 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr shrinkMotionSpec 0x0
+int attr singleChoiceItemLayout 0x0
+int attr singleLine 0x0
+int attr singleSelection 0x0
+int attr snackbarButtonStyle 0x0
+int attr snackbarStyle 0x0
+int attr spanCount 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr stackFromEnd 0x0
+int attr startIconCheckable 0x0
+int attr startIconContentDescription 0x0
+int attr startIconDrawable 0x0
+int attr startIconTint 0x0
+int attr startIconTintMode 0x0
+int attr state_above_anchor 0x0
+int attr state_collapsed 0x0
+int attr state_collapsible 0x0
+int attr state_dragged 0x0
+int attr state_liftable 0x0
+int attr state_lifted 0x0
+int attr statusBarBackground 0x0
+int attr statusBarForeground 0x0
+int attr statusBarScrim 0x0
+int attr strokeColor 0x0
+int attr strokeWidth 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr tabBackground 0x0
+int attr tabContentStart 0x0
+int attr tabGravity 0x0
+int attr tabIconTint 0x0
+int attr tabIconTintMode 0x0
+int attr tabIndicator 0x0
+int attr tabIndicatorAnimationDuration 0x0
+int attr tabIndicatorColor 0x0
+int attr tabIndicatorFullWidth 0x0
+int attr tabIndicatorGravity 0x0
+int attr tabIndicatorHeight 0x0
+int attr tabInlineLabel 0x0
+int attr tabMaxWidth 0x0
+int attr tabMinWidth 0x0
+int attr tabMode 0x0
+int attr tabPadding 0x0
+int attr tabPaddingBottom 0x0
+int attr tabPaddingEnd 0x0
+int attr tabPaddingStart 0x0
+int attr tabPaddingTop 0x0
+int attr tabRippleColor 0x0
+int attr tabSelectedTextColor 0x0
+int attr tabStyle 0x0
+int attr tabTextAppearance 0x0
+int attr tabTextColor 0x0
+int attr tabUnboundedRipple 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceBody1 0x0
+int attr textAppearanceBody2 0x0
+int attr textAppearanceButton 0x0
+int attr textAppearanceCaption 0x0
+int attr textAppearanceHeadline1 0x0
+int attr textAppearanceHeadline2 0x0
+int attr textAppearanceHeadline3 0x0
+int attr textAppearanceHeadline4 0x0
+int attr textAppearanceHeadline5 0x0
+int attr textAppearanceHeadline6 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceLineHeightEnabled 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearanceOverline 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textAppearanceSubtitle1 0x0
+int attr textAppearanceSubtitle2 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr textEndPadding 0x0
+int attr textInputStyle 0x0
+int attr textLocale 0x0
+int attr textStartPadding 0x0
+int attr theme 0x0
+int attr themeLineHeight 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleEnabled 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarId 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr useCompatPadding 0x0
+int attr useMaterialThemeColors 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int attr yearSelectedStyle 0x0
+int attr yearStyle 0x0
+int attr yearTodayStyle 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_allow_stacked_button_bar 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int bool mtrl_btn_textappearance_all_caps 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_decor_view_status_guard 0x0
+int color abc_decor_view_status_guard_light 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_input_method_navigation_guard 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color androidx_core_ripple_material_light 0x0
+int color androidx_core_secondary_text_default_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color cardview_dark_background 0x0
+int color cardview_light_background 0x0
+int color cardview_shadow_end_color 0x0
+int color cardview_shadow_start_color 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color checkbox_themeable_attribute_color 0x0
+int color design_bottom_navigation_shadow_color 0x0
+int color design_box_stroke_color 0x0
+int color design_dark_default_color_background 0x0
+int color design_dark_default_color_error 0x0
+int color design_dark_default_color_on_background 0x0
+int color design_dark_default_color_on_error 0x0
+int color design_dark_default_color_on_primary 0x0
+int color design_dark_default_color_on_secondary 0x0
+int color design_dark_default_color_on_surface 0x0
+int color design_dark_default_color_primary 0x0
+int color design_dark_default_color_primary_dark 0x0
+int color design_dark_default_color_primary_variant 0x0
+int color design_dark_default_color_secondary 0x0
+int color design_dark_default_color_secondary_variant 0x0
+int color design_dark_default_color_surface 0x0
+int color design_default_color_background 0x0
+int color design_default_color_error 0x0
+int color design_default_color_on_background 0x0
+int color design_default_color_on_error 0x0
+int color design_default_color_on_primary 0x0
+int color design_default_color_on_secondary 0x0
+int color design_default_color_on_surface 0x0
+int color design_default_color_primary 0x0
+int color design_default_color_primary_dark 0x0
+int color design_default_color_primary_variant 0x0
+int color design_default_color_secondary 0x0
+int color design_default_color_secondary_variant 0x0
+int color design_default_color_surface 0x0
+int color design_error 0x0
+int color design_fab_shadow_end_color 0x0
+int color design_fab_shadow_mid_color 0x0
+int color design_fab_shadow_start_color 0x0
+int color design_fab_stroke_end_inner_color 0x0
+int color design_fab_stroke_end_outer_color 0x0
+int color design_fab_stroke_top_inner_color 0x0
+int color design_fab_stroke_top_outer_color 0x0
+int color design_icon_tint 0x0
+int color design_snackbar_background_color 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color material_on_background_disabled 0x0
+int color material_on_background_emphasis_high_type 0x0
+int color material_on_background_emphasis_medium 0x0
+int color material_on_primary_disabled 0x0
+int color material_on_primary_emphasis_high_type 0x0
+int color material_on_primary_emphasis_medium 0x0
+int color material_on_surface_disabled 0x0
+int color material_on_surface_emphasis_high_type 0x0
+int color material_on_surface_emphasis_medium 0x0
+int color mtrl_bottom_nav_colored_item_tint 0x0
+int color mtrl_bottom_nav_colored_ripple_color 0x0
+int color mtrl_bottom_nav_item_tint 0x0
+int color mtrl_bottom_nav_ripple_color 0x0
+int color mtrl_btn_bg_color_selector 0x0
+int color mtrl_btn_ripple_color 0x0
+int color mtrl_btn_stroke_color_selector 0x0
+int color mtrl_btn_text_btn_bg_color_selector 0x0
+int color mtrl_btn_text_btn_ripple_color 0x0
+int color mtrl_btn_text_color_disabled 0x0
+int color mtrl_btn_text_color_selector 0x0
+int color mtrl_btn_transparent_bg_color 0x0
+int color mtrl_calendar_item_stroke_color 0x0
+int color mtrl_calendar_selected_range 0x0
+int color mtrl_card_view_foreground 0x0
+int color mtrl_card_view_ripple 0x0
+int color mtrl_chip_background_color 0x0
+int color mtrl_chip_close_icon_tint 0x0
+int color mtrl_chip_ripple_color 0x0
+int color mtrl_chip_surface_color 0x0
+int color mtrl_chip_text_color 0x0
+int color mtrl_choice_chip_background_color 0x0
+int color mtrl_choice_chip_ripple_color 0x0
+int color mtrl_choice_chip_text_color 0x0
+int color mtrl_error 0x0
+int color mtrl_extended_fab_bg_color_selector 0x0
+int color mtrl_extended_fab_ripple_color 0x0
+int color mtrl_extended_fab_text_color_selector 0x0
+int color mtrl_fab_ripple_color 0x0
+int color mtrl_filled_background_color 0x0
+int color mtrl_filled_icon_tint 0x0
+int color mtrl_filled_stroke_color 0x0
+int color mtrl_indicator_text_color 0x0
+int color mtrl_navigation_item_background_color 0x0
+int color mtrl_navigation_item_icon_tint 0x0
+int color mtrl_navigation_item_text_color 0x0
+int color mtrl_on_primary_text_btn_text_color_selector 0x0
+int color mtrl_outlined_icon_tint 0x0
+int color mtrl_outlined_stroke_color 0x0
+int color mtrl_popupmenu_overlay_color 0x0
+int color mtrl_scrim_color 0x0
+int color mtrl_tabs_colored_ripple_color 0x0
+int color mtrl_tabs_icon_color_selector 0x0
+int color mtrl_tabs_icon_color_selector_colored 0x0
+int color mtrl_tabs_legacy_text_color_selector 0x0
+int color mtrl_tabs_ripple_color 0x0
+int color mtrl_text_btn_text_color_selector 0x0
+int color mtrl_textinput_default_box_stroke_color 0x0
+int color mtrl_textinput_disabled_color 0x0
+int color mtrl_textinput_filled_box_default_background_color 0x0
+int color mtrl_textinput_focused_box_stroke_color 0x0
+int color mtrl_textinput_hovered_box_stroke_color 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color test_mtrl_calendar_day 0x0
+int color test_mtrl_calendar_day_selected 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_height_large_material 0x0
+int dimen abc_list_item_height_material 0x0
+int dimen abc_list_item_height_small_material 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_star_big 0x0
+int dimen abc_star_medium 0x0
+int dimen abc_star_small 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen action_bar_size 0x0
+int dimen appcompat_dialog_background_inset 0x0
+int dimen autofill_inline_suggestion_icon_size 0x0
+int dimen cardview_compat_inset_shadow 0x0
+int dimen cardview_default_elevation 0x0
+int dimen cardview_default_radius 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen default_dimension 0x0
+int dimen design_appbar_elevation 0x0
+int dimen design_bottom_navigation_active_item_max_width 0x0
+int dimen design_bottom_navigation_active_item_min_width 0x0
+int dimen design_bottom_navigation_active_text_size 0x0
+int dimen design_bottom_navigation_elevation 0x0
+int dimen design_bottom_navigation_height 0x0
+int dimen design_bottom_navigation_icon_size 0x0
+int dimen design_bottom_navigation_item_max_width 0x0
+int dimen design_bottom_navigation_item_min_width 0x0
+int dimen design_bottom_navigation_margin 0x0
+int dimen design_bottom_navigation_shadow_height 0x0
+int dimen design_bottom_navigation_text_size 0x0
+int dimen design_bottom_sheet_elevation 0x0
+int dimen design_bottom_sheet_modal_elevation 0x0
+int dimen design_bottom_sheet_peek_height_min 0x0
+int dimen design_fab_border_width 0x0
+int dimen design_fab_elevation 0x0
+int dimen design_fab_image_size 0x0
+int dimen design_fab_size_mini 0x0
+int dimen design_fab_size_normal 0x0
+int dimen design_fab_translation_z_hovered_focused 0x0
+int dimen design_fab_translation_z_pressed 0x0
+int dimen design_navigation_elevation 0x0
+int dimen design_navigation_icon_padding 0x0
+int dimen design_navigation_icon_size 0x0
+int dimen design_navigation_item_horizontal_padding 0x0
+int dimen design_navigation_item_icon_padding 0x0
+int dimen design_navigation_max_width 0x0
+int dimen design_navigation_padding_bottom 0x0
+int dimen design_navigation_separator_vertical_padding 0x0
+int dimen design_snackbar_action_inline_max_width 0x0
+int dimen design_snackbar_action_text_color_alpha 0x0
+int dimen design_snackbar_background_corner_radius 0x0
+int dimen design_snackbar_elevation 0x0
+int dimen design_snackbar_extra_spacing_horizontal 0x0
+int dimen design_snackbar_max_width 0x0
+int dimen design_snackbar_min_width 0x0
+int dimen design_snackbar_padding_horizontal 0x0
+int dimen design_snackbar_padding_vertical 0x0
+int dimen design_snackbar_padding_vertical_2lines 0x0
+int dimen design_snackbar_text_size 0x0
+int dimen design_tab_max_width 0x0
+int dimen design_tab_scrollable_min_width 0x0
+int dimen design_tab_text_size 0x0
+int dimen design_tab_text_size_2line 0x0
+int dimen design_textinput_caption_translate_y 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen fastscroll_default_thickness 0x0
+int dimen fastscroll_margin 0x0
+int dimen fastscroll_minimum_range 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen item_touch_helper_max_drag_scroll_per_frame 0x0
+int dimen item_touch_helper_swipe_escape_max_velocity 0x0
+int dimen item_touch_helper_swipe_escape_velocity 0x0
+int dimen material_emphasis_disabled 0x0
+int dimen material_emphasis_high_type 0x0
+int dimen material_emphasis_medium 0x0
+int dimen material_text_view_test_line_height 0x0
+int dimen material_text_view_test_line_height_override 0x0
+int dimen mtrl_alert_dialog_background_inset_bottom 0x0
+int dimen mtrl_alert_dialog_background_inset_end 0x0
+int dimen mtrl_alert_dialog_background_inset_start 0x0
+int dimen mtrl_alert_dialog_background_inset_top 0x0
+int dimen mtrl_alert_dialog_picker_background_inset 0x0
+int dimen mtrl_badge_horizontal_edge_offset 0x0
+int dimen mtrl_badge_long_text_horizontal_padding 0x0
+int dimen mtrl_badge_radius 0x0
+int dimen mtrl_badge_text_horizontal_edge_offset 0x0
+int dimen mtrl_badge_text_size 0x0
+int dimen mtrl_badge_with_text_radius 0x0
+int dimen mtrl_bottomappbar_fabOffsetEndMode 0x0
+int dimen mtrl_bottomappbar_fab_bottom_margin 0x0
+int dimen mtrl_bottomappbar_fab_cradle_margin 0x0
+int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x0
+int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x0
+int dimen mtrl_bottomappbar_height 0x0
+int dimen mtrl_btn_corner_radius 0x0
+int dimen mtrl_btn_dialog_btn_min_width 0x0
+int dimen mtrl_btn_disabled_elevation 0x0
+int dimen mtrl_btn_disabled_z 0x0
+int dimen mtrl_btn_elevation 0x0
+int dimen mtrl_btn_focused_z 0x0
+int dimen mtrl_btn_hovered_z 0x0
+int dimen mtrl_btn_icon_btn_padding_left 0x0
+int dimen mtrl_btn_icon_padding 0x0
+int dimen mtrl_btn_inset 0x0
+int dimen mtrl_btn_letter_spacing 0x0
+int dimen mtrl_btn_padding_bottom 0x0
+int dimen mtrl_btn_padding_left 0x0
+int dimen mtrl_btn_padding_right 0x0
+int dimen mtrl_btn_padding_top 0x0
+int dimen mtrl_btn_pressed_z 0x0
+int dimen mtrl_btn_stroke_size 0x0
+int dimen mtrl_btn_text_btn_icon_padding 0x0
+int dimen mtrl_btn_text_btn_padding_left 0x0
+int dimen mtrl_btn_text_btn_padding_right 0x0
+int dimen mtrl_btn_text_size 0x0
+int dimen mtrl_btn_z 0x0
+int dimen mtrl_calendar_action_height 0x0
+int dimen mtrl_calendar_action_padding 0x0
+int dimen mtrl_calendar_bottom_padding 0x0
+int dimen mtrl_calendar_content_padding 0x0
+int dimen mtrl_calendar_day_corner 0x0
+int dimen mtrl_calendar_day_height 0x0
+int dimen mtrl_calendar_day_horizontal_padding 0x0
+int dimen mtrl_calendar_day_today_stroke 0x0
+int dimen mtrl_calendar_day_vertical_padding 0x0
+int dimen mtrl_calendar_day_width 0x0
+int dimen mtrl_calendar_days_of_week_height 0x0
+int dimen mtrl_calendar_dialog_background_inset 0x0
+int dimen mtrl_calendar_header_content_padding 0x0
+int dimen mtrl_calendar_header_content_padding_fullscreen 0x0
+int dimen mtrl_calendar_header_divider_thickness 0x0
+int dimen mtrl_calendar_header_height 0x0
+int dimen mtrl_calendar_header_height_fullscreen 0x0
+int dimen mtrl_calendar_header_selection_line_height 0x0
+int dimen mtrl_calendar_header_text_padding 0x0
+int dimen mtrl_calendar_header_toggle_margin_bottom 0x0
+int dimen mtrl_calendar_header_toggle_margin_top 0x0
+int dimen mtrl_calendar_landscape_header_width 0x0
+int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x0
+int dimen mtrl_calendar_month_horizontal_padding 0x0
+int dimen mtrl_calendar_month_vertical_padding 0x0
+int dimen mtrl_calendar_navigation_bottom_padding 0x0
+int dimen mtrl_calendar_navigation_height 0x0
+int dimen mtrl_calendar_navigation_top_padding 0x0
+int dimen mtrl_calendar_pre_l_text_clip_padding 0x0
+int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x0
+int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x0
+int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x0
+int dimen mtrl_calendar_selection_text_baseline_to_top 0x0
+int dimen mtrl_calendar_text_input_padding_top 0x0
+int dimen mtrl_calendar_title_baseline_to_top 0x0
+int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x0
+int dimen mtrl_calendar_year_corner 0x0
+int dimen mtrl_calendar_year_height 0x0
+int dimen mtrl_calendar_year_horizontal_padding 0x0
+int dimen mtrl_calendar_year_vertical_padding 0x0
+int dimen mtrl_calendar_year_width 0x0
+int dimen mtrl_card_checked_icon_margin 0x0
+int dimen mtrl_card_checked_icon_size 0x0
+int dimen mtrl_card_corner_radius 0x0
+int dimen mtrl_card_dragged_z 0x0
+int dimen mtrl_card_elevation 0x0
+int dimen mtrl_card_spacing 0x0
+int dimen mtrl_chip_pressed_translation_z 0x0
+int dimen mtrl_chip_text_size 0x0
+int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x0
+int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x0
+int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x0
+int dimen mtrl_extended_fab_bottom_padding 0x0
+int dimen mtrl_extended_fab_corner_radius 0x0
+int dimen mtrl_extended_fab_disabled_elevation 0x0
+int dimen mtrl_extended_fab_disabled_translation_z 0x0
+int dimen mtrl_extended_fab_elevation 0x0
+int dimen mtrl_extended_fab_end_padding 0x0
+int dimen mtrl_extended_fab_end_padding_icon 0x0
+int dimen mtrl_extended_fab_icon_size 0x0
+int dimen mtrl_extended_fab_icon_text_spacing 0x0
+int dimen mtrl_extended_fab_min_height 0x0
+int dimen mtrl_extended_fab_min_width 0x0
+int dimen mtrl_extended_fab_start_padding 0x0
+int dimen mtrl_extended_fab_start_padding_icon 0x0
+int dimen mtrl_extended_fab_top_padding 0x0
+int dimen mtrl_extended_fab_translation_z_base 0x0
+int dimen mtrl_extended_fab_translation_z_hovered_focused 0x0
+int dimen mtrl_extended_fab_translation_z_pressed 0x0
+int dimen mtrl_fab_elevation 0x0
+int dimen mtrl_fab_min_touch_target 0x0
+int dimen mtrl_fab_translation_z_hovered_focused 0x0
+int dimen mtrl_fab_translation_z_pressed 0x0
+int dimen mtrl_high_ripple_default_alpha 0x0
+int dimen mtrl_high_ripple_focused_alpha 0x0
+int dimen mtrl_high_ripple_hovered_alpha 0x0
+int dimen mtrl_high_ripple_pressed_alpha 0x0
+int dimen mtrl_large_touch_target 0x0
+int dimen mtrl_low_ripple_default_alpha 0x0
+int dimen mtrl_low_ripple_focused_alpha 0x0
+int dimen mtrl_low_ripple_hovered_alpha 0x0
+int dimen mtrl_low_ripple_pressed_alpha 0x0
+int dimen mtrl_min_touch_target_size 0x0
+int dimen mtrl_navigation_elevation 0x0
+int dimen mtrl_navigation_item_horizontal_padding 0x0
+int dimen mtrl_navigation_item_icon_padding 0x0
+int dimen mtrl_navigation_item_icon_size 0x0
+int dimen mtrl_navigation_item_shape_horizontal_margin 0x0
+int dimen mtrl_navigation_item_shape_vertical_margin 0x0
+int dimen mtrl_shape_corner_size_large_component 0x0
+int dimen mtrl_shape_corner_size_medium_component 0x0
+int dimen mtrl_shape_corner_size_small_component 0x0
+int dimen mtrl_snackbar_action_text_color_alpha 0x0
+int dimen mtrl_snackbar_background_corner_radius 0x0
+int dimen mtrl_snackbar_background_overlay_color_alpha 0x0
+int dimen mtrl_snackbar_margin 0x0
+int dimen mtrl_switch_thumb_elevation 0x0
+int dimen mtrl_textinput_box_corner_radius_medium 0x0
+int dimen mtrl_textinput_box_corner_radius_small 0x0
+int dimen mtrl_textinput_box_label_cutout_padding 0x0
+int dimen mtrl_textinput_box_stroke_width_default 0x0
+int dimen mtrl_textinput_box_stroke_width_focused 0x0
+int dimen mtrl_textinput_end_icon_margin_start 0x0
+int dimen mtrl_textinput_outline_box_expanded_padding 0x0
+int dimen mtrl_textinput_start_icon_margin_end 0x0
+int dimen mtrl_toolbar_default_height 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen test_mtrl_calendar_day_cornerSize 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_material_anim 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_material_anim 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_star_black_16dp 0x0
+int drawable abc_ic_star_black_36dp 0x0
+int drawable abc_ic_star_black_48dp 0x0
+int drawable abc_ic_star_half_black_16dp 0x0
+int drawable abc_ic_star_half_black_36dp 0x0
+int drawable abc_ic_star_half_black_48dp 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_star_black_48dp 0x0
+int drawable abc_star_half_black_48dp 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl 0x0
+int drawable abc_text_select_handle_left_mtrl_dark 0x0
+int drawable abc_text_select_handle_left_mtrl_light 0x0
+int drawable abc_text_select_handle_middle_mtrl 0x0
+int drawable abc_text_select_handle_middle_mtrl_dark 0x0
+int drawable abc_text_select_handle_middle_mtrl_light 0x0
+int drawable abc_text_select_handle_right_mtrl 0x0
+int drawable abc_text_select_handle_right_mtrl_dark 0x0
+int drawable abc_text_select_handle_right_mtrl_light 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable autofill_inline_suggestion_chip_background 0x0
+int drawable avd_hide_password 0x0
+int drawable avd_show_password 0x0
+int drawable btn_checkbox_checked_mtrl 0x0
+int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x0
+int drawable btn_checkbox_unchecked_mtrl 0x0
+int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x0
+int drawable btn_radio_off_mtrl 0x0
+int drawable btn_radio_off_to_on_mtrl_animation 0x0
+int drawable btn_radio_on_mtrl 0x0
+int drawable btn_radio_on_to_off_mtrl_animation 0x0
+int drawable design_bottom_navigation_item_background 0x0
+int drawable design_fab_background 0x0
+int drawable design_ic_visibility 0x0
+int drawable design_ic_visibility_off 0x0
+int drawable design_password_eye 0x0
+int drawable design_snackbar_background 0x0
+int drawable ic_calendar_black_24dp 0x0
+int drawable ic_clear_black_24dp 0x0
+int drawable ic_edit_black_24dp 0x0
+int drawable ic_keyboard_arrow_left_black_24dp 0x0
+int drawable ic_keyboard_arrow_right_black_24dp 0x0
+int drawable ic_menu_arrow_down_black_24dp 0x0
+int drawable ic_menu_arrow_up_black_24dp 0x0
+int drawable ic_mtrl_checked_circle 0x0
+int drawable ic_mtrl_chip_checked_black 0x0
+int drawable ic_mtrl_chip_checked_circle 0x0
+int drawable ic_mtrl_chip_close_circle 0x0
+int drawable mtrl_dialog_background 0x0
+int drawable mtrl_dropdown_arrow 0x0
+int drawable mtrl_ic_arrow_drop_down 0x0
+int drawable mtrl_ic_arrow_drop_up 0x0
+int drawable mtrl_ic_cancel 0x0
+int drawable mtrl_ic_error 0x0
+int drawable mtrl_popupmenu_background 0x0
+int drawable mtrl_popupmenu_background_dark 0x0
+int drawable mtrl_tabs_default_indicator 0x0
+int drawable navigation_empty_icon 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable test_custom_background 0x0
+int drawable test_level_drawable 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id BOTTOM_END 0x0
+int id BOTTOM_START 0x0
+int id TOP_END 0x0
+int id TOP_START 0x0
+int id accessibility_action_clickable_span 0x0
+int id accessibility_actions 0x0
+int id accessibility_collection 0x0
+int id accessibility_collection_item 0x0
+int id accessibility_custom_action_0 0x0
+int id accessibility_custom_action_1 0x0
+int id accessibility_custom_action_10 0x0
+int id accessibility_custom_action_11 0x0
+int id accessibility_custom_action_12 0x0
+int id accessibility_custom_action_13 0x0
+int id accessibility_custom_action_14 0x0
+int id accessibility_custom_action_15 0x0
+int id accessibility_custom_action_16 0x0
+int id accessibility_custom_action_17 0x0
+int id accessibility_custom_action_18 0x0
+int id accessibility_custom_action_19 0x0
+int id accessibility_custom_action_2 0x0
+int id accessibility_custom_action_20 0x0
+int id accessibility_custom_action_21 0x0
+int id accessibility_custom_action_22 0x0
+int id accessibility_custom_action_23 0x0
+int id accessibility_custom_action_24 0x0
+int id accessibility_custom_action_25 0x0
+int id accessibility_custom_action_26 0x0
+int id accessibility_custom_action_27 0x0
+int id accessibility_custom_action_28 0x0
+int id accessibility_custom_action_29 0x0
+int id accessibility_custom_action_3 0x0
+int id accessibility_custom_action_30 0x0
+int id accessibility_custom_action_31 0x0
+int id accessibility_custom_action_4 0x0
+int id accessibility_custom_action_5 0x0
+int id accessibility_custom_action_6 0x0
+int id accessibility_custom_action_7 0x0
+int id accessibility_custom_action_8 0x0
+int id accessibility_custom_action_9 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_links 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_state_expanded 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id alertTitle 0x0
+int id async 0x0
+int id auto 0x0
+int id autofill_inline_suggestion_end_icon 0x0
+int id autofill_inline_suggestion_start_icon 0x0
+int id autofill_inline_suggestion_subtitle 0x0
+int id autofill_inline_suggestion_title 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id buttonPanel 0x0
+int id cancel_button 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id checked 0x0
+int id chip 0x0
+int id chip_group 0x0
+int id chronometer 0x0
+int id clear_text 0x0
+int id confirm_button 0x0
+int id container 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id coordinator 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id cut 0x0
+int id date_picker_actions 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id design_bottom_sheet 0x0
+int id design_menu_item_action_area 0x0
+int id design_menu_item_action_area_stub 0x0
+int id design_menu_item_text 0x0
+int id design_navigation_view 0x0
+int id dialog_button 0x0
+int id dropdown_menu 0x0
+int id edit_query 0x0
+int id end 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fade 0x0
+int id fill 0x0
+int id filled 0x0
+int id filter_chip 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id fixed 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id fragment_container_view_tag 0x0
+int id ghost_view 0x0
+int id ghost_view_holder 0x0
+int id group_divider 0x0
+int id home 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id item1 0x0
+int id item2 0x0
+int id item3 0x0
+int id item4 0x0
+int id item_touch_helper_previous_elevation 0x0
+int id labeled 0x0
+int id labelled_by 0x0
+int id largeLabel 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id masked 0x0
+int id message 0x0
+int id mini 0x0
+int id month_grid 0x0
+int id month_navigation_bar 0x0
+int id month_navigation_fragment_toggle 0x0
+int id month_navigation_next 0x0
+int id month_navigation_previous 0x0
+int id month_title 0x0
+int id mtrl_calendar_day_selector_frame 0x0
+int id mtrl_calendar_days_of_week 0x0
+int id mtrl_calendar_frame 0x0
+int id mtrl_calendar_main_pane 0x0
+int id mtrl_calendar_months 0x0
+int id mtrl_calendar_selection_frame 0x0
+int id mtrl_calendar_text_input_frame 0x0
+int id mtrl_calendar_year_selector_frame 0x0
+int id mtrl_card_checked_layer_id 0x0
+int id mtrl_child_content_container 0x0
+int id mtrl_internal_children_alpha_tag 0x0
+int id mtrl_picker_fullscreen 0x0
+int id mtrl_picker_header 0x0
+int id mtrl_picker_header_selection_text 0x0
+int id mtrl_picker_header_title_and_selection 0x0
+int id mtrl_picker_header_toggle 0x0
+int id mtrl_picker_text_input_date 0x0
+int id mtrl_picker_text_input_range_end 0x0
+int id mtrl_picker_text_input_range_start 0x0
+int id mtrl_picker_title_text 0x0
+int id multiply 0x0
+int id navigation_header_container 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id off 0x0
+int id on 0x0
+int id outline 0x0
+int id parallax 0x0
+int id parentPanel 0x0
+int id parent_matrix 0x0
+int id password_toggle 0x0
+int id pin 0x0
+int id pointer_events 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id rounded 0x0
+int id save_non_transition_alpha 0x0
+int id save_overlay_view 0x0
+int id scale 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id scrollable 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id selected 0x0
+int id shortcut 0x0
+int id slide 0x0
+int id smallLabel 0x0
+int id snackbar_action 0x0
+int id snackbar_text 0x0
+int id spacer 0x0
+int id special_effects_controller_view_tag 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id start 0x0
+int id stretch 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_accessibility_actions 0x0
+int id tag_accessibility_clickable_spans 0x0
+int id tag_accessibility_heading 0x0
+int id tag_accessibility_pane_title 0x0
+int id tag_on_apply_window_listener 0x0
+int id tag_on_receive_content_listener 0x0
+int id tag_on_receive_content_mime_types 0x0
+int id tag_screen_reader_focusable 0x0
+int id tag_state_description 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id tag_window_insets_animation_callback 0x0
+int id test_checkbox_android_button_tint 0x0
+int id test_checkbox_app_button_tint 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id text_input_end_icon 0x0
+int id text_input_start_icon 0x0
+int id textinput_counter 0x0
+int id textinput_error 0x0
+int id textinput_helper_text 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id top 0x0
+int id topPanel 0x0
+int id touch_outside 0x0
+int id transition_current_scene 0x0
+int id transition_layout_save 0x0
+int id transition_position 0x0
+int id transition_scene_layoutid_cache 0x0
+int id transition_transform 0x0
+int id unchecked 0x0
+int id uniform 0x0
+int id unlabeled 0x0
+int id up 0x0
+int id view_offset_helper 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id view_tree_lifecycle_owner 0x0
+int id view_tree_saved_state_registry_owner 0x0
+int id view_tree_view_model_store_owner 0x0
+int id visible 0x0
+int id visible_removing_fragment_view_tag 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer app_bar_elevation_anim_duration 0x0
+int integer bottom_sheet_slide_duration 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer design_snackbar_text_max_lines 0x0
+int integer design_tab_indicator_anim_duration_ms 0x0
+int integer hide_password_duration 0x0
+int integer mtrl_badge_max_character_count 0x0
+int integer mtrl_btn_anim_delay_ms 0x0
+int integer mtrl_btn_anim_duration_ms 0x0
+int integer mtrl_calendar_header_orientation 0x0
+int integer mtrl_calendar_selection_text_lines 0x0
+int integer mtrl_calendar_year_selector_span 0x0
+int integer mtrl_card_anim_delay_ms 0x0
+int integer mtrl_card_anim_duration_ms 0x0
+int integer mtrl_chip_anim_duration 0x0
+int integer mtrl_tab_indicator_anim_duration_ms 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer show_password_duration 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x0
+int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x0
+int interpolator fast_out_slow_in 0x0
+int interpolator mtrl_fast_out_linear_in 0x0
+int interpolator mtrl_fast_out_slow_in 0x0
+int interpolator mtrl_linear 0x0
+int interpolator mtrl_linear_out_slow_in 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout autofill_inline_suggestion 0x0
+int layout custom_dialog 0x0
+int layout design_bottom_navigation_item 0x0
+int layout design_bottom_sheet_dialog 0x0
+int layout design_layout_snackbar 0x0
+int layout design_layout_snackbar_include 0x0
+int layout design_layout_tab_icon 0x0
+int layout design_layout_tab_text 0x0
+int layout design_menu_item_action_area 0x0
+int layout design_navigation_item 0x0
+int layout design_navigation_item_header 0x0
+int layout design_navigation_item_separator 0x0
+int layout design_navigation_item_subheader 0x0
+int layout design_navigation_menu 0x0
+int layout design_navigation_menu_item 0x0
+int layout design_text_input_end_icon 0x0
+int layout design_text_input_start_icon 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout mtrl_alert_dialog 0x0
+int layout mtrl_alert_dialog_actions 0x0
+int layout mtrl_alert_dialog_title 0x0
+int layout mtrl_alert_select_dialog_item 0x0
+int layout mtrl_alert_select_dialog_multichoice 0x0
+int layout mtrl_alert_select_dialog_singlechoice 0x0
+int layout mtrl_calendar_day 0x0
+int layout mtrl_calendar_day_of_week 0x0
+int layout mtrl_calendar_days_of_week 0x0
+int layout mtrl_calendar_horizontal 0x0
+int layout mtrl_calendar_month 0x0
+int layout mtrl_calendar_month_labeled 0x0
+int layout mtrl_calendar_month_navigation 0x0
+int layout mtrl_calendar_months 0x0
+int layout mtrl_calendar_vertical 0x0
+int layout mtrl_calendar_year 0x0
+int layout mtrl_layout_snackbar 0x0
+int layout mtrl_layout_snackbar_include 0x0
+int layout mtrl_picker_actions 0x0
+int layout mtrl_picker_dialog 0x0
+int layout mtrl_picker_fullscreen 0x0
+int layout mtrl_picker_header_dialog 0x0
+int layout mtrl_picker_header_fullscreen 0x0
+int layout mtrl_picker_header_selection_text 0x0
+int layout mtrl_picker_header_title_text 0x0
+int layout mtrl_picker_header_toggle 0x0
+int layout mtrl_picker_text_input_date 0x0
+int layout mtrl_picker_text_input_date_range 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int layout test_action_chip 0x0
+int layout test_design_checkbox 0x0
+int layout test_reflow_chipgroup 0x0
+int layout test_toolbar 0x0
+int layout test_toolbar_custom_background 0x0
+int layout test_toolbar_elevation 0x0
+int layout test_toolbar_surface 0x0
+int layout text_view_with_line_height_from_appearance 0x0
+int layout text_view_with_line_height_from_layout 0x0
+int layout text_view_with_line_height_from_style 0x0
+int layout text_view_with_theme_line_height 0x0
+int layout text_view_without_line_height 0x0
+int menu example_menu 0x0
+int menu example_menu2 0x0
+int plurals mtrl_badge_content_description 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string appbar_scrolling_view_behavior 0x0
+int string bottom_sheet_behavior 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_inspector_stop 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string character_counter_content_description 0x0
+int string character_counter_overflowed_content_description 0x0
+int string character_counter_pattern 0x0
+int string chip_text 0x0
+int string clear_text_end_icon_content_description 0x0
+int string combobox_description 0x0
+int string error_icon_content_description 0x0
+int string exposed_dropdown_menu_content_description 0x0
+int string fab_transformation_scrim_behavior 0x0
+int string fab_transformation_sheet_behavior 0x0
+int string header_description 0x0
+int string hide_bottom_view_on_scroll_behavior 0x0
+int string icon_content_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string mtrl_badge_numberless_content_description 0x0
+int string mtrl_chip_close_icon_content_description 0x0
+int string mtrl_exceed_max_badge_number_suffix 0x0
+int string mtrl_picker_a11y_next_month 0x0
+int string mtrl_picker_a11y_prev_month 0x0
+int string mtrl_picker_announce_current_selection 0x0
+int string mtrl_picker_cancel 0x0
+int string mtrl_picker_confirm 0x0
+int string mtrl_picker_date_header_selected 0x0
+int string mtrl_picker_date_header_title 0x0
+int string mtrl_picker_date_header_unselected 0x0
+int string mtrl_picker_day_of_week_column_header 0x0
+int string mtrl_picker_invalid_format 0x0
+int string mtrl_picker_invalid_format_example 0x0
+int string mtrl_picker_invalid_format_use 0x0
+int string mtrl_picker_invalid_range 0x0
+int string mtrl_picker_navigate_to_year_description 0x0
+int string mtrl_picker_out_of_range 0x0
+int string mtrl_picker_range_header_only_end_selected 0x0
+int string mtrl_picker_range_header_only_start_selected 0x0
+int string mtrl_picker_range_header_selected 0x0
+int string mtrl_picker_range_header_title 0x0
+int string mtrl_picker_range_header_unselected 0x0
+int string mtrl_picker_save 0x0
+int string mtrl_picker_text_input_date_hint 0x0
+int string mtrl_picker_text_input_date_range_end_hint 0x0
+int string mtrl_picker_text_input_date_range_start_hint 0x0
+int string mtrl_picker_text_input_day_abbr 0x0
+int string mtrl_picker_text_input_month_abbr 0x0
+int string mtrl_picker_text_input_year_abbr 0x0
+int string mtrl_picker_toggle_to_calendar_input_mode 0x0
+int string mtrl_picker_toggle_to_day_selection 0x0
+int string mtrl_picker_toggle_to_text_input_mode 0x0
+int string mtrl_picker_toggle_to_year_selection 0x0
+int string password_toggle_content_description 0x0
+int string path_password_eye 0x0
+int string path_password_eye_mask_strike_through 0x0
+int string path_password_eye_mask_visible 0x0
+int string path_password_strike_through 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string state_unselected_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Animation_Design_BottomSheetDialog 0x0
+int style Animation_MaterialComponents_BottomSheetDialog 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_CardView 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x0
+int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x0
+int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_MaterialComponents_Badge 0x0
+int style Base_TextAppearance_MaterialComponents_Button 0x0
+int style Base_TextAppearance_MaterialComponents_Headline6 0x0
+int style Base_TextAppearance_MaterialComponents_Subtitle2 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_ThemeOverlay_MaterialComponents_Dialog 0x0
+int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x0
+int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_Theme_MaterialComponents 0x0
+int style Base_Theme_MaterialComponents_Bridge 0x0
+int style Base_Theme_MaterialComponents_CompactMenu 0x0
+int style Base_Theme_MaterialComponents_Dialog 0x0
+int style Base_Theme_MaterialComponents_DialogWhenLarge 0x0
+int style Base_Theme_MaterialComponents_Dialog_Alert 0x0
+int style Base_Theme_MaterialComponents_Dialog_Bridge 0x0
+int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x0
+int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x0
+int style Base_Theme_MaterialComponents_Light 0x0
+int style Base_Theme_MaterialComponents_Light_Bridge 0x0
+int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x0
+int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x0
+int style Base_Theme_MaterialComponents_Light_Dialog 0x0
+int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x0
+int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x0
+int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x0
+int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x0
+int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x0
+int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x0
+int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x0
+int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x0
+int style Base_V14_Theme_MaterialComponents 0x0
+int style Base_V14_Theme_MaterialComponents_Bridge 0x0
+int style Base_V14_Theme_MaterialComponents_Dialog 0x0
+int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x0
+int style Base_V14_Theme_MaterialComponents_Light 0x0
+int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x0
+int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x0
+int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x0
+int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Base_Widget_Design_TabLayout 0x0
+int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x0
+int style Base_Widget_MaterialComponents_CheckedTextView 0x0
+int style Base_Widget_MaterialComponents_Chip 0x0
+int style Base_Widget_MaterialComponents_PopupMenu 0x0
+int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x0
+int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x0
+int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x0
+int style Base_Widget_MaterialComponents_TextInputEditText 0x0
+int style Base_Widget_MaterialComponents_TextInputLayout 0x0
+int style Base_Widget_MaterialComponents_TextView 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style CardView 0x0
+int style CardView_Dark 0x0
+int style CardView_Light 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style EmptyTheme 0x0
+int style MaterialAlertDialog_MaterialComponents 0x0
+int style MaterialAlertDialog_MaterialComponents_Body_Text 0x0
+int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x0
+int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x0
+int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x0
+int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x0
+int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x0
+int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x0
+int style MaterialAlertDialog_MaterialComponents_Title_Text 0x0
+int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_MaterialComponents 0x0
+int style Platform_MaterialComponents_Dialog 0x0
+int style Platform_MaterialComponents_Light 0x0
+int style Platform_MaterialComponents_Light_Dialog 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style ShapeAppearanceOverlay 0x0
+int style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize 0x0
+int style ShapeAppearanceOverlay_BottomRightCut 0x0
+int style ShapeAppearanceOverlay_Cut 0x0
+int style ShapeAppearanceOverlay_DifferentCornerSize 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x0
+int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x0
+int style ShapeAppearanceOverlay_TopLeftCut 0x0
+int style ShapeAppearanceOverlay_TopRightDifferentCornerSize 0x0
+int style ShapeAppearance_MaterialComponents 0x0
+int style ShapeAppearance_MaterialComponents_LargeComponent 0x0
+int style ShapeAppearance_MaterialComponents_MediumComponent 0x0
+int style ShapeAppearance_MaterialComponents_SmallComponent 0x0
+int style ShapeAppearance_MaterialComponents_Test 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TestStyleWithLineHeight 0x0
+int style TestStyleWithLineHeightAppearance 0x0
+int style TestStyleWithThemeLineHeightAttribute 0x0
+int style TestStyleWithoutLineHeight 0x0
+int style TestThemeWithLineHeight 0x0
+int style TestThemeWithLineHeightDisabled 0x0
+int style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x0
+int style Test_Theme_MaterialComponents_MaterialCalendar 0x0
+int style Test_Widget_MaterialComponents_MaterialCalendar 0x0
+int style Test_Widget_MaterialComponents_MaterialCalendar_Day 0x0
+int style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Design_CollapsingToolbar_Expanded 0x0
+int style TextAppearance_Design_Counter 0x0
+int style TextAppearance_Design_Counter_Overflow 0x0
+int style TextAppearance_Design_Error 0x0
+int style TextAppearance_Design_HelperText 0x0
+int style TextAppearance_Design_Hint 0x0
+int style TextAppearance_Design_Snackbar_Message 0x0
+int style TextAppearance_Design_Tab 0x0
+int style TextAppearance_MaterialComponents_Badge 0x0
+int style TextAppearance_MaterialComponents_Body1 0x0
+int style TextAppearance_MaterialComponents_Body2 0x0
+int style TextAppearance_MaterialComponents_Button 0x0
+int style TextAppearance_MaterialComponents_Caption 0x0
+int style TextAppearance_MaterialComponents_Chip 0x0
+int style TextAppearance_MaterialComponents_Headline1 0x0
+int style TextAppearance_MaterialComponents_Headline2 0x0
+int style TextAppearance_MaterialComponents_Headline3 0x0
+int style TextAppearance_MaterialComponents_Headline4 0x0
+int style TextAppearance_MaterialComponents_Headline5 0x0
+int style TextAppearance_MaterialComponents_Headline6 0x0
+int style TextAppearance_MaterialComponents_Overline 0x0
+int style TextAppearance_MaterialComponents_Subtitle1 0x0
+int style TextAppearance_MaterialComponents_Subtitle2 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_DayNight 0x0
+int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style ThemeOverlay_Design_TextInputEditText 0x0
+int style ThemeOverlay_MaterialComponents 0x0
+int style ThemeOverlay_MaterialComponents_ActionBar 0x0
+int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x0
+int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x0
+int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x0
+int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x0
+int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x0
+int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x0
+int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x0
+int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x0
+int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x0
+int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x0
+int style ThemeOverlay_MaterialComponents_Dark 0x0
+int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x0
+int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x0
+int style ThemeOverlay_MaterialComponents_Dialog 0x0
+int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x0
+int style ThemeOverlay_MaterialComponents_Light 0x0
+int style ThemeOverlay_MaterialComponents_Light_BottomSheetDialog 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x0
+int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x0
+int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x0
+int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x0
+int style ThemeOverlay_MaterialComponents_TextInputEditText 0x0
+int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x0
+int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x0
+int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x0
+int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x0
+int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x0
+int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Empty 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_AutofillInlineSuggestion 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_Design 0x0
+int style Theme_Design_BottomSheetDialog 0x0
+int style Theme_Design_Light 0x0
+int style Theme_Design_Light_BottomSheetDialog 0x0
+int style Theme_Design_Light_NoActionBar 0x0
+int style Theme_Design_NoActionBar 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_MaterialComponents 0x0
+int style Theme_MaterialComponents_BottomSheetDialog 0x0
+int style Theme_MaterialComponents_Bridge 0x0
+int style Theme_MaterialComponents_CompactMenu 0x0
+int style Theme_MaterialComponents_DayNight 0x0
+int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x0
+int style Theme_MaterialComponents_DayNight_Bridge 0x0
+int style Theme_MaterialComponents_DayNight_DarkActionBar 0x0
+int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x0
+int style Theme_MaterialComponents_DayNight_Dialog 0x0
+int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x0
+int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x0
+int style Theme_MaterialComponents_DayNight_NoActionBar 0x0
+int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x0
+int style Theme_MaterialComponents_Dialog 0x0
+int style Theme_MaterialComponents_DialogWhenLarge 0x0
+int style Theme_MaterialComponents_Dialog_Alert 0x0
+int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x0
+int style Theme_MaterialComponents_Dialog_Bridge 0x0
+int style Theme_MaterialComponents_Dialog_FixedSize 0x0
+int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x0
+int style Theme_MaterialComponents_Dialog_MinWidth 0x0
+int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x0
+int style Theme_MaterialComponents_Light 0x0
+int style Theme_MaterialComponents_Light_BarSize 0x0
+int style Theme_MaterialComponents_Light_BottomSheetDialog 0x0
+int style Theme_MaterialComponents_Light_Bridge 0x0
+int style Theme_MaterialComponents_Light_DarkActionBar 0x0
+int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x0
+int style Theme_MaterialComponents_Light_Dialog 0x0
+int style Theme_MaterialComponents_Light_DialogWhenLarge 0x0
+int style Theme_MaterialComponents_Light_Dialog_Alert 0x0
+int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x0
+int style Theme_MaterialComponents_Light_Dialog_Bridge 0x0
+int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x0
+int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x0
+int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x0
+int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x0
+int style Theme_MaterialComponents_Light_LargeTouch 0x0
+int style Theme_MaterialComponents_Light_NoActionBar 0x0
+int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x0
+int style Theme_MaterialComponents_NoActionBar 0x0
+int style Theme_MaterialComponents_NoActionBar_Bridge 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Autofill 0x0
+int style Widget_Autofill_InlineSuggestionChip 0x0
+int style Widget_Autofill_InlineSuggestionEndIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionStartIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionSubtitle 0x0
+int style Widget_Autofill_InlineSuggestionTitle 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Design_AppBarLayout 0x0
+int style Widget_Design_BottomNavigationView 0x0
+int style Widget_Design_BottomSheet_Modal 0x0
+int style Widget_Design_CollapsingToolbar 0x0
+int style Widget_Design_FloatingActionButton 0x0
+int style Widget_Design_NavigationView 0x0
+int style Widget_Design_ScrimInsetsFrameLayout 0x0
+int style Widget_Design_Snackbar 0x0
+int style Widget_Design_TabLayout 0x0
+int style Widget_Design_TextInputLayout 0x0
+int style Widget_MaterialComponents_ActionBar_Primary 0x0
+int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x0
+int style Widget_MaterialComponents_ActionBar_Solid 0x0
+int style Widget_MaterialComponents_ActionBar_Surface 0x0
+int style Widget_MaterialComponents_AppBarLayout_Primary 0x0
+int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x0
+int style Widget_MaterialComponents_AppBarLayout_Surface 0x0
+int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x0
+int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x0
+int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x0
+int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x0
+int style Widget_MaterialComponents_Badge 0x0
+int style Widget_MaterialComponents_BottomAppBar 0x0
+int style Widget_MaterialComponents_BottomAppBar_Colored 0x0
+int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x0
+int style Widget_MaterialComponents_BottomNavigationView 0x0
+int style Widget_MaterialComponents_BottomNavigationView_Colored 0x0
+int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x0
+int style Widget_MaterialComponents_BottomSheet 0x0
+int style Widget_MaterialComponents_BottomSheet_Modal 0x0
+int style Widget_MaterialComponents_Button 0x0
+int style Widget_MaterialComponents_Button_Icon 0x0
+int style Widget_MaterialComponents_Button_OutlinedButton 0x0
+int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x0
+int style Widget_MaterialComponents_Button_TextButton 0x0
+int style Widget_MaterialComponents_Button_TextButton_Dialog 0x0
+int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x0
+int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x0
+int style Widget_MaterialComponents_Button_TextButton_Icon 0x0
+int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x0
+int style Widget_MaterialComponents_Button_UnelevatedButton 0x0
+int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x0
+int style Widget_MaterialComponents_CardView 0x0
+int style Widget_MaterialComponents_CheckedTextView 0x0
+int style Widget_MaterialComponents_ChipGroup 0x0
+int style Widget_MaterialComponents_Chip_Action 0x0
+int style Widget_MaterialComponents_Chip_Choice 0x0
+int style Widget_MaterialComponents_Chip_Entry 0x0
+int style Widget_MaterialComponents_Chip_Filter 0x0
+int style Widget_MaterialComponents_CompoundButton_CheckBox 0x0
+int style Widget_MaterialComponents_CompoundButton_RadioButton 0x0
+int style Widget_MaterialComponents_CompoundButton_Switch 0x0
+int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x0
+int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x0
+int style Widget_MaterialComponents_FloatingActionButton 0x0
+int style Widget_MaterialComponents_Light_ActionBar_Solid 0x0
+int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x0
+int style Widget_MaterialComponents_MaterialCalendar 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Day 0x0
+int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x0
+int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Item 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Year 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x0
+int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x0
+int style Widget_MaterialComponents_NavigationView 0x0
+int style Widget_MaterialComponents_PopupMenu 0x0
+int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x0
+int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x0
+int style Widget_MaterialComponents_PopupMenu_Overflow 0x0
+int style Widget_MaterialComponents_Snackbar 0x0
+int style Widget_MaterialComponents_Snackbar_FullWidth 0x0
+int style Widget_MaterialComponents_TabLayout 0x0
+int style Widget_MaterialComponents_TabLayout_Colored 0x0
+int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x0
+int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x0
+int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x0
+int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x0
+int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x0
+int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x0
+int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x0
+int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x0
+int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x0
+int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x0
+int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x0
+int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x0
+int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x0
+int style Widget_MaterialComponents_TextView 0x0
+int style Widget_MaterialComponents_Toolbar 0x0
+int style Widget_MaterialComponents_Toolbar_Primary 0x0
+int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x0
+int style Widget_MaterialComponents_Toolbar_Surface 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int style redboxButton 0x0
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppBarLayout { 0x10100d4, 0x1010540, 0x101048f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppBarLayout_android_background 0
+int styleable AppBarLayout_android_keyboardNavigationCluster 1
+int styleable AppBarLayout_android_touchscreenBlocksFocus 2
+int styleable AppBarLayout_elevation 3
+int styleable AppBarLayout_expanded 4
+int styleable AppBarLayout_liftOnScroll 5
+int styleable AppBarLayout_liftOnScrollTargetViewId 6
+int styleable AppBarLayout_statusBarForeground 7
+int[] styleable AppBarLayoutStates { 0x0, 0x0, 0x0, 0x0 }
+int styleable AppBarLayoutStates_state_collapsed 0
+int styleable AppBarLayoutStates_state_collapsible 1
+int styleable AppBarLayoutStates_state_liftable 2
+int styleable AppBarLayoutStates_state_lifted 3
+int[] styleable AppBarLayout_Layout { 0x0, 0x0 }
+int styleable AppBarLayout_Layout_layout_scrollFlags 0
+int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
+int[] styleable AppCompatEmojiHelper {  }
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_drawableBottomCompat 6
+int styleable AppCompatTextView_drawableEndCompat 7
+int styleable AppCompatTextView_drawableLeftCompat 8
+int styleable AppCompatTextView_drawableRightCompat 9
+int styleable AppCompatTextView_drawableStartCompat 10
+int styleable AppCompatTextView_drawableTint 11
+int styleable AppCompatTextView_drawableTintMode 12
+int styleable AppCompatTextView_drawableTopCompat 13
+int styleable AppCompatTextView_emojiCompatEnabled 14
+int styleable AppCompatTextView_firstBaselineToTopHeight 15
+int styleable AppCompatTextView_fontFamily 16
+int styleable AppCompatTextView_fontVariationSettings 17
+int styleable AppCompatTextView_lastBaselineToBottomHeight 18
+int styleable AppCompatTextView_lineHeight 19
+int styleable AppCompatTextView_textAllCaps 20
+int styleable AppCompatTextView_textLocale 21
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseContentDescription 17
+int styleable AppCompatTheme_actionModeCloseDrawable 18
+int styleable AppCompatTheme_actionModeCopyDrawable 19
+int styleable AppCompatTheme_actionModeCutDrawable 20
+int styleable AppCompatTheme_actionModeFindDrawable 21
+int styleable AppCompatTheme_actionModePasteDrawable 22
+int styleable AppCompatTheme_actionModePopupWindowStyle 23
+int styleable AppCompatTheme_actionModeSelectAllDrawable 24
+int styleable AppCompatTheme_actionModeShareDrawable 25
+int styleable AppCompatTheme_actionModeSplitBackground 26
+int styleable AppCompatTheme_actionModeStyle 27
+int styleable AppCompatTheme_actionModeTheme 28
+int styleable AppCompatTheme_actionModeWebSearchDrawable 29
+int styleable AppCompatTheme_actionOverflowButtonStyle 30
+int styleable AppCompatTheme_actionOverflowMenuStyle 31
+int styleable AppCompatTheme_activityChooserViewStyle 32
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
+int styleable AppCompatTheme_alertDialogCenterButtons 34
+int styleable AppCompatTheme_alertDialogStyle 35
+int styleable AppCompatTheme_alertDialogTheme 36
+int styleable AppCompatTheme_android_windowAnimationStyle 37
+int styleable AppCompatTheme_android_windowIsFloating 38
+int styleable AppCompatTheme_autoCompleteTextViewStyle 39
+int styleable AppCompatTheme_borderlessButtonStyle 40
+int styleable AppCompatTheme_buttonBarButtonStyle 41
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
+int styleable AppCompatTheme_buttonBarStyle 45
+int styleable AppCompatTheme_buttonStyle 46
+int styleable AppCompatTheme_buttonStyleSmall 47
+int styleable AppCompatTheme_checkboxStyle 48
+int styleable AppCompatTheme_checkedTextViewStyle 49
+int styleable AppCompatTheme_colorAccent 50
+int styleable AppCompatTheme_colorBackgroundFloating 51
+int styleable AppCompatTheme_colorButtonNormal 52
+int styleable AppCompatTheme_colorControlActivated 53
+int styleable AppCompatTheme_colorControlHighlight 54
+int styleable AppCompatTheme_colorControlNormal 55
+int styleable AppCompatTheme_colorError 56
+int styleable AppCompatTheme_colorPrimary 57
+int styleable AppCompatTheme_colorPrimaryDark 58
+int styleable AppCompatTheme_colorSwitchThumbNormal 59
+int styleable AppCompatTheme_controlBackground 60
+int styleable AppCompatTheme_dialogCornerRadius 61
+int styleable AppCompatTheme_dialogPreferredPadding 62
+int styleable AppCompatTheme_dialogTheme 63
+int styleable AppCompatTheme_dividerHorizontal 64
+int styleable AppCompatTheme_dividerVertical 65
+int styleable AppCompatTheme_dropDownListViewStyle 66
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
+int styleable AppCompatTheme_editTextBackground 68
+int styleable AppCompatTheme_editTextColor 69
+int styleable AppCompatTheme_editTextStyle 70
+int styleable AppCompatTheme_homeAsUpIndicator 71
+int styleable AppCompatTheme_imageButtonStyle 72
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
+int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
+int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
+int styleable AppCompatTheme_listDividerAlertDialog 76
+int styleable AppCompatTheme_listMenuViewStyle 77
+int styleable AppCompatTheme_listPopupWindowStyle 78
+int styleable AppCompatTheme_listPreferredItemHeight 79
+int styleable AppCompatTheme_listPreferredItemHeightLarge 80
+int styleable AppCompatTheme_listPreferredItemHeightSmall 81
+int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
+int styleable AppCompatTheme_listPreferredItemPaddingRight 84
+int styleable AppCompatTheme_listPreferredItemPaddingStart 85
+int styleable AppCompatTheme_panelBackground 86
+int styleable AppCompatTheme_panelMenuListTheme 87
+int styleable AppCompatTheme_panelMenuListWidth 88
+int styleable AppCompatTheme_popupMenuStyle 89
+int styleable AppCompatTheme_popupWindowStyle 90
+int styleable AppCompatTheme_radioButtonStyle 91
+int styleable AppCompatTheme_ratingBarStyle 92
+int styleable AppCompatTheme_ratingBarStyleIndicator 93
+int styleable AppCompatTheme_ratingBarStyleSmall 94
+int styleable AppCompatTheme_searchViewStyle 95
+int styleable AppCompatTheme_seekBarStyle 96
+int styleable AppCompatTheme_selectableItemBackground 97
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
+int styleable AppCompatTheme_spinnerDropDownItemStyle 99
+int styleable AppCompatTheme_spinnerStyle 100
+int styleable AppCompatTheme_switchStyle 101
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
+int styleable AppCompatTheme_textAppearanceListItem 103
+int styleable AppCompatTheme_textAppearanceListItemSecondary 104
+int styleable AppCompatTheme_textAppearanceListItemSmall 105
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
+int styleable AppCompatTheme_textColorAlertDialogListItem 110
+int styleable AppCompatTheme_textColorSearchUrl 111
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
+int styleable AppCompatTheme_toolbarStyle 113
+int styleable AppCompatTheme_tooltipForegroundColor 114
+int styleable AppCompatTheme_tooltipFrameBackground 115
+int styleable AppCompatTheme_viewInflaterClass 116
+int styleable AppCompatTheme_windowActionBar 117
+int styleable AppCompatTheme_windowActionBarOverlay 118
+int styleable AppCompatTheme_windowActionModeOverlay 119
+int styleable AppCompatTheme_windowFixedHeightMajor 120
+int styleable AppCompatTheme_windowFixedHeightMinor 121
+int styleable AppCompatTheme_windowFixedWidthMajor 122
+int styleable AppCompatTheme_windowFixedWidthMinor 123
+int styleable AppCompatTheme_windowMinWidthMajor 124
+int styleable AppCompatTheme_windowMinWidthMinor 125
+int styleable AppCompatTheme_windowNoTitle 126
+int[] styleable Autofill_InlineSuggestion { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionChip 0
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionEndIconStyle 1
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionStartIconStyle 2
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionSubtitle 3
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionTitle 4
+int styleable Autofill_InlineSuggestion_isAutofillInlineSuggestionTheme 5
+int[] styleable Badge { 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Badge_backgroundColor 0
+int styleable Badge_badgeGravity 1
+int styleable Badge_badgeTextColor 2
+int styleable Badge_maxCharacterCount 3
+int styleable Badge_number 4
+int[] styleable BottomAppBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable BottomAppBar_backgroundTint 0
+int styleable BottomAppBar_elevation 1
+int styleable BottomAppBar_fabAlignmentMode 2
+int styleable BottomAppBar_fabAnimationMode 3
+int styleable BottomAppBar_fabCradleMargin 4
+int styleable BottomAppBar_fabCradleRoundedCornerRadius 5
+int styleable BottomAppBar_fabCradleVerticalOffset 6
+int styleable BottomAppBar_hideOnScroll 7
+int[] styleable BottomNavigationView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable BottomNavigationView_backgroundTint 0
+int styleable BottomNavigationView_elevation 1
+int styleable BottomNavigationView_itemBackground 2
+int styleable BottomNavigationView_itemHorizontalTranslationEnabled 3
+int styleable BottomNavigationView_itemIconSize 4
+int styleable BottomNavigationView_itemIconTint 5
+int styleable BottomNavigationView_itemRippleColor 6
+int styleable BottomNavigationView_itemTextAppearanceActive 7
+int styleable BottomNavigationView_itemTextAppearanceInactive 8
+int styleable BottomNavigationView_itemTextColor 9
+int styleable BottomNavigationView_labelVisibilityMode 10
+int styleable BottomNavigationView_menu 11
+int[] styleable BottomSheetBehavior_Layout { 0x1010440, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable BottomSheetBehavior_Layout_android_elevation 0
+int styleable BottomSheetBehavior_Layout_backgroundTint 1
+int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 2
+int styleable BottomSheetBehavior_Layout_behavior_fitToContents 3
+int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 4
+int styleable BottomSheetBehavior_Layout_behavior_hideable 5
+int styleable BottomSheetBehavior_Layout_behavior_peekHeight 6
+int styleable BottomSheetBehavior_Layout_behavior_saveFlags 7
+int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 8
+int styleable BottomSheetBehavior_Layout_shapeAppearance 9
+int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 10
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable Capability { 0x0, 0x0 }
+int styleable Capability_queryPatterns 0
+int styleable Capability_shortcutMatchRequired 1
+int[] styleable CardView { 0x1010140, 0x101013f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CardView_android_minHeight 0
+int styleable CardView_android_minWidth 1
+int styleable CardView_cardBackgroundColor 2
+int styleable CardView_cardCornerRadius 3
+int styleable CardView_cardElevation 4
+int styleable CardView_cardMaxElevation 5
+int styleable CardView_cardPreventCornerOverlap 6
+int styleable CardView_cardUseCompatPadding 7
+int styleable CardView_contentPadding 8
+int styleable CardView_contentPaddingBottom 9
+int styleable CardView_contentPaddingLeft 10
+int styleable CardView_contentPaddingRight 11
+int styleable CardView_contentPaddingTop 12
+int[] styleable CheckedTextView { 0x1010108, 0x0, 0x0, 0x0 }
+int styleable CheckedTextView_android_checkMark 0
+int styleable CheckedTextView_checkMarkCompat 1
+int styleable CheckedTextView_checkMarkTint 2
+int styleable CheckedTextView_checkMarkTintMode 3
+int[] styleable Chip { 0x10101e5, 0x10100ab, 0x101011f, 0x101014f, 0x1010034, 0x1010098, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Chip_android_checkable 0
+int styleable Chip_android_ellipsize 1
+int styleable Chip_android_maxWidth 2
+int styleable Chip_android_text 3
+int styleable Chip_android_textAppearance 4
+int styleable Chip_android_textColor 5
+int styleable Chip_checkedIcon 6
+int styleable Chip_checkedIconEnabled 7
+int styleable Chip_checkedIconVisible 8
+int styleable Chip_chipBackgroundColor 9
+int styleable Chip_chipCornerRadius 10
+int styleable Chip_chipEndPadding 11
+int styleable Chip_chipIcon 12
+int styleable Chip_chipIconEnabled 13
+int styleable Chip_chipIconSize 14
+int styleable Chip_chipIconTint 15
+int styleable Chip_chipIconVisible 16
+int styleable Chip_chipMinHeight 17
+int styleable Chip_chipMinTouchTargetSize 18
+int styleable Chip_chipStartPadding 19
+int styleable Chip_chipStrokeColor 20
+int styleable Chip_chipStrokeWidth 21
+int styleable Chip_chipSurfaceColor 22
+int styleable Chip_closeIcon 23
+int styleable Chip_closeIconEnabled 24
+int styleable Chip_closeIconEndPadding 25
+int styleable Chip_closeIconSize 26
+int styleable Chip_closeIconStartPadding 27
+int styleable Chip_closeIconTint 28
+int styleable Chip_closeIconVisible 29
+int styleable Chip_ensureMinTouchTargetSize 30
+int styleable Chip_hideMotionSpec 31
+int styleable Chip_iconEndPadding 32
+int styleable Chip_iconStartPadding 33
+int styleable Chip_rippleColor 34
+int styleable Chip_shapeAppearance 35
+int styleable Chip_shapeAppearanceOverlay 36
+int styleable Chip_showMotionSpec 37
+int styleable Chip_textEndPadding 38
+int styleable Chip_textStartPadding 39
+int[] styleable ChipGroup { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ChipGroup_checkedChip 0
+int styleable ChipGroup_chipSpacing 1
+int styleable ChipGroup_chipSpacingHorizontal 2
+int styleable ChipGroup_chipSpacingVertical 3
+int styleable ChipGroup_singleLine 4
+int styleable ChipGroup_singleSelection 5
+int[] styleable CollapsingToolbarLayout { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
+int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
+int styleable CollapsingToolbarLayout_contentScrim 2
+int styleable CollapsingToolbarLayout_expandedTitleGravity 3
+int styleable CollapsingToolbarLayout_expandedTitleMargin 4
+int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
+int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
+int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
+int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
+int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
+int styleable CollapsingToolbarLayout_scrimAnimationDuration 10
+int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 11
+int styleable CollapsingToolbarLayout_statusBarScrim 12
+int styleable CollapsingToolbarLayout_title 13
+int styleable CollapsingToolbarLayout_titleEnabled 14
+int styleable CollapsingToolbarLayout_toolbarId 15
+int[] styleable CollapsingToolbarLayout_Layout { 0x0, 0x0 }
+int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
+int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x1010647, 0x0 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int styleable ColorStateListItem_android_lStar 3
+int styleable ColorStateListItem_lStar 4
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonCompat 1
+int styleable CompoundButton_buttonTint 2
+int styleable CompoundButton_buttonTintMode 3
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable ExtendedFloatingActionButton { 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ExtendedFloatingActionButton_elevation 0
+int styleable ExtendedFloatingActionButton_extendMotionSpec 1
+int styleable ExtendedFloatingActionButton_hideMotionSpec 2
+int styleable ExtendedFloatingActionButton_showMotionSpec 3
+int styleable ExtendedFloatingActionButton_shrinkMotionSpec 4
+int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x0, 0x0 }
+int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
+int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
+int[] styleable FloatingActionButton { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FloatingActionButton_backgroundTint 0
+int styleable FloatingActionButton_backgroundTintMode 1
+int styleable FloatingActionButton_borderWidth 2
+int styleable FloatingActionButton_elevation 3
+int styleable FloatingActionButton_ensureMinTouchTargetSize 4
+int styleable FloatingActionButton_fabCustomSize 5
+int styleable FloatingActionButton_fabSize 6
+int styleable FloatingActionButton_hideMotionSpec 7
+int styleable FloatingActionButton_hoveredFocusedTranslationZ 8
+int styleable FloatingActionButton_maxImageSize 9
+int styleable FloatingActionButton_pressedTranslationZ 10
+int styleable FloatingActionButton_rippleColor 11
+int styleable FloatingActionButton_shapeAppearance 12
+int styleable FloatingActionButton_shapeAppearanceOverlay 13
+int styleable FloatingActionButton_showMotionSpec 14
+int styleable FloatingActionButton_useCompatPadding 15
+int[] styleable FloatingActionButton_Behavior_Layout { 0x0 }
+int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
+int[] styleable FlowLayout { 0x0, 0x0 }
+int styleable FlowLayout_itemSpacing 0
+int styleable FlowLayout_lineSpacing 1
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int styleable FontFamily_fontProviderSystemFontFamily 6
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable ForegroundLinearLayout { 0x1010109, 0x1010200, 0x0 }
+int styleable ForegroundLinearLayout_android_foreground 0
+int styleable ForegroundLinearLayout_android_foregroundGravity 1
+int styleable ForegroundLinearLayout_foregroundInsidePadding 2
+int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
+int styleable Fragment_android_id 0
+int styleable Fragment_android_name 1
+int styleable Fragment_android_tag 2
+int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
+int styleable FragmentContainerView_android_name 0
+int styleable FragmentContainerView_android_tag 1
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable MaterialAlertDialog { 0x0, 0x0, 0x0, 0x0 }
+int styleable MaterialAlertDialog_backgroundInsetBottom 0
+int styleable MaterialAlertDialog_backgroundInsetEnd 1
+int styleable MaterialAlertDialog_backgroundInsetStart 2
+int styleable MaterialAlertDialog_backgroundInsetTop 3
+int[] styleable MaterialAlertDialogTheme { 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
+int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 1
+int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 2
+int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 3
+int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 4
+int[] styleable MaterialButton { 0x10101e5, 0x10101ba, 0x10101b7, 0x10101b8, 0x10101b9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MaterialButton_android_checkable 0
+int styleable MaterialButton_android_insetBottom 1
+int styleable MaterialButton_android_insetLeft 2
+int styleable MaterialButton_android_insetRight 3
+int styleable MaterialButton_android_insetTop 4
+int styleable MaterialButton_backgroundTint 5
+int styleable MaterialButton_backgroundTintMode 6
+int styleable MaterialButton_cornerRadius 7
+int styleable MaterialButton_elevation 8
+int styleable MaterialButton_icon 9
+int styleable MaterialButton_iconGravity 10
+int styleable MaterialButton_iconPadding 11
+int styleable MaterialButton_iconSize 12
+int styleable MaterialButton_iconTint 13
+int styleable MaterialButton_iconTintMode 14
+int styleable MaterialButton_rippleColor 15
+int styleable MaterialButton_shapeAppearance 16
+int styleable MaterialButton_shapeAppearanceOverlay 17
+int styleable MaterialButton_strokeColor 18
+int styleable MaterialButton_strokeWidth 19
+int[] styleable MaterialButtonToggleGroup { 0x0, 0x0 }
+int styleable MaterialButtonToggleGroup_checkedButton 0
+int styleable MaterialButtonToggleGroup_singleSelection 1
+int[] styleable MaterialCalendar { 0x101020d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MaterialCalendar_android_windowFullscreen 0
+int styleable MaterialCalendar_dayInvalidStyle 1
+int styleable MaterialCalendar_daySelectedStyle 2
+int styleable MaterialCalendar_dayStyle 3
+int styleable MaterialCalendar_dayTodayStyle 4
+int styleable MaterialCalendar_rangeFillColor 5
+int styleable MaterialCalendar_yearSelectedStyle 6
+int styleable MaterialCalendar_yearStyle 7
+int styleable MaterialCalendar_yearTodayStyle 8
+int[] styleable MaterialCalendarItem { 0x10101ba, 0x10101b7, 0x10101b8, 0x10101b9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MaterialCalendarItem_android_insetBottom 0
+int styleable MaterialCalendarItem_android_insetLeft 1
+int styleable MaterialCalendarItem_android_insetRight 2
+int styleable MaterialCalendarItem_android_insetTop 3
+int styleable MaterialCalendarItem_itemFillColor 4
+int styleable MaterialCalendarItem_itemShapeAppearance 5
+int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
+int styleable MaterialCalendarItem_itemStrokeColor 7
+int styleable MaterialCalendarItem_itemStrokeWidth 8
+int styleable MaterialCalendarItem_itemTextColor 9
+int[] styleable MaterialCardView { 0x10101e5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MaterialCardView_android_checkable 0
+int styleable MaterialCardView_cardForegroundColor 1
+int styleable MaterialCardView_checkedIcon 2
+int styleable MaterialCardView_checkedIconTint 3
+int styleable MaterialCardView_rippleColor 4
+int styleable MaterialCardView_shapeAppearance 5
+int styleable MaterialCardView_shapeAppearanceOverlay 6
+int styleable MaterialCardView_state_dragged 7
+int styleable MaterialCardView_strokeColor 8
+int styleable MaterialCardView_strokeWidth 9
+int[] styleable MaterialCheckBox { 0x0, 0x0 }
+int styleable MaterialCheckBox_buttonTint 0
+int styleable MaterialCheckBox_useMaterialThemeColors 1
+int[] styleable MaterialRadioButton { 0x0 }
+int styleable MaterialRadioButton_useMaterialThemeColors 0
+int[] styleable MaterialShape { 0x0, 0x0 }
+int styleable MaterialShape_shapeAppearance 0
+int styleable MaterialShape_shapeAppearanceOverlay 1
+int[] styleable MaterialTextAppearance { 0x101057f, 0x0 }
+int styleable MaterialTextAppearance_android_lineHeight 0
+int styleable MaterialTextAppearance_lineHeight 1
+int[] styleable MaterialTextView { 0x101057f, 0x1010034, 0x0 }
+int styleable MaterialTextView_android_lineHeight 0
+int styleable MaterialTextView_android_textAppearance 1
+int styleable MaterialTextView_lineHeight 2
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable NavigationView { 0x10100d4, 0x10100dd, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable NavigationView_android_background 0
+int styleable NavigationView_android_fitsSystemWindows 1
+int styleable NavigationView_android_maxWidth 2
+int styleable NavigationView_elevation 3
+int styleable NavigationView_headerLayout 4
+int styleable NavigationView_itemBackground 5
+int styleable NavigationView_itemHorizontalPadding 6
+int styleable NavigationView_itemIconPadding 7
+int styleable NavigationView_itemIconSize 8
+int styleable NavigationView_itemIconTint 9
+int styleable NavigationView_itemMaxLines 10
+int styleable NavigationView_itemShapeAppearance 11
+int styleable NavigationView_itemShapeAppearanceOverlay 12
+int styleable NavigationView_itemShapeFillColor 13
+int styleable NavigationView_itemShapeInsetBottom 14
+int styleable NavigationView_itemShapeInsetEnd 15
+int styleable NavigationView_itemShapeInsetStart 16
+int styleable NavigationView_itemShapeInsetTop 17
+int styleable NavigationView_itemTextAppearance 18
+int styleable NavigationView_itemTextColor 19
+int styleable NavigationView_menu 20
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable RecyclerView { 0x10100eb, 0x10100f1, 0x10100c4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable RecyclerView_android_clipToPadding 0
+int styleable RecyclerView_android_descendantFocusability 1
+int styleable RecyclerView_android_orientation 2
+int styleable RecyclerView_fastScrollEnabled 3
+int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
+int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
+int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
+int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
+int styleable RecyclerView_layoutManager 8
+int styleable RecyclerView_reverseLayout 9
+int styleable RecyclerView_spanCount 10
+int styleable RecyclerView_stackFromEnd 11
+int[] styleable ScrimInsetsFrameLayout { 0x0 }
+int styleable ScrimInsetsFrameLayout_insetForeground 0
+int[] styleable ScrollingViewBehavior_Layout { 0x0 }
+int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable ShapeAppearance { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ShapeAppearance_cornerFamily 0
+int styleable ShapeAppearance_cornerFamilyBottomLeft 1
+int styleable ShapeAppearance_cornerFamilyBottomRight 2
+int styleable ShapeAppearance_cornerFamilyTopLeft 3
+int styleable ShapeAppearance_cornerFamilyTopRight 4
+int styleable ShapeAppearance_cornerSize 5
+int styleable ShapeAppearance_cornerSizeBottomLeft 6
+int styleable ShapeAppearance_cornerSizeBottomRight 7
+int styleable ShapeAppearance_cornerSizeTopLeft 8
+int styleable ShapeAppearance_cornerSizeTopRight 9
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Snackbar { 0x0, 0x0 }
+int styleable Snackbar_snackbarButtonStyle 0
+int styleable Snackbar_snackbarStyle 1
+int[] styleable SnackbarLayout { 0x0, 0x101011f, 0x0, 0x0, 0x0, 0x0 }
+int styleable SnackbarLayout_actionTextColorAlpha 0
+int styleable SnackbarLayout_android_maxWidth 1
+int styleable SnackbarLayout_animationMode 2
+int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
+int styleable SnackbarLayout_elevation 4
+int styleable SnackbarLayout_maxActionInlineWidth 5
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable SwitchMaterial { 0x0 }
+int styleable SwitchMaterial_useMaterialThemeColors 0
+int[] styleable TabItem { 0x1010002, 0x10100f2, 0x101014f }
+int styleable TabItem_android_icon 0
+int styleable TabItem_android_layout 1
+int styleable TabItem_android_text 2
+int[] styleable TabLayout { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable TabLayout_tabBackground 0
+int styleable TabLayout_tabContentStart 1
+int styleable TabLayout_tabGravity 2
+int styleable TabLayout_tabIconTint 3
+int styleable TabLayout_tabIconTintMode 4
+int styleable TabLayout_tabIndicator 5
+int styleable TabLayout_tabIndicatorAnimationDuration 6
+int styleable TabLayout_tabIndicatorColor 7
+int styleable TabLayout_tabIndicatorFullWidth 8
+int styleable TabLayout_tabIndicatorGravity 9
+int styleable TabLayout_tabIndicatorHeight 10
+int styleable TabLayout_tabInlineLabel 11
+int styleable TabLayout_tabMaxWidth 12
+int styleable TabLayout_tabMinWidth 13
+int styleable TabLayout_tabMode 14
+int styleable TabLayout_tabPadding 15
+int styleable TabLayout_tabPaddingBottom 16
+int styleable TabLayout_tabPaddingEnd 17
+int styleable TabLayout_tabPaddingStart 18
+int styleable TabLayout_tabPaddingTop 19
+int styleable TabLayout_tabRippleColor 20
+int styleable TabLayout_tabSelectedTextColor 21
+int styleable TabLayout_tabTextAppearance 22
+int styleable TabLayout_tabTextColor 23
+int styleable TabLayout_tabUnboundedRipple 24
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010585, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textFontWeight 8
+int styleable TextAppearance_android_textSize 9
+int styleable TextAppearance_android_textStyle 10
+int styleable TextAppearance_android_typeface 11
+int styleable TextAppearance_fontFamily 12
+int styleable TextAppearance_fontVariationSettings 13
+int styleable TextAppearance_textAllCaps 14
+int styleable TextAppearance_textLocale 15
+int[] styleable TextInputLayout { 0x1010150, 0x101009a, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable TextInputLayout_android_hint 0
+int styleable TextInputLayout_android_textColorHint 1
+int styleable TextInputLayout_boxBackgroundColor 2
+int styleable TextInputLayout_boxBackgroundMode 3
+int styleable TextInputLayout_boxCollapsedPaddingTop 4
+int styleable TextInputLayout_boxCornerRadiusBottomEnd 5
+int styleable TextInputLayout_boxCornerRadiusBottomStart 6
+int styleable TextInputLayout_boxCornerRadiusTopEnd 7
+int styleable TextInputLayout_boxCornerRadiusTopStart 8
+int styleable TextInputLayout_boxStrokeColor 9
+int styleable TextInputLayout_boxStrokeWidth 10
+int styleable TextInputLayout_boxStrokeWidthFocused 11
+int styleable TextInputLayout_counterEnabled 12
+int styleable TextInputLayout_counterMaxLength 13
+int styleable TextInputLayout_counterOverflowTextAppearance 14
+int styleable TextInputLayout_counterOverflowTextColor 15
+int styleable TextInputLayout_counterTextAppearance 16
+int styleable TextInputLayout_counterTextColor 17
+int styleable TextInputLayout_endIconCheckable 18
+int styleable TextInputLayout_endIconContentDescription 19
+int styleable TextInputLayout_endIconDrawable 20
+int styleable TextInputLayout_endIconMode 21
+int styleable TextInputLayout_endIconTint 22
+int styleable TextInputLayout_endIconTintMode 23
+int styleable TextInputLayout_errorEnabled 24
+int styleable TextInputLayout_errorIconDrawable 25
+int styleable TextInputLayout_errorIconTint 26
+int styleable TextInputLayout_errorIconTintMode 27
+int styleable TextInputLayout_errorTextAppearance 28
+int styleable TextInputLayout_errorTextColor 29
+int styleable TextInputLayout_helperText 30
+int styleable TextInputLayout_helperTextEnabled 31
+int styleable TextInputLayout_helperTextTextAppearance 32
+int styleable TextInputLayout_helperTextTextColor 33
+int styleable TextInputLayout_hintAnimationEnabled 34
+int styleable TextInputLayout_hintEnabled 35
+int styleable TextInputLayout_hintTextAppearance 36
+int styleable TextInputLayout_hintTextColor 37
+int styleable TextInputLayout_passwordToggleContentDescription 38
+int styleable TextInputLayout_passwordToggleDrawable 39
+int styleable TextInputLayout_passwordToggleEnabled 40
+int styleable TextInputLayout_passwordToggleTint 41
+int styleable TextInputLayout_passwordToggleTintMode 42
+int styleable TextInputLayout_shapeAppearance 43
+int styleable TextInputLayout_shapeAppearanceOverlay 44
+int styleable TextInputLayout_startIconCheckable 45
+int styleable TextInputLayout_startIconContentDescription 46
+int styleable TextInputLayout_startIconDrawable 47
+int styleable TextInputLayout_startIconTint 48
+int styleable TextInputLayout_startIconTintMode 49
+int[] styleable ThemeEnforcement { 0x1010034, 0x0, 0x0 }
+int styleable ThemeEnforcement_android_textAppearance 0
+int styleable ThemeEnforcement_enforceMaterialTheme 1
+int styleable ThemeEnforcement_enforceTextAppearance 2
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_menu 14
+int styleable Toolbar_navigationContentDescription 15
+int styleable Toolbar_navigationIcon 16
+int styleable Toolbar_popupTheme 17
+int styleable Toolbar_subtitle 18
+int styleable Toolbar_subtitleTextAppearance 19
+int styleable Toolbar_subtitleTextColor 20
+int styleable Toolbar_title 21
+int styleable Toolbar_titleMargin 22
+int styleable Toolbar_titleMarginBottom 23
+int styleable Toolbar_titleMarginEnd 24
+int styleable Toolbar_titleMarginStart 25
+int styleable Toolbar_titleMarginTop 26
+int styleable Toolbar_titleMargins 27
+int styleable Toolbar_titleTextAppearance 28
+int styleable Toolbar_titleTextColor 29
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewPager2 { 0x10100c4 }
+int styleable ViewPager2_android_orientation 0
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
+int xml standalone_badge 0x0
+int xml standalone_badge_gravity_bottom_end 0x0
+int xml standalone_badge_gravity_bottom_start 0x0
+int xml standalone_badge_gravity_top_start 0x0
diff --git a/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_in_from_left.xml.flat b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_in_from_left.xml.flat
new file mode 100644
index 0000000..c10301e
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_in_from_left.xml.flat differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_in_from_right.xml.flat b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_in_from_right.xml.flat
new file mode 100644
index 0000000..7c81243
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_in_from_right.xml.flat differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_out_to_left.xml.flat b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_out_to_left.xml.flat
new file mode 100644
index 0000000..01df36f
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_out_to_left.xml.flat differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_out_to_right.xml.flat b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_out_to_right.xml.flat
new file mode 100644
index 0000000..908a465
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/compiled_local_resources/debug/out/anim_rns_slide_out_to_right.xml.flat differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-screens/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..72ba766
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,5 @@
+#Sat Mar 22 09:48:03 IST 2025
+com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_in_from_right.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml
+com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_out_to_right.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml
+com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_out_to_left.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml
+com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_in_from_left.xml=/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml
diff --git a/node_modules/react-native-screens/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-screens/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..e9011d3
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res"><file name="rns_slide_in_from_right" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_in_from_right.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_right" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_out_to_right.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_left" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_out_to_left.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_left" path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/res/anim/rns_slide_in_from_left.xml" qualifiers="" type="anim"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-screens/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..7a638c1
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-screens/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..617711e
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-screens/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..a8dcd6b
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/build/intermediates/shader_assets/debug/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/BuildConfig.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/BuildConfig.class
new file mode 100644
index 0000000..c0848e0
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/BuildConfig.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/LifecycleHelper$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/LifecycleHelper$1.class
new file mode 100644
index 0000000..3e41def
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/LifecycleHelper$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/LifecycleHelper.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/LifecycleHelper.class
new file mode 100644
index 0000000..225c634
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/LifecycleHelper.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/RNScreensPackage.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/RNScreensPackage.class
new file mode 100644
index 0000000..949de5d
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/RNScreensPackage.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$1.class
new file mode 100644
index 0000000..8e955b4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$2.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$2.class
new file mode 100644
index 0000000..42412cd
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$ActivityState.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$ActivityState.class
new file mode 100644
index 0000000..5615cfe
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$ActivityState.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$ReplaceAnimation.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$ReplaceAnimation.class
new file mode 100644
index 0000000..9006182
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$ReplaceAnimation.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$StackAnimation.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$StackAnimation.class
new file mode 100644
index 0000000..23fa6ea
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$StackAnimation.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$StackPresentation.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$StackPresentation.class
new file mode 100644
index 0000000..2c6371a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$StackPresentation.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$WindowTraits.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$WindowTraits.class
new file mode 100644
index 0000000..e9a8d99
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen$WindowTraits.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen.class
new file mode 100644
index 0000000..294b0a9
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/Screen.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenAppearEvent.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenAppearEvent.class
new file mode 100644
index 0000000..d85e0bf
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenAppearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$1.class
new file mode 100644
index 0000000..aa51480
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$2.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$2.class
new file mode 100644
index 0000000..31a5ccb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$3.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$3.class
new file mode 100644
index 0000000..05db9db
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer$3.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer.class
new file mode 100644
index 0000000..761faf4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainer.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainerViewManager.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainerViewManager.class
new file mode 100644
index 0000000..7784ce7
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenContainerViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenDisappearEvent.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenDisappearEvent.class
new file mode 100644
index 0000000..5c36533
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenDisappearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenDismissedEvent.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenDismissedEvent.class
new file mode 100644
index 0000000..d6cdebe
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenDismissedEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment$1.class
new file mode 100644
index 0000000..17e19d5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment$2.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment$2.class
new file mode 100644
index 0000000..6aaf623
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment.class
new file mode 100644
index 0000000..b0c4667
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenFragment.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$1.class
new file mode 100644
index 0000000..f1ab875
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$2.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$2.class
new file mode 100644
index 0000000..e8b3943
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$3.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$3.class
new file mode 100644
index 0000000..299002a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$3.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$4.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$4.class
new file mode 100644
index 0000000..54e76bd
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack$4.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack.class
new file mode 100644
index 0000000..46bf336
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStack.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$1.class
new file mode 100644
index 0000000..57e578b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.class
new file mode 100644
index 0000000..88626cb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class
new file mode 100644
index 0000000..3bb9e38
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment.class
new file mode 100644
index 0000000..061d5ba
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackFragment.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.class
new file mode 100644
index 0000000..5d4ff57
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.class
new file mode 100644
index 0000000..391d00b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class
new file mode 100644
index 0000000..42c2724
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig.class
new file mode 100644
index 0000000..35b4aca
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfig.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class
new file mode 100644
index 0000000..760b72e
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class
new file mode 100644
index 0000000..c2ab426
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubview.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubview.class
new file mode 100644
index 0000000..2314c3d
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubview.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class
new file mode 100644
index 0000000..e8de1b2
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackViewManager.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackViewManager.class
new file mode 100644
index 0000000..d2be4c1
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenStackViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenViewManager.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenViewManager.class
new file mode 100644
index 0000000..3ab0d61
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWillAppearEvent.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWillAppearEvent.class
new file mode 100644
index 0000000..cbdfc42
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWillAppearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWillDisappearEvent.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWillDisappearEvent.class
new file mode 100644
index 0000000..155284b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWillDisappearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$1$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$1$1.class
new file mode 100644
index 0000000..6f5d932
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$1$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$1.class
new file mode 100644
index 0000000..042a4c4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$2.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$2.class
new file mode 100644
index 0000000..e8dff73
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$3$1.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$3$1.class
new file mode 100644
index 0000000..46d3406
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$3$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$3.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$3.class
new file mode 100644
index 0000000..5ed442c
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$3.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$4.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$4.class
new file mode 100644
index 0000000..2c16fb5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$4.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$5.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$5.class
new file mode 100644
index 0000000..ab80d2a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits$5.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits.class
new file mode 100644
index 0000000..f9e01f3
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/ScreenWindowTraits.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/StackFinishTransitioningEvent.class b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/StackFinishTransitioningEvent.class
new file mode 100644
index 0000000..ab0c944
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/javac/debug/classes/com/swmansion/rnscreens/StackFinishTransitioningEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-screens/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..f2bff6a
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,6 @@
+R_DEF: Internal format may change without notice
+local
+anim rns_slide_in_from_left
+anim rns_slide_in_from_right
+anim rns_slide_out_to_left
+anim rns_slide_out_to_right
diff --git a/node_modules/react-native-screens/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-screens/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..496a8b8
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,8 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.swmansion.rnscreens" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+5-->/Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+6
+7</manifest>
diff --git a/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..d64ed47
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.swmansion.rnscreens" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-screens/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml
new file mode 100644
index 0000000..939110f
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_left.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="-100%"
+    android:toXDelta="0%" />
diff --git a/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml
new file mode 100644
index 0000000..428eb9b
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_in_from_right.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="100%"
+    android:toXDelta="0%" />
diff --git a/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml
new file mode 100644
index 0000000..400a202
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_left.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="0%"
+    android:toXDelta="-100%"/>
diff --git a/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml
new file mode 100644
index 0000000..a00332b
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/anim/rns_slide_out_to_right.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<translate xmlns:android="http://schemas.android.com/apk/res/android"
+    android:duration="@android:integer/config_mediumAnimTime"
+    android:fromXDelta="0%"
+    android:toXDelta="100%"/>
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/BuildConfig.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/BuildConfig.class
new file mode 100644
index 0000000..c0848e0
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/BuildConfig.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/LifecycleHelper$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/LifecycleHelper$1.class
new file mode 100644
index 0000000..3e41def
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/LifecycleHelper$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/LifecycleHelper.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/LifecycleHelper.class
new file mode 100644
index 0000000..225c634
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/LifecycleHelper.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/RNScreensPackage.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/RNScreensPackage.class
new file mode 100644
index 0000000..949de5d
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/RNScreensPackage.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$1.class
new file mode 100644
index 0000000..8e955b4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$2.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$2.class
new file mode 100644
index 0000000..42412cd
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$ActivityState.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$ActivityState.class
new file mode 100644
index 0000000..5615cfe
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$ActivityState.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$ReplaceAnimation.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$ReplaceAnimation.class
new file mode 100644
index 0000000..9006182
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$ReplaceAnimation.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$StackAnimation.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$StackAnimation.class
new file mode 100644
index 0000000..23fa6ea
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$StackAnimation.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$StackPresentation.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$StackPresentation.class
new file mode 100644
index 0000000..2c6371a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$StackPresentation.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$WindowTraits.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$WindowTraits.class
new file mode 100644
index 0000000..e9a8d99
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen$WindowTraits.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen.class
new file mode 100644
index 0000000..294b0a9
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/Screen.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenAppearEvent.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenAppearEvent.class
new file mode 100644
index 0000000..d85e0bf
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenAppearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$1.class
new file mode 100644
index 0000000..aa51480
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$2.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$2.class
new file mode 100644
index 0000000..31a5ccb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$3.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$3.class
new file mode 100644
index 0000000..05db9db
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer$3.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer.class
new file mode 100644
index 0000000..761faf4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainer.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainerViewManager.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainerViewManager.class
new file mode 100644
index 0000000..7784ce7
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenContainerViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenDisappearEvent.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenDisappearEvent.class
new file mode 100644
index 0000000..5c36533
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenDisappearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenDismissedEvent.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenDismissedEvent.class
new file mode 100644
index 0000000..d6cdebe
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenDismissedEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment$1.class
new file mode 100644
index 0000000..17e19d5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment$2.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment$2.class
new file mode 100644
index 0000000..6aaf623
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment.class
new file mode 100644
index 0000000..b0c4667
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenFragment.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$1.class
new file mode 100644
index 0000000..f1ab875
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$2.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$2.class
new file mode 100644
index 0000000..e8b3943
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$3.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$3.class
new file mode 100644
index 0000000..299002a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$3.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$4.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$4.class
new file mode 100644
index 0000000..54e76bd
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack$4.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack.class
new file mode 100644
index 0000000..46bf336
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStack.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$1.class
new file mode 100644
index 0000000..57e578b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.class
new file mode 100644
index 0000000..88626cb
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class
new file mode 100644
index 0000000..3bb9e38
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment$NotifyingCoordinatorLayout.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment.class
new file mode 100644
index 0000000..061d5ba
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackFragment.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.class
new file mode 100644
index 0000000..5d4ff57
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.class
new file mode 100644
index 0000000..391d00b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class
new file mode 100644
index 0000000..42c2724
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig$DebugMenuToolbar.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig.class
new file mode 100644
index 0000000..35b4aca
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfig.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class
new file mode 100644
index 0000000..760b72e
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class
new file mode 100644
index 0000000..c2ab426
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview$Type.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview.class
new file mode 100644
index 0000000..2314c3d
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubview.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class
new file mode 100644
index 0000000..e8de1b2
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackViewManager.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackViewManager.class
new file mode 100644
index 0000000..d2be4c1
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenStackViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenViewManager.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenViewManager.class
new file mode 100644
index 0000000..3ab0d61
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenViewManager.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWillAppearEvent.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWillAppearEvent.class
new file mode 100644
index 0000000..cbdfc42
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWillAppearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWillDisappearEvent.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWillDisappearEvent.class
new file mode 100644
index 0000000..155284b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWillDisappearEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$1$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$1$1.class
new file mode 100644
index 0000000..6f5d932
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$1$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$1.class
new file mode 100644
index 0000000..042a4c4
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$2.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$2.class
new file mode 100644
index 0000000..e8dff73
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$2.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$3$1.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$3$1.class
new file mode 100644
index 0000000..46d3406
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$3$1.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$3.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$3.class
new file mode 100644
index 0000000..5ed442c
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$3.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$4.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$4.class
new file mode 100644
index 0000000..2c16fb5
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$4.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$5.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$5.class
new file mode 100644
index 0000000..ab80d2a
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits$5.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits.class
new file mode 100644
index 0000000..f9e01f3
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/ScreenWindowTraits.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/StackFinishTransitioningEvent.class b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/StackFinishTransitioningEvent.class
new file mode 100644
index 0000000..ab0c944
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_dir/debug/com/swmansion/rnscreens/StackFinishTransitioningEvent.class differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..f83e47b
Binary files /dev/null and b/node_modules/react-native-screens/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-screens/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-screens/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..6ea77a3
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,2764 @@
+com.swmansion.rnscreens
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim btn_checkbox_to_checked_box_inner_merged_animation
+anim btn_checkbox_to_checked_box_outer_merged_animation
+anim btn_checkbox_to_checked_icon_null_animation
+anim btn_checkbox_to_unchecked_box_inner_merged_animation
+anim btn_checkbox_to_unchecked_check_path_merged_animation
+anim btn_checkbox_to_unchecked_icon_null_animation
+anim btn_radio_to_off_mtrl_dot_group_animation
+anim btn_radio_to_off_mtrl_ring_outer_animation
+anim btn_radio_to_off_mtrl_ring_outer_path_animation
+anim btn_radio_to_on_mtrl_dot_group_animation
+anim btn_radio_to_on_mtrl_ring_outer_animation
+anim btn_radio_to_on_mtrl_ring_outer_path_animation
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+anim design_bottom_sheet_slide_in
+anim design_bottom_sheet_slide_out
+anim design_snackbar_in
+anim design_snackbar_out
+anim fragment_fast_out_extra_slow_in
+anim mtrl_bottom_sheet_slide_in
+anim mtrl_bottom_sheet_slide_out
+anim mtrl_card_lowers_interpolator
+anim rns_slide_in_from_left
+anim rns_slide_in_from_right
+anim rns_slide_out_to_left
+anim rns_slide_out_to_right
+animator design_appbar_state_list_animator
+animator design_fab_hide_motion_spec
+animator design_fab_show_motion_spec
+animator fragment_close_enter
+animator fragment_close_exit
+animator fragment_fade_enter
+animator fragment_fade_exit
+animator fragment_open_enter
+animator fragment_open_exit
+animator mtrl_btn_state_list_anim
+animator mtrl_btn_unelevated_state_list_anim
+animator mtrl_card_state_list_anim
+animator mtrl_chip_state_list_anim
+animator mtrl_extended_fab_change_size_motion_spec
+animator mtrl_extended_fab_hide_motion_spec
+animator mtrl_extended_fab_show_motion_spec
+animator mtrl_extended_fab_state_list_animator
+animator mtrl_fab_hide_motion_spec
+animator mtrl_fab_show_motion_spec
+animator mtrl_fab_transformation_sheet_collapse_spec
+animator mtrl_fab_transformation_sheet_expand_spec
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseContentDescription
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeTheme
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionTextColorAlpha
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr animationMode
+attr appBarLayoutStyle
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr autofillInlineSuggestionChip
+attr autofillInlineSuggestionEndIconStyle
+attr autofillInlineSuggestionStartIconStyle
+attr autofillInlineSuggestionSubtitle
+attr autofillInlineSuggestionTitle
+attr background
+attr backgroundColor
+attr backgroundImage
+attr backgroundInsetBottom
+attr backgroundInsetEnd
+attr backgroundInsetStart
+attr backgroundInsetTop
+attr backgroundOverlayColorAlpha
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr badgeGravity
+attr badgeStyle
+attr badgeTextColor
+attr barLength
+attr behavior_autoHide
+attr behavior_autoShrink
+attr behavior_expandedOffset
+attr behavior_fitToContents
+attr behavior_halfExpandedRatio
+attr behavior_hideable
+attr behavior_overlapTop
+attr behavior_peekHeight
+attr behavior_saveFlags
+attr behavior_skipCollapsed
+attr borderWidth
+attr borderlessButtonStyle
+attr bottomAppBarStyle
+attr bottomNavigationStyle
+attr bottomSheetDialogTheme
+attr bottomSheetStyle
+attr boxBackgroundColor
+attr boxBackgroundMode
+attr boxCollapsedPaddingTop
+attr boxCornerRadiusBottomEnd
+attr boxCornerRadiusBottomStart
+attr boxCornerRadiusTopEnd
+attr boxCornerRadiusTopStart
+attr boxStrokeColor
+attr boxStrokeWidth
+attr boxStrokeWidthFocused
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonCompat
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr cardBackgroundColor
+attr cardCornerRadius
+attr cardElevation
+attr cardForegroundColor
+attr cardMaxElevation
+attr cardPreventCornerOverlap
+attr cardUseCompatPadding
+attr cardViewStyle
+attr checkMarkCompat
+attr checkMarkTint
+attr checkMarkTintMode
+attr checkboxStyle
+attr checkedButton
+attr checkedChip
+attr checkedIcon
+attr checkedIconEnabled
+attr checkedIconTint
+attr checkedIconVisible
+attr checkedTextViewStyle
+attr chipBackgroundColor
+attr chipCornerRadius
+attr chipEndPadding
+attr chipGroupStyle
+attr chipIcon
+attr chipIconEnabled
+attr chipIconSize
+attr chipIconTint
+attr chipIconVisible
+attr chipMinHeight
+attr chipMinTouchTargetSize
+attr chipSpacing
+attr chipSpacingHorizontal
+attr chipSpacingVertical
+attr chipStandaloneStyle
+attr chipStartPadding
+attr chipStrokeColor
+attr chipStrokeWidth
+attr chipStyle
+attr chipSurfaceColor
+attr closeIcon
+attr closeIconEnabled
+attr closeIconEndPadding
+attr closeIconSize
+attr closeIconStartPadding
+attr closeIconTint
+attr closeIconVisible
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr collapsedTitleGravity
+attr collapsedTitleTextAppearance
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorOnBackground
+attr colorOnError
+attr colorOnPrimary
+attr colorOnPrimarySurface
+attr colorOnSecondary
+attr colorOnSurface
+attr colorPrimary
+attr colorPrimaryDark
+attr colorPrimarySurface
+attr colorPrimaryVariant
+attr colorSecondary
+attr colorSecondaryVariant
+attr colorSurface
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr contentPadding
+attr contentPaddingBottom
+attr contentPaddingLeft
+attr contentPaddingRight
+attr contentPaddingTop
+attr contentScrim
+attr controlBackground
+attr coordinatorLayoutStyle
+attr cornerFamily
+attr cornerFamilyBottomLeft
+attr cornerFamilyBottomRight
+attr cornerFamilyTopLeft
+attr cornerFamilyTopRight
+attr cornerRadius
+attr cornerSize
+attr cornerSizeBottomLeft
+attr cornerSizeBottomRight
+attr cornerSizeTopLeft
+attr cornerSizeTopRight
+attr counterEnabled
+attr counterMaxLength
+attr counterOverflowTextAppearance
+attr counterOverflowTextColor
+attr counterTextAppearance
+attr counterTextColor
+attr customNavigationLayout
+attr dayInvalidStyle
+attr daySelectedStyle
+attr dayStyle
+attr dayTodayStyle
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableBottomCompat
+attr drawableEndCompat
+attr drawableLeftCompat
+attr drawableRightCompat
+attr drawableSize
+attr drawableStartCompat
+attr drawableTint
+attr drawableTintMode
+attr drawableTopCompat
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr elevationOverlayColor
+attr elevationOverlayEnabled
+attr emojiCompatEnabled
+attr endIconCheckable
+attr endIconContentDescription
+attr endIconDrawable
+attr endIconMode
+attr endIconTint
+attr endIconTintMode
+attr enforceMaterialTheme
+attr enforceTextAppearance
+attr ensureMinTouchTargetSize
+attr errorEnabled
+attr errorIconDrawable
+attr errorIconTint
+attr errorIconTintMode
+attr errorTextAppearance
+attr errorTextColor
+attr expandActivityOverflowButtonDrawable
+attr expanded
+attr expandedTitleGravity
+attr expandedTitleMargin
+attr expandedTitleMarginBottom
+attr expandedTitleMarginEnd
+attr expandedTitleMarginStart
+attr expandedTitleMarginTop
+attr expandedTitleTextAppearance
+attr extendMotionSpec
+attr extendedFloatingActionButtonStyle
+attr fabAlignmentMode
+attr fabAnimationMode
+attr fabCradleMargin
+attr fabCradleRoundedCornerRadius
+attr fabCradleVerticalOffset
+attr fabCustomSize
+attr fabSize
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr fastScrollEnabled
+attr fastScrollHorizontalThumbDrawable
+attr fastScrollHorizontalTrackDrawable
+attr fastScrollVerticalThumbDrawable
+attr fastScrollVerticalTrackDrawable
+attr firstBaselineToTopHeight
+attr floatingActionButtonStyle
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontProviderSystemFontFamily
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr foregroundInsidePadding
+attr gapBetweenBars
+attr goIcon
+attr headerLayout
+attr height
+attr helperText
+attr helperTextEnabled
+attr helperTextTextAppearance
+attr helperTextTextColor
+attr hideMotionSpec
+attr hideOnContentScroll
+attr hideOnScroll
+attr hintAnimationEnabled
+attr hintEnabled
+attr hintTextAppearance
+attr hintTextColor
+attr homeAsUpIndicator
+attr homeLayout
+attr hoveredFocusedTranslationZ
+attr icon
+attr iconEndPadding
+attr iconGravity
+attr iconPadding
+attr iconSize
+attr iconStartPadding
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr insetForeground
+attr isAutofillInlineSuggestionTheme
+attr isLightTheme
+attr isMaterialTheme
+attr itemBackground
+attr itemFillColor
+attr itemHorizontalPadding
+attr itemHorizontalTranslationEnabled
+attr itemIconPadding
+attr itemIconSize
+attr itemIconTint
+attr itemMaxLines
+attr itemPadding
+attr itemRippleColor
+attr itemShapeAppearance
+attr itemShapeAppearanceOverlay
+attr itemShapeFillColor
+attr itemShapeInsetBottom
+attr itemShapeInsetEnd
+attr itemShapeInsetStart
+attr itemShapeInsetTop
+attr itemSpacing
+attr itemStrokeColor
+attr itemStrokeWidth
+attr itemTextAppearance
+attr itemTextAppearanceActive
+attr itemTextAppearanceInactive
+attr itemTextColor
+attr keylines
+attr lStar
+attr labelVisibilityMode
+attr lastBaselineToBottomHeight
+attr layout
+attr layoutManager
+attr layout_anchor
+attr layout_anchorGravity
+attr layout_behavior
+attr layout_collapseMode
+attr layout_collapseParallaxMultiplier
+attr layout_dodgeInsetEdges
+attr layout_insetEdge
+attr layout_keyline
+attr layout_scrollFlags
+attr layout_scrollInterpolator
+attr liftOnScroll
+attr liftOnScrollTargetViewId
+attr lineHeight
+attr lineSpacing
+attr listChoiceBackgroundIndicator
+attr listChoiceIndicatorMultipleAnimated
+attr listChoiceIndicatorSingleAnimated
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingEnd
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr listPreferredItemPaddingStart
+attr logo
+attr logoDescription
+attr materialAlertDialogBodyTextStyle
+attr materialAlertDialogTheme
+attr materialAlertDialogTitleIconStyle
+attr materialAlertDialogTitlePanelStyle
+attr materialAlertDialogTitleTextStyle
+attr materialButtonOutlinedStyle
+attr materialButtonStyle
+attr materialButtonToggleGroupStyle
+attr materialCalendarDay
+attr materialCalendarFullscreenTheme
+attr materialCalendarHeaderConfirmButton
+attr materialCalendarHeaderDivider
+attr materialCalendarHeaderLayout
+attr materialCalendarHeaderSelection
+attr materialCalendarHeaderTitle
+attr materialCalendarHeaderToggleButton
+attr materialCalendarStyle
+attr materialCalendarTheme
+attr materialCardViewStyle
+attr materialThemeOverlay
+attr maxActionInlineWidth
+attr maxButtonHeight
+attr maxCharacterCount
+attr maxImageSize
+attr measureWithLargestChild
+attr menu
+attr minTouchTargetSize
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr navigationViewStyle
+attr nestedScrollViewStyle
+attr number
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr passwordToggleContentDescription
+attr passwordToggleDrawable
+attr passwordToggleEnabled
+attr passwordToggleTint
+attr passwordToggleTintMode
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuBackground
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr pressedTranslationZ
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr queryPatterns
+attr radioButtonStyle
+attr rangeFillColor
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr recyclerViewStyle
+attr retryImage
+attr retryImageScaleType
+attr reverseLayout
+attr rippleColor
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr scrimAnimationDuration
+attr scrimBackground
+attr scrimVisibleHeightTrigger
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr shapeAppearance
+attr shapeAppearanceLargeComponent
+attr shapeAppearanceMediumComponent
+attr shapeAppearanceOverlay
+attr shapeAppearanceSmallComponent
+attr shortcutMatchRequired
+attr showAsAction
+attr showDividers
+attr showMotionSpec
+attr showText
+attr showTitle
+attr shrinkMotionSpec
+attr singleChoiceItemLayout
+attr singleLine
+attr singleSelection
+attr snackbarButtonStyle
+attr snackbarStyle
+attr spanCount
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr stackFromEnd
+attr startIconCheckable
+attr startIconContentDescription
+attr startIconDrawable
+attr startIconTint
+attr startIconTintMode
+attr state_above_anchor
+attr state_collapsed
+attr state_collapsible
+attr state_dragged
+attr state_liftable
+attr state_lifted
+attr statusBarBackground
+attr statusBarForeground
+attr statusBarScrim
+attr strokeColor
+attr strokeWidth
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr tabBackground
+attr tabContentStart
+attr tabGravity
+attr tabIconTint
+attr tabIconTintMode
+attr tabIndicator
+attr tabIndicatorAnimationDuration
+attr tabIndicatorColor
+attr tabIndicatorFullWidth
+attr tabIndicatorGravity
+attr tabIndicatorHeight
+attr tabInlineLabel
+attr tabMaxWidth
+attr tabMinWidth
+attr tabMode
+attr tabPadding
+attr tabPaddingBottom
+attr tabPaddingEnd
+attr tabPaddingStart
+attr tabPaddingTop
+attr tabRippleColor
+attr tabSelectedTextColor
+attr tabStyle
+attr tabTextAppearance
+attr tabTextColor
+attr tabUnboundedRipple
+attr textAllCaps
+attr textAppearanceBody1
+attr textAppearanceBody2
+attr textAppearanceButton
+attr textAppearanceCaption
+attr textAppearanceHeadline1
+attr textAppearanceHeadline2
+attr textAppearanceHeadline3
+attr textAppearanceHeadline4
+attr textAppearanceHeadline5
+attr textAppearanceHeadline6
+attr textAppearanceLargePopupMenu
+attr textAppearanceLineHeightEnabled
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearanceOverline
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textAppearanceSubtitle1
+attr textAppearanceSubtitle2
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr textEndPadding
+attr textInputStyle
+attr textLocale
+attr textStartPadding
+attr theme
+attr themeLineHeight
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleEnabled
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarId
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr useCompatPadding
+attr useMaterialThemeColors
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+attr yearSelectedStyle
+attr yearStyle
+attr yearTodayStyle
+bool abc_action_bar_embed_tabs
+bool abc_allow_stacked_button_bar
+bool abc_config_actionMenuItemAllCaps
+bool mtrl_btn_textappearance_all_caps
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_decor_view_status_guard
+color abc_decor_view_status_guard_light
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_input_method_navigation_guard
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color androidx_core_ripple_material_light
+color androidx_core_secondary_text_default_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color cardview_dark_background
+color cardview_light_background
+color cardview_shadow_end_color
+color cardview_shadow_start_color
+color catalyst_logbox_background
+color catalyst_redbox_background
+color checkbox_themeable_attribute_color
+color design_bottom_navigation_shadow_color
+color design_box_stroke_color
+color design_dark_default_color_background
+color design_dark_default_color_error
+color design_dark_default_color_on_background
+color design_dark_default_color_on_error
+color design_dark_default_color_on_primary
+color design_dark_default_color_on_secondary
+color design_dark_default_color_on_surface
+color design_dark_default_color_primary
+color design_dark_default_color_primary_dark
+color design_dark_default_color_primary_variant
+color design_dark_default_color_secondary
+color design_dark_default_color_secondary_variant
+color design_dark_default_color_surface
+color design_default_color_background
+color design_default_color_error
+color design_default_color_on_background
+color design_default_color_on_error
+color design_default_color_on_primary
+color design_default_color_on_secondary
+color design_default_color_on_surface
+color design_default_color_primary
+color design_default_color_primary_dark
+color design_default_color_primary_variant
+color design_default_color_secondary
+color design_default_color_secondary_variant
+color design_default_color_surface
+color design_error
+color design_fab_shadow_end_color
+color design_fab_shadow_mid_color
+color design_fab_shadow_start_color
+color design_fab_stroke_end_inner_color
+color design_fab_stroke_end_outer_color
+color design_fab_stroke_top_inner_color
+color design_fab_stroke_top_outer_color
+color design_icon_tint
+color design_snackbar_background_color
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color material_on_background_disabled
+color material_on_background_emphasis_high_type
+color material_on_background_emphasis_medium
+color material_on_primary_disabled
+color material_on_primary_emphasis_high_type
+color material_on_primary_emphasis_medium
+color material_on_surface_disabled
+color material_on_surface_emphasis_high_type
+color material_on_surface_emphasis_medium
+color mtrl_bottom_nav_colored_item_tint
+color mtrl_bottom_nav_colored_ripple_color
+color mtrl_bottom_nav_item_tint
+color mtrl_bottom_nav_ripple_color
+color mtrl_btn_bg_color_selector
+color mtrl_btn_ripple_color
+color mtrl_btn_stroke_color_selector
+color mtrl_btn_text_btn_bg_color_selector
+color mtrl_btn_text_btn_ripple_color
+color mtrl_btn_text_color_disabled
+color mtrl_btn_text_color_selector
+color mtrl_btn_transparent_bg_color
+color mtrl_calendar_item_stroke_color
+color mtrl_calendar_selected_range
+color mtrl_card_view_foreground
+color mtrl_card_view_ripple
+color mtrl_chip_background_color
+color mtrl_chip_close_icon_tint
+color mtrl_chip_ripple_color
+color mtrl_chip_surface_color
+color mtrl_chip_text_color
+color mtrl_choice_chip_background_color
+color mtrl_choice_chip_ripple_color
+color mtrl_choice_chip_text_color
+color mtrl_error
+color mtrl_extended_fab_bg_color_selector
+color mtrl_extended_fab_ripple_color
+color mtrl_extended_fab_text_color_selector
+color mtrl_fab_ripple_color
+color mtrl_filled_background_color
+color mtrl_filled_icon_tint
+color mtrl_filled_stroke_color
+color mtrl_indicator_text_color
+color mtrl_navigation_item_background_color
+color mtrl_navigation_item_icon_tint
+color mtrl_navigation_item_text_color
+color mtrl_on_primary_text_btn_text_color_selector
+color mtrl_outlined_icon_tint
+color mtrl_outlined_stroke_color
+color mtrl_popupmenu_overlay_color
+color mtrl_scrim_color
+color mtrl_tabs_colored_ripple_color
+color mtrl_tabs_icon_color_selector
+color mtrl_tabs_icon_color_selector_colored
+color mtrl_tabs_legacy_text_color_selector
+color mtrl_tabs_ripple_color
+color mtrl_text_btn_text_color_selector
+color mtrl_textinput_default_box_stroke_color
+color mtrl_textinput_disabled_color
+color mtrl_textinput_filled_box_default_background_color
+color mtrl_textinput_focused_box_stroke_color
+color mtrl_textinput_hovered_box_stroke_color
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color test_mtrl_calendar_day
+color test_mtrl_calendar_day_selected
+color tooltip_background_dark
+color tooltip_background_light
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_height_large_material
+dimen abc_list_item_height_material
+dimen abc_list_item_height_small_material
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_star_big
+dimen abc_star_medium
+dimen abc_star_small
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen action_bar_size
+dimen appcompat_dialog_background_inset
+dimen autofill_inline_suggestion_icon_size
+dimen cardview_compat_inset_shadow
+dimen cardview_default_elevation
+dimen cardview_default_radius
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen default_dimension
+dimen design_appbar_elevation
+dimen design_bottom_navigation_active_item_max_width
+dimen design_bottom_navigation_active_item_min_width
+dimen design_bottom_navigation_active_text_size
+dimen design_bottom_navigation_elevation
+dimen design_bottom_navigation_height
+dimen design_bottom_navigation_icon_size
+dimen design_bottom_navigation_item_max_width
+dimen design_bottom_navigation_item_min_width
+dimen design_bottom_navigation_margin
+dimen design_bottom_navigation_shadow_height
+dimen design_bottom_navigation_text_size
+dimen design_bottom_sheet_elevation
+dimen design_bottom_sheet_modal_elevation
+dimen design_bottom_sheet_peek_height_min
+dimen design_fab_border_width
+dimen design_fab_elevation
+dimen design_fab_image_size
+dimen design_fab_size_mini
+dimen design_fab_size_normal
+dimen design_fab_translation_z_hovered_focused
+dimen design_fab_translation_z_pressed
+dimen design_navigation_elevation
+dimen design_navigation_icon_padding
+dimen design_navigation_icon_size
+dimen design_navigation_item_horizontal_padding
+dimen design_navigation_item_icon_padding
+dimen design_navigation_max_width
+dimen design_navigation_padding_bottom
+dimen design_navigation_separator_vertical_padding
+dimen design_snackbar_action_inline_max_width
+dimen design_snackbar_action_text_color_alpha
+dimen design_snackbar_background_corner_radius
+dimen design_snackbar_elevation
+dimen design_snackbar_extra_spacing_horizontal
+dimen design_snackbar_max_width
+dimen design_snackbar_min_width
+dimen design_snackbar_padding_horizontal
+dimen design_snackbar_padding_vertical
+dimen design_snackbar_padding_vertical_2lines
+dimen design_snackbar_text_size
+dimen design_tab_max_width
+dimen design_tab_scrollable_min_width
+dimen design_tab_text_size
+dimen design_tab_text_size_2line
+dimen design_textinput_caption_translate_y
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen fastscroll_default_thickness
+dimen fastscroll_margin
+dimen fastscroll_minimum_range
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen item_touch_helper_max_drag_scroll_per_frame
+dimen item_touch_helper_swipe_escape_max_velocity
+dimen item_touch_helper_swipe_escape_velocity
+dimen material_emphasis_disabled
+dimen material_emphasis_high_type
+dimen material_emphasis_medium
+dimen material_text_view_test_line_height
+dimen material_text_view_test_line_height_override
+dimen mtrl_alert_dialog_background_inset_bottom
+dimen mtrl_alert_dialog_background_inset_end
+dimen mtrl_alert_dialog_background_inset_start
+dimen mtrl_alert_dialog_background_inset_top
+dimen mtrl_alert_dialog_picker_background_inset
+dimen mtrl_badge_horizontal_edge_offset
+dimen mtrl_badge_long_text_horizontal_padding
+dimen mtrl_badge_radius
+dimen mtrl_badge_text_horizontal_edge_offset
+dimen mtrl_badge_text_size
+dimen mtrl_badge_with_text_radius
+dimen mtrl_bottomappbar_fabOffsetEndMode
+dimen mtrl_bottomappbar_fab_bottom_margin
+dimen mtrl_bottomappbar_fab_cradle_margin
+dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius
+dimen mtrl_bottomappbar_fab_cradle_vertical_offset
+dimen mtrl_bottomappbar_height
+dimen mtrl_btn_corner_radius
+dimen mtrl_btn_dialog_btn_min_width
+dimen mtrl_btn_disabled_elevation
+dimen mtrl_btn_disabled_z
+dimen mtrl_btn_elevation
+dimen mtrl_btn_focused_z
+dimen mtrl_btn_hovered_z
+dimen mtrl_btn_icon_btn_padding_left
+dimen mtrl_btn_icon_padding
+dimen mtrl_btn_inset
+dimen mtrl_btn_letter_spacing
+dimen mtrl_btn_padding_bottom
+dimen mtrl_btn_padding_left
+dimen mtrl_btn_padding_right
+dimen mtrl_btn_padding_top
+dimen mtrl_btn_pressed_z
+dimen mtrl_btn_stroke_size
+dimen mtrl_btn_text_btn_icon_padding
+dimen mtrl_btn_text_btn_padding_left
+dimen mtrl_btn_text_btn_padding_right
+dimen mtrl_btn_text_size
+dimen mtrl_btn_z
+dimen mtrl_calendar_action_height
+dimen mtrl_calendar_action_padding
+dimen mtrl_calendar_bottom_padding
+dimen mtrl_calendar_content_padding
+dimen mtrl_calendar_day_corner
+dimen mtrl_calendar_day_height
+dimen mtrl_calendar_day_horizontal_padding
+dimen mtrl_calendar_day_today_stroke
+dimen mtrl_calendar_day_vertical_padding
+dimen mtrl_calendar_day_width
+dimen mtrl_calendar_days_of_week_height
+dimen mtrl_calendar_dialog_background_inset
+dimen mtrl_calendar_header_content_padding
+dimen mtrl_calendar_header_content_padding_fullscreen
+dimen mtrl_calendar_header_divider_thickness
+dimen mtrl_calendar_header_height
+dimen mtrl_calendar_header_height_fullscreen
+dimen mtrl_calendar_header_selection_line_height
+dimen mtrl_calendar_header_text_padding
+dimen mtrl_calendar_header_toggle_margin_bottom
+dimen mtrl_calendar_header_toggle_margin_top
+dimen mtrl_calendar_landscape_header_width
+dimen mtrl_calendar_maximum_default_fullscreen_minor_axis
+dimen mtrl_calendar_month_horizontal_padding
+dimen mtrl_calendar_month_vertical_padding
+dimen mtrl_calendar_navigation_bottom_padding
+dimen mtrl_calendar_navigation_height
+dimen mtrl_calendar_navigation_top_padding
+dimen mtrl_calendar_pre_l_text_clip_padding
+dimen mtrl_calendar_selection_baseline_to_top_fullscreen
+dimen mtrl_calendar_selection_text_baseline_to_bottom
+dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen
+dimen mtrl_calendar_selection_text_baseline_to_top
+dimen mtrl_calendar_text_input_padding_top
+dimen mtrl_calendar_title_baseline_to_top
+dimen mtrl_calendar_title_baseline_to_top_fullscreen
+dimen mtrl_calendar_year_corner
+dimen mtrl_calendar_year_height
+dimen mtrl_calendar_year_horizontal_padding
+dimen mtrl_calendar_year_vertical_padding
+dimen mtrl_calendar_year_width
+dimen mtrl_card_checked_icon_margin
+dimen mtrl_card_checked_icon_size
+dimen mtrl_card_corner_radius
+dimen mtrl_card_dragged_z
+dimen mtrl_card_elevation
+dimen mtrl_card_spacing
+dimen mtrl_chip_pressed_translation_z
+dimen mtrl_chip_text_size
+dimen mtrl_exposed_dropdown_menu_popup_elevation
+dimen mtrl_exposed_dropdown_menu_popup_vertical_offset
+dimen mtrl_exposed_dropdown_menu_popup_vertical_padding
+dimen mtrl_extended_fab_bottom_padding
+dimen mtrl_extended_fab_corner_radius
+dimen mtrl_extended_fab_disabled_elevation
+dimen mtrl_extended_fab_disabled_translation_z
+dimen mtrl_extended_fab_elevation
+dimen mtrl_extended_fab_end_padding
+dimen mtrl_extended_fab_end_padding_icon
+dimen mtrl_extended_fab_icon_size
+dimen mtrl_extended_fab_icon_text_spacing
+dimen mtrl_extended_fab_min_height
+dimen mtrl_extended_fab_min_width
+dimen mtrl_extended_fab_start_padding
+dimen mtrl_extended_fab_start_padding_icon
+dimen mtrl_extended_fab_top_padding
+dimen mtrl_extended_fab_translation_z_base
+dimen mtrl_extended_fab_translation_z_hovered_focused
+dimen mtrl_extended_fab_translation_z_pressed
+dimen mtrl_fab_elevation
+dimen mtrl_fab_min_touch_target
+dimen mtrl_fab_translation_z_hovered_focused
+dimen mtrl_fab_translation_z_pressed
+dimen mtrl_high_ripple_default_alpha
+dimen mtrl_high_ripple_focused_alpha
+dimen mtrl_high_ripple_hovered_alpha
+dimen mtrl_high_ripple_pressed_alpha
+dimen mtrl_large_touch_target
+dimen mtrl_low_ripple_default_alpha
+dimen mtrl_low_ripple_focused_alpha
+dimen mtrl_low_ripple_hovered_alpha
+dimen mtrl_low_ripple_pressed_alpha
+dimen mtrl_min_touch_target_size
+dimen mtrl_navigation_elevation
+dimen mtrl_navigation_item_horizontal_padding
+dimen mtrl_navigation_item_icon_padding
+dimen mtrl_navigation_item_icon_size
+dimen mtrl_navigation_item_shape_horizontal_margin
+dimen mtrl_navigation_item_shape_vertical_margin
+dimen mtrl_shape_corner_size_large_component
+dimen mtrl_shape_corner_size_medium_component
+dimen mtrl_shape_corner_size_small_component
+dimen mtrl_snackbar_action_text_color_alpha
+dimen mtrl_snackbar_background_corner_radius
+dimen mtrl_snackbar_background_overlay_color_alpha
+dimen mtrl_snackbar_margin
+dimen mtrl_switch_thumb_elevation
+dimen mtrl_textinput_box_corner_radius_medium
+dimen mtrl_textinput_box_corner_radius_small
+dimen mtrl_textinput_box_label_cutout_padding
+dimen mtrl_textinput_box_stroke_width_default
+dimen mtrl_textinput_box_stroke_width_focused
+dimen mtrl_textinput_end_icon_margin_start
+dimen mtrl_textinput_outline_box_expanded_padding
+dimen mtrl_textinput_start_icon_margin_end
+dimen mtrl_toolbar_default_height
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen test_mtrl_calendar_day_cornerSize
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_material_anim
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_material_anim
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_star_black_16dp
+drawable abc_ic_star_black_36dp
+drawable abc_ic_star_black_48dp
+drawable abc_ic_star_half_black_16dp
+drawable abc_ic_star_half_black_36dp
+drawable abc_ic_star_half_black_48dp
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_star_black_48dp
+drawable abc_star_half_black_48dp
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl
+drawable abc_text_select_handle_left_mtrl_dark
+drawable abc_text_select_handle_left_mtrl_light
+drawable abc_text_select_handle_middle_mtrl
+drawable abc_text_select_handle_middle_mtrl_dark
+drawable abc_text_select_handle_middle_mtrl_light
+drawable abc_text_select_handle_right_mtrl
+drawable abc_text_select_handle_right_mtrl_dark
+drawable abc_text_select_handle_right_mtrl_light
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable autofill_inline_suggestion_chip_background
+drawable avd_hide_password
+drawable avd_show_password
+drawable btn_checkbox_checked_mtrl
+drawable btn_checkbox_checked_to_unchecked_mtrl_animation
+drawable btn_checkbox_unchecked_mtrl
+drawable btn_checkbox_unchecked_to_checked_mtrl_animation
+drawable btn_radio_off_mtrl
+drawable btn_radio_off_to_on_mtrl_animation
+drawable btn_radio_on_mtrl
+drawable btn_radio_on_to_off_mtrl_animation
+drawable design_bottom_navigation_item_background
+drawable design_fab_background
+drawable design_ic_visibility
+drawable design_ic_visibility_off
+drawable design_password_eye
+drawable design_snackbar_background
+drawable ic_calendar_black_24dp
+drawable ic_clear_black_24dp
+drawable ic_edit_black_24dp
+drawable ic_keyboard_arrow_left_black_24dp
+drawable ic_keyboard_arrow_right_black_24dp
+drawable ic_menu_arrow_down_black_24dp
+drawable ic_menu_arrow_up_black_24dp
+drawable ic_mtrl_checked_circle
+drawable ic_mtrl_chip_checked_black
+drawable ic_mtrl_chip_checked_circle
+drawable ic_mtrl_chip_close_circle
+drawable mtrl_dialog_background
+drawable mtrl_dropdown_arrow
+drawable mtrl_ic_arrow_drop_down
+drawable mtrl_ic_arrow_drop_up
+drawable mtrl_ic_cancel
+drawable mtrl_ic_error
+drawable mtrl_popupmenu_background
+drawable mtrl_popupmenu_background_dark
+drawable mtrl_tabs_default_indicator
+drawable navigation_empty_icon
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable test_custom_background
+drawable test_level_drawable
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id BOTTOM_END
+id BOTTOM_START
+id TOP_END
+id TOP_START
+id accessibility_action_clickable_span
+id accessibility_actions
+id accessibility_collection
+id accessibility_collection_item
+id accessibility_custom_action_0
+id accessibility_custom_action_1
+id accessibility_custom_action_10
+id accessibility_custom_action_11
+id accessibility_custom_action_12
+id accessibility_custom_action_13
+id accessibility_custom_action_14
+id accessibility_custom_action_15
+id accessibility_custom_action_16
+id accessibility_custom_action_17
+id accessibility_custom_action_18
+id accessibility_custom_action_19
+id accessibility_custom_action_2
+id accessibility_custom_action_20
+id accessibility_custom_action_21
+id accessibility_custom_action_22
+id accessibility_custom_action_23
+id accessibility_custom_action_24
+id accessibility_custom_action_25
+id accessibility_custom_action_26
+id accessibility_custom_action_27
+id accessibility_custom_action_28
+id accessibility_custom_action_29
+id accessibility_custom_action_3
+id accessibility_custom_action_30
+id accessibility_custom_action_31
+id accessibility_custom_action_4
+id accessibility_custom_action_5
+id accessibility_custom_action_6
+id accessibility_custom_action_7
+id accessibility_custom_action_8
+id accessibility_custom_action_9
+id accessibility_hint
+id accessibility_label
+id accessibility_links
+id accessibility_role
+id accessibility_state
+id accessibility_state_expanded
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id alertTitle
+id async
+id auto
+id autofill_inline_suggestion_end_icon
+id autofill_inline_suggestion_start_icon
+id autofill_inline_suggestion_subtitle
+id autofill_inline_suggestion_title
+id blocking
+id bottom
+id buttonPanel
+id cancel_button
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id checked
+id chip
+id chip_group
+id chronometer
+id clear_text
+id confirm_button
+id container
+id content
+id contentPanel
+id coordinator
+id custom
+id customPanel
+id cut
+id date_picker_actions
+id decor_content_parent
+id default_activity_button
+id design_bottom_sheet
+id design_menu_item_action_area
+id design_menu_item_action_area_stub
+id design_menu_item_text
+id design_navigation_view
+id dialog_button
+id dropdown_menu
+id edit_query
+id end
+id expand_activities_button
+id expanded_menu
+id fade
+id fill
+id filled
+id filter_chip
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id fixed
+id focusCrop
+id forever
+id fps_text
+id fragment_container_view_tag
+id ghost_view
+id ghost_view_holder
+id group_divider
+id home
+id icon
+id icon_group
+id image
+id info
+id italic
+id item1
+id item2
+id item3
+id item4
+id item_touch_helper_previous_elevation
+id labeled
+id labelled_by
+id largeLabel
+id left
+id line1
+id line3
+id listMode
+id list_item
+id masked
+id message
+id mini
+id month_grid
+id month_navigation_bar
+id month_navigation_fragment_toggle
+id month_navigation_next
+id month_navigation_previous
+id month_title
+id mtrl_calendar_day_selector_frame
+id mtrl_calendar_days_of_week
+id mtrl_calendar_frame
+id mtrl_calendar_main_pane
+id mtrl_calendar_months
+id mtrl_calendar_selection_frame
+id mtrl_calendar_text_input_frame
+id mtrl_calendar_year_selector_frame
+id mtrl_card_checked_layer_id
+id mtrl_child_content_container
+id mtrl_internal_children_alpha_tag
+id mtrl_picker_fullscreen
+id mtrl_picker_header
+id mtrl_picker_header_selection_text
+id mtrl_picker_header_title_and_selection
+id mtrl_picker_header_toggle
+id mtrl_picker_text_input_date
+id mtrl_picker_text_input_range_end
+id mtrl_picker_text_input_range_start
+id mtrl_picker_title_text
+id multiply
+id navigation_header_container
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id off
+id on
+id outline
+id parallax
+id parentPanel
+id parent_matrix
+id password_toggle
+id pin
+id pointer_events
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id rounded
+id save_non_transition_alpha
+id save_overlay_view
+id scale
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id scrollable
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id selected
+id shortcut
+id slide
+id smallLabel
+id snackbar_action
+id snackbar_text
+id spacer
+id special_effects_controller_view_tag
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id start
+id stretch
+id submenuarrow
+id submit_area
+id tabMode
+id tag_accessibility_actions
+id tag_accessibility_clickable_spans
+id tag_accessibility_heading
+id tag_accessibility_pane_title
+id tag_on_apply_window_listener
+id tag_on_receive_content_listener
+id tag_on_receive_content_mime_types
+id tag_screen_reader_focusable
+id tag_state_description
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id tag_window_insets_animation_callback
+id test_checkbox_android_button_tint
+id test_checkbox_app_button_tint
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id text_input_end_icon
+id text_input_start_icon
+id textinput_counter
+id textinput_error
+id textinput_helper_text
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id top
+id topPanel
+id touch_outside
+id transition_current_scene
+id transition_layout_save
+id transition_position
+id transition_scene_layoutid_cache
+id transition_transform
+id unchecked
+id uniform
+id unlabeled
+id up
+id view_offset_helper
+id view_tag_instance_handle
+id view_tag_native_id
+id view_tree_lifecycle_owner
+id view_tree_saved_state_registry_owner
+id view_tree_view_model_store_owner
+id visible
+id visible_removing_fragment_view_tag
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer app_bar_elevation_anim_duration
+integer bottom_sheet_slide_duration
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer design_snackbar_text_max_lines
+integer design_tab_indicator_anim_duration_ms
+integer hide_password_duration
+integer mtrl_badge_max_character_count
+integer mtrl_btn_anim_delay_ms
+integer mtrl_btn_anim_duration_ms
+integer mtrl_calendar_header_orientation
+integer mtrl_calendar_selection_text_lines
+integer mtrl_calendar_year_selector_span
+integer mtrl_card_anim_delay_ms
+integer mtrl_card_anim_duration_ms
+integer mtrl_chip_anim_duration
+integer mtrl_tab_indicator_anim_duration_ms
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer show_password_duration
+integer status_bar_notification_info_maxnum
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_1
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1
+interpolator btn_radio_to_off_mtrl_animation_interpolator_0
+interpolator btn_radio_to_on_mtrl_animation_interpolator_0
+interpolator fast_out_slow_in
+interpolator mtrl_fast_out_linear_in
+interpolator mtrl_fast_out_slow_in
+interpolator mtrl_linear
+interpolator mtrl_linear_out_slow_in
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout autofill_inline_suggestion
+layout custom_dialog
+layout design_bottom_navigation_item
+layout design_bottom_sheet_dialog
+layout design_layout_snackbar
+layout design_layout_snackbar_include
+layout design_layout_tab_icon
+layout design_layout_tab_text
+layout design_menu_item_action_area
+layout design_navigation_item
+layout design_navigation_item_header
+layout design_navigation_item_separator
+layout design_navigation_item_subheader
+layout design_navigation_menu
+layout design_navigation_menu_item
+layout design_text_input_end_icon
+layout design_text_input_start_icon
+layout dev_loading_view
+layout fps_view
+layout mtrl_alert_dialog
+layout mtrl_alert_dialog_actions
+layout mtrl_alert_dialog_title
+layout mtrl_alert_select_dialog_item
+layout mtrl_alert_select_dialog_multichoice
+layout mtrl_alert_select_dialog_singlechoice
+layout mtrl_calendar_day
+layout mtrl_calendar_day_of_week
+layout mtrl_calendar_days_of_week
+layout mtrl_calendar_horizontal
+layout mtrl_calendar_month
+layout mtrl_calendar_month_labeled
+layout mtrl_calendar_month_navigation
+layout mtrl_calendar_months
+layout mtrl_calendar_vertical
+layout mtrl_calendar_year
+layout mtrl_layout_snackbar
+layout mtrl_layout_snackbar_include
+layout mtrl_picker_actions
+layout mtrl_picker_dialog
+layout mtrl_picker_fullscreen
+layout mtrl_picker_header_dialog
+layout mtrl_picker_header_fullscreen
+layout mtrl_picker_header_selection_text
+layout mtrl_picker_header_title_text
+layout mtrl_picker_header_toggle
+layout mtrl_picker_text_input_date
+layout mtrl_picker_text_input_date_range
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+layout test_action_chip
+layout test_design_checkbox
+layout test_reflow_chipgroup
+layout test_toolbar
+layout test_toolbar_custom_background
+layout test_toolbar_elevation
+layout test_toolbar_surface
+layout text_view_with_line_height_from_appearance
+layout text_view_with_line_height_from_layout
+layout text_view_with_line_height_from_style
+layout text_view_with_theme_line_height
+layout text_view_without_line_height
+menu example_menu
+menu example_menu2
+plurals mtrl_badge_content_description
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string appbar_scrolling_view_behavior
+string bottom_sheet_behavior
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_inspector_stop
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string character_counter_content_description
+string character_counter_overflowed_content_description
+string character_counter_pattern
+string chip_text
+string clear_text_end_icon_content_description
+string combobox_description
+string error_icon_content_description
+string exposed_dropdown_menu_content_description
+string fab_transformation_scrim_behavior
+string fab_transformation_sheet_behavior
+string header_description
+string hide_bottom_view_on_scroll_behavior
+string icon_content_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string mtrl_badge_numberless_content_description
+string mtrl_chip_close_icon_content_description
+string mtrl_exceed_max_badge_number_suffix
+string mtrl_picker_a11y_next_month
+string mtrl_picker_a11y_prev_month
+string mtrl_picker_announce_current_selection
+string mtrl_picker_cancel
+string mtrl_picker_confirm
+string mtrl_picker_date_header_selected
+string mtrl_picker_date_header_title
+string mtrl_picker_date_header_unselected
+string mtrl_picker_day_of_week_column_header
+string mtrl_picker_invalid_format
+string mtrl_picker_invalid_format_example
+string mtrl_picker_invalid_format_use
+string mtrl_picker_invalid_range
+string mtrl_picker_navigate_to_year_description
+string mtrl_picker_out_of_range
+string mtrl_picker_range_header_only_end_selected
+string mtrl_picker_range_header_only_start_selected
+string mtrl_picker_range_header_selected
+string mtrl_picker_range_header_title
+string mtrl_picker_range_header_unselected
+string mtrl_picker_save
+string mtrl_picker_text_input_date_hint
+string mtrl_picker_text_input_date_range_end_hint
+string mtrl_picker_text_input_date_range_start_hint
+string mtrl_picker_text_input_day_abbr
+string mtrl_picker_text_input_month_abbr
+string mtrl_picker_text_input_year_abbr
+string mtrl_picker_toggle_to_calendar_input_mode
+string mtrl_picker_toggle_to_day_selection
+string mtrl_picker_toggle_to_text_input_mode
+string mtrl_picker_toggle_to_year_selection
+string password_toggle_content_description
+string path_password_eye
+string path_password_eye_mask_strike_through
+string path_password_eye_mask_visible
+string path_password_strike_through
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string state_unselected_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Animation_Design_BottomSheetDialog
+style Animation_MaterialComponents_BottomSheetDialog
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_CardView
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_MaterialAlertDialog_MaterialComponents_Title_Icon
+style Base_MaterialAlertDialog_MaterialComponents_Title_Panel
+style Base_MaterialAlertDialog_MaterialComponents_Title_Text
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_MaterialComponents_Badge
+style Base_TextAppearance_MaterialComponents_Button
+style Base_TextAppearance_MaterialComponents_Headline6
+style Base_TextAppearance_MaterialComponents_Subtitle2
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_ThemeOverlay_MaterialComponents_Dialog
+style Base_ThemeOverlay_MaterialComponents_Dialog_Alert
+style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_Theme_MaterialComponents
+style Base_Theme_MaterialComponents_Bridge
+style Base_Theme_MaterialComponents_CompactMenu
+style Base_Theme_MaterialComponents_Dialog
+style Base_Theme_MaterialComponents_DialogWhenLarge
+style Base_Theme_MaterialComponents_Dialog_Alert
+style Base_Theme_MaterialComponents_Dialog_Bridge
+style Base_Theme_MaterialComponents_Dialog_FixedSize
+style Base_Theme_MaterialComponents_Dialog_MinWidth
+style Base_Theme_MaterialComponents_Light
+style Base_Theme_MaterialComponents_Light_Bridge
+style Base_Theme_MaterialComponents_Light_DarkActionBar
+style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge
+style Base_Theme_MaterialComponents_Light_Dialog
+style Base_Theme_MaterialComponents_Light_DialogWhenLarge
+style Base_Theme_MaterialComponents_Light_Dialog_Alert
+style Base_Theme_MaterialComponents_Light_Dialog_Bridge
+style Base_Theme_MaterialComponents_Light_Dialog_FixedSize
+style Base_Theme_MaterialComponents_Light_Dialog_MinWidth
+style Base_V14_ThemeOverlay_MaterialComponents_Dialog
+style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert
+style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog
+style Base_V14_Theme_MaterialComponents
+style Base_V14_Theme_MaterialComponents_Bridge
+style Base_V14_Theme_MaterialComponents_Dialog
+style Base_V14_Theme_MaterialComponents_Dialog_Bridge
+style Base_V14_Theme_MaterialComponents_Light
+style Base_V14_Theme_MaterialComponents_Light_Bridge
+style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge
+style Base_V14_Theme_MaterialComponents_Light_Dialog
+style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style Base_Widget_Design_TabLayout
+style Base_Widget_MaterialComponents_AutoCompleteTextView
+style Base_Widget_MaterialComponents_CheckedTextView
+style Base_Widget_MaterialComponents_Chip
+style Base_Widget_MaterialComponents_PopupMenu
+style Base_Widget_MaterialComponents_PopupMenu_ContextMenu
+style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow
+style Base_Widget_MaterialComponents_PopupMenu_Overflow
+style Base_Widget_MaterialComponents_TextInputEditText
+style Base_Widget_MaterialComponents_TextInputLayout
+style Base_Widget_MaterialComponents_TextView
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style CardView
+style CardView_Dark
+style CardView_Light
+style DialogAnimationFade
+style DialogAnimationSlide
+style EmptyTheme
+style MaterialAlertDialog_MaterialComponents
+style MaterialAlertDialog_MaterialComponents_Body_Text
+style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar
+style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner
+style MaterialAlertDialog_MaterialComponents_Title_Icon
+style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked
+style MaterialAlertDialog_MaterialComponents_Title_Panel
+style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked
+style MaterialAlertDialog_MaterialComponents_Title_Text
+style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_MaterialComponents
+style Platform_MaterialComponents_Dialog
+style Platform_MaterialComponents_Light
+style Platform_MaterialComponents_Light_Dialog
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style ShapeAppearanceOverlay
+style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize
+style ShapeAppearanceOverlay_BottomRightCut
+style ShapeAppearanceOverlay_Cut
+style ShapeAppearanceOverlay_DifferentCornerSize
+style ShapeAppearanceOverlay_MaterialComponents_BottomSheet
+style ShapeAppearanceOverlay_MaterialComponents_Chip
+style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton
+style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton
+style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day
+style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen
+style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year
+style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox
+style ShapeAppearanceOverlay_TopLeftCut
+style ShapeAppearanceOverlay_TopRightDifferentCornerSize
+style ShapeAppearance_MaterialComponents
+style ShapeAppearance_MaterialComponents_LargeComponent
+style ShapeAppearance_MaterialComponents_MediumComponent
+style ShapeAppearance_MaterialComponents_SmallComponent
+style ShapeAppearance_MaterialComponents_Test
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TestStyleWithLineHeight
+style TestStyleWithLineHeightAppearance
+style TestStyleWithThemeLineHeightAttribute
+style TestStyleWithoutLineHeight
+style TestThemeWithLineHeight
+style TestThemeWithLineHeightDisabled
+style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day
+style Test_Theme_MaterialComponents_MaterialCalendar
+style Test_Widget_MaterialComponents_MaterialCalendar
+style Test_Widget_MaterialComponents_MaterialCalendar_Day
+style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Design_CollapsingToolbar_Expanded
+style TextAppearance_Design_Counter
+style TextAppearance_Design_Counter_Overflow
+style TextAppearance_Design_Error
+style TextAppearance_Design_HelperText
+style TextAppearance_Design_Hint
+style TextAppearance_Design_Snackbar_Message
+style TextAppearance_Design_Tab
+style TextAppearance_MaterialComponents_Badge
+style TextAppearance_MaterialComponents_Body1
+style TextAppearance_MaterialComponents_Body2
+style TextAppearance_MaterialComponents_Button
+style TextAppearance_MaterialComponents_Caption
+style TextAppearance_MaterialComponents_Chip
+style TextAppearance_MaterialComponents_Headline1
+style TextAppearance_MaterialComponents_Headline2
+style TextAppearance_MaterialComponents_Headline3
+style TextAppearance_MaterialComponents_Headline4
+style TextAppearance_MaterialComponents_Headline5
+style TextAppearance_MaterialComponents_Headline6
+style TextAppearance_MaterialComponents_Overline
+style TextAppearance_MaterialComponents_Subtitle1
+style TextAppearance_MaterialComponents_Subtitle2
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_DayNight
+style ThemeOverlay_AppCompat_DayNight_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style ThemeOverlay_Design_TextInputEditText
+style ThemeOverlay_MaterialComponents
+style ThemeOverlay_MaterialComponents_ActionBar
+style ThemeOverlay_MaterialComponents_ActionBar_Primary
+style ThemeOverlay_MaterialComponents_ActionBar_Surface
+style ThemeOverlay_MaterialComponents_AutoCompleteTextView
+style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox
+style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense
+style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox
+style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense
+style ThemeOverlay_MaterialComponents_BottomAppBar_Primary
+style ThemeOverlay_MaterialComponents_BottomAppBar_Surface
+style ThemeOverlay_MaterialComponents_BottomSheetDialog
+style ThemeOverlay_MaterialComponents_Dark
+style ThemeOverlay_MaterialComponents_Dark_ActionBar
+style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog
+style ThemeOverlay_MaterialComponents_Dialog
+style ThemeOverlay_MaterialComponents_Dialog_Alert
+style ThemeOverlay_MaterialComponents_Light
+style ThemeOverlay_MaterialComponents_Light_BottomSheetDialog
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day
+style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner
+style ThemeOverlay_MaterialComponents_MaterialCalendar
+style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen
+style ThemeOverlay_MaterialComponents_TextInputEditText
+style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox
+style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense
+style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox
+style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense
+style ThemeOverlay_MaterialComponents_Toolbar_Primary
+style ThemeOverlay_MaterialComponents_Toolbar_Surface
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Empty
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_AutofillInlineSuggestion
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_Design
+style Theme_Design_BottomSheetDialog
+style Theme_Design_Light
+style Theme_Design_Light_BottomSheetDialog
+style Theme_Design_Light_NoActionBar
+style Theme_Design_NoActionBar
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_MaterialComponents
+style Theme_MaterialComponents_BottomSheetDialog
+style Theme_MaterialComponents_Bridge
+style Theme_MaterialComponents_CompactMenu
+style Theme_MaterialComponents_DayNight
+style Theme_MaterialComponents_DayNight_BottomSheetDialog
+style Theme_MaterialComponents_DayNight_Bridge
+style Theme_MaterialComponents_DayNight_DarkActionBar
+style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge
+style Theme_MaterialComponents_DayNight_Dialog
+style Theme_MaterialComponents_DayNight_DialogWhenLarge
+style Theme_MaterialComponents_DayNight_Dialog_Alert
+style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge
+style Theme_MaterialComponents_DayNight_Dialog_Bridge
+style Theme_MaterialComponents_DayNight_Dialog_FixedSize
+style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge
+style Theme_MaterialComponents_DayNight_Dialog_MinWidth
+style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge
+style Theme_MaterialComponents_DayNight_NoActionBar
+style Theme_MaterialComponents_DayNight_NoActionBar_Bridge
+style Theme_MaterialComponents_Dialog
+style Theme_MaterialComponents_DialogWhenLarge
+style Theme_MaterialComponents_Dialog_Alert
+style Theme_MaterialComponents_Dialog_Alert_Bridge
+style Theme_MaterialComponents_Dialog_Bridge
+style Theme_MaterialComponents_Dialog_FixedSize
+style Theme_MaterialComponents_Dialog_FixedSize_Bridge
+style Theme_MaterialComponents_Dialog_MinWidth
+style Theme_MaterialComponents_Dialog_MinWidth_Bridge
+style Theme_MaterialComponents_Light
+style Theme_MaterialComponents_Light_BarSize
+style Theme_MaterialComponents_Light_BottomSheetDialog
+style Theme_MaterialComponents_Light_Bridge
+style Theme_MaterialComponents_Light_DarkActionBar
+style Theme_MaterialComponents_Light_DarkActionBar_Bridge
+style Theme_MaterialComponents_Light_Dialog
+style Theme_MaterialComponents_Light_DialogWhenLarge
+style Theme_MaterialComponents_Light_Dialog_Alert
+style Theme_MaterialComponents_Light_Dialog_Alert_Bridge
+style Theme_MaterialComponents_Light_Dialog_Bridge
+style Theme_MaterialComponents_Light_Dialog_FixedSize
+style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge
+style Theme_MaterialComponents_Light_Dialog_MinWidth
+style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge
+style Theme_MaterialComponents_Light_LargeTouch
+style Theme_MaterialComponents_Light_NoActionBar
+style Theme_MaterialComponents_Light_NoActionBar_Bridge
+style Theme_MaterialComponents_NoActionBar
+style Theme_MaterialComponents_NoActionBar_Bridge
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Autofill
+style Widget_Autofill_InlineSuggestionChip
+style Widget_Autofill_InlineSuggestionEndIconStyle
+style Widget_Autofill_InlineSuggestionStartIconStyle
+style Widget_Autofill_InlineSuggestionSubtitle
+style Widget_Autofill_InlineSuggestionTitle
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style Widget_Design_AppBarLayout
+style Widget_Design_BottomNavigationView
+style Widget_Design_BottomSheet_Modal
+style Widget_Design_CollapsingToolbar
+style Widget_Design_FloatingActionButton
+style Widget_Design_NavigationView
+style Widget_Design_ScrimInsetsFrameLayout
+style Widget_Design_Snackbar
+style Widget_Design_TabLayout
+style Widget_Design_TextInputLayout
+style Widget_MaterialComponents_ActionBar_Primary
+style Widget_MaterialComponents_ActionBar_PrimarySurface
+style Widget_MaterialComponents_ActionBar_Solid
+style Widget_MaterialComponents_ActionBar_Surface
+style Widget_MaterialComponents_AppBarLayout_Primary
+style Widget_MaterialComponents_AppBarLayout_PrimarySurface
+style Widget_MaterialComponents_AppBarLayout_Surface
+style Widget_MaterialComponents_AutoCompleteTextView_FilledBox
+style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense
+style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox
+style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense
+style Widget_MaterialComponents_Badge
+style Widget_MaterialComponents_BottomAppBar
+style Widget_MaterialComponents_BottomAppBar_Colored
+style Widget_MaterialComponents_BottomAppBar_PrimarySurface
+style Widget_MaterialComponents_BottomNavigationView
+style Widget_MaterialComponents_BottomNavigationView_Colored
+style Widget_MaterialComponents_BottomNavigationView_PrimarySurface
+style Widget_MaterialComponents_BottomSheet
+style Widget_MaterialComponents_BottomSheet_Modal
+style Widget_MaterialComponents_Button
+style Widget_MaterialComponents_Button_Icon
+style Widget_MaterialComponents_Button_OutlinedButton
+style Widget_MaterialComponents_Button_OutlinedButton_Icon
+style Widget_MaterialComponents_Button_TextButton
+style Widget_MaterialComponents_Button_TextButton_Dialog
+style Widget_MaterialComponents_Button_TextButton_Dialog_Flush
+style Widget_MaterialComponents_Button_TextButton_Dialog_Icon
+style Widget_MaterialComponents_Button_TextButton_Icon
+style Widget_MaterialComponents_Button_TextButton_Snackbar
+style Widget_MaterialComponents_Button_UnelevatedButton
+style Widget_MaterialComponents_Button_UnelevatedButton_Icon
+style Widget_MaterialComponents_CardView
+style Widget_MaterialComponents_CheckedTextView
+style Widget_MaterialComponents_ChipGroup
+style Widget_MaterialComponents_Chip_Action
+style Widget_MaterialComponents_Chip_Choice
+style Widget_MaterialComponents_Chip_Entry
+style Widget_MaterialComponents_Chip_Filter
+style Widget_MaterialComponents_CompoundButton_CheckBox
+style Widget_MaterialComponents_CompoundButton_RadioButton
+style Widget_MaterialComponents_CompoundButton_Switch
+style Widget_MaterialComponents_ExtendedFloatingActionButton
+style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon
+style Widget_MaterialComponents_FloatingActionButton
+style Widget_MaterialComponents_Light_ActionBar_Solid
+style Widget_MaterialComponents_MaterialButtonToggleGroup
+style Widget_MaterialComponents_MaterialCalendar
+style Widget_MaterialComponents_MaterialCalendar_Day
+style Widget_MaterialComponents_MaterialCalendar_DayTextView
+style Widget_MaterialComponents_MaterialCalendar_Day_Invalid
+style Widget_MaterialComponents_MaterialCalendar_Day_Selected
+style Widget_MaterialComponents_MaterialCalendar_Day_Today
+style Widget_MaterialComponents_MaterialCalendar_Fullscreen
+style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton
+style Widget_MaterialComponents_MaterialCalendar_HeaderDivider
+style Widget_MaterialComponents_MaterialCalendar_HeaderLayout
+style Widget_MaterialComponents_MaterialCalendar_HeaderSelection
+style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen
+style Widget_MaterialComponents_MaterialCalendar_HeaderTitle
+style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton
+style Widget_MaterialComponents_MaterialCalendar_Item
+style Widget_MaterialComponents_MaterialCalendar_Year
+style Widget_MaterialComponents_MaterialCalendar_Year_Selected
+style Widget_MaterialComponents_MaterialCalendar_Year_Today
+style Widget_MaterialComponents_NavigationView
+style Widget_MaterialComponents_PopupMenu
+style Widget_MaterialComponents_PopupMenu_ContextMenu
+style Widget_MaterialComponents_PopupMenu_ListPopupWindow
+style Widget_MaterialComponents_PopupMenu_Overflow
+style Widget_MaterialComponents_Snackbar
+style Widget_MaterialComponents_Snackbar_FullWidth
+style Widget_MaterialComponents_TabLayout
+style Widget_MaterialComponents_TabLayout_Colored
+style Widget_MaterialComponents_TabLayout_PrimarySurface
+style Widget_MaterialComponents_TextInputEditText_FilledBox
+style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense
+style Widget_MaterialComponents_TextInputEditText_OutlinedBox
+style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense
+style Widget_MaterialComponents_TextInputLayout_FilledBox
+style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense
+style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu
+style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu
+style Widget_MaterialComponents_TextInputLayout_OutlinedBox
+style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense
+style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu
+style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu
+style Widget_MaterialComponents_TextView
+style Widget_MaterialComponents_Toolbar
+style Widget_MaterialComponents_Toolbar_Primary
+style Widget_MaterialComponents_Toolbar_PrimarySurface
+style Widget_MaterialComponents_Toolbar_Surface
+style Widget_Support_CoordinatorLayout
+style redboxButton
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppBarLayout android_background android_keyboardNavigationCluster android_touchscreenBlocksFocus elevation expanded liftOnScroll liftOnScrollTargetViewId statusBarForeground
+styleable AppBarLayoutStates state_collapsed state_collapsible state_liftable state_lifted
+styleable AppBarLayout_Layout layout_scrollFlags layout_scrollInterpolator
+styleable AppCompatEmojiHelper
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType drawableBottomCompat drawableEndCompat drawableLeftCompat drawableRightCompat drawableStartCompat drawableTint drawableTintMode drawableTopCompat emojiCompatEnabled firstBaselineToTopHeight fontFamily fontVariationSettings lastBaselineToBottomHeight lineHeight textAllCaps textLocale
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseContentDescription actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeTheme actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listChoiceIndicatorMultipleAnimated listChoiceIndicatorSingleAnimated listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingEnd listPreferredItemPaddingLeft listPreferredItemPaddingRight listPreferredItemPaddingStart panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable Autofill_InlineSuggestion autofillInlineSuggestionChip autofillInlineSuggestionEndIconStyle autofillInlineSuggestionStartIconStyle autofillInlineSuggestionSubtitle autofillInlineSuggestionTitle isAutofillInlineSuggestionTheme
+styleable Badge backgroundColor badgeGravity badgeTextColor maxCharacterCount number
+styleable BottomAppBar backgroundTint elevation fabAlignmentMode fabAnimationMode fabCradleMargin fabCradleRoundedCornerRadius fabCradleVerticalOffset hideOnScroll
+styleable BottomNavigationView backgroundTint elevation itemBackground itemHorizontalTranslationEnabled itemIconSize itemIconTint itemRippleColor itemTextAppearanceActive itemTextAppearanceInactive itemTextColor labelVisibilityMode menu
+styleable BottomSheetBehavior_Layout android_elevation backgroundTint behavior_expandedOffset behavior_fitToContents behavior_halfExpandedRatio behavior_hideable behavior_peekHeight behavior_saveFlags behavior_skipCollapsed shapeAppearance shapeAppearanceOverlay
+styleable ButtonBarLayout allowStacking
+styleable Capability queryPatterns shortcutMatchRequired
+styleable CardView android_minHeight android_minWidth cardBackgroundColor cardCornerRadius cardElevation cardMaxElevation cardPreventCornerOverlap cardUseCompatPadding contentPadding contentPaddingBottom contentPaddingLeft contentPaddingRight contentPaddingTop
+styleable CheckedTextView android_checkMark checkMarkCompat checkMarkTint checkMarkTintMode
+styleable Chip android_checkable android_ellipsize android_maxWidth android_text android_textAppearance android_textColor checkedIcon checkedIconEnabled checkedIconVisible chipBackgroundColor chipCornerRadius chipEndPadding chipIcon chipIconEnabled chipIconSize chipIconTint chipIconVisible chipMinHeight chipMinTouchTargetSize chipStartPadding chipStrokeColor chipStrokeWidth chipSurfaceColor closeIcon closeIconEnabled closeIconEndPadding closeIconSize closeIconStartPadding closeIconTint closeIconVisible ensureMinTouchTargetSize hideMotionSpec iconEndPadding iconStartPadding rippleColor shapeAppearance shapeAppearanceOverlay showMotionSpec textEndPadding textStartPadding
+styleable ChipGroup checkedChip chipSpacing chipSpacingHorizontal chipSpacingVertical singleLine singleSelection
+styleable CollapsingToolbarLayout collapsedTitleGravity collapsedTitleTextAppearance contentScrim expandedTitleGravity expandedTitleMargin expandedTitleMarginBottom expandedTitleMarginEnd expandedTitleMarginStart expandedTitleMarginTop expandedTitleTextAppearance scrimAnimationDuration scrimVisibleHeightTrigger statusBarScrim title titleEnabled toolbarId
+styleable CollapsingToolbarLayout_Layout layout_collapseMode layout_collapseParallaxMultiplier
+styleable ColorStateListItem alpha android_alpha android_color android_lStar lStar
+styleable CompoundButton android_button buttonCompat buttonTint buttonTintMode
+styleable CoordinatorLayout keylines statusBarBackground
+styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable ExtendedFloatingActionButton elevation extendMotionSpec hideMotionSpec showMotionSpec shrinkMotionSpec
+styleable ExtendedFloatingActionButton_Behavior_Layout behavior_autoHide behavior_autoShrink
+styleable FloatingActionButton backgroundTint backgroundTintMode borderWidth elevation ensureMinTouchTargetSize fabCustomSize fabSize hideMotionSpec hoveredFocusedTranslationZ maxImageSize pressedTranslationZ rippleColor shapeAppearance shapeAppearanceOverlay showMotionSpec useCompatPadding
+styleable FloatingActionButton_Behavior_Layout behavior_autoHide
+styleable FlowLayout itemSpacing lineSpacing
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable ForegroundLinearLayout android_foreground android_foregroundGravity foregroundInsidePadding
+styleable Fragment android_id android_name android_tag
+styleable FragmentContainerView android_name android_tag
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable MaterialAlertDialog backgroundInsetBottom backgroundInsetEnd backgroundInsetStart backgroundInsetTop
+styleable MaterialAlertDialogTheme materialAlertDialogBodyTextStyle materialAlertDialogTheme materialAlertDialogTitleIconStyle materialAlertDialogTitlePanelStyle materialAlertDialogTitleTextStyle
+styleable MaterialButton android_checkable android_insetBottom android_insetLeft android_insetRight android_insetTop backgroundTint backgroundTintMode cornerRadius elevation icon iconGravity iconPadding iconSize iconTint iconTintMode rippleColor shapeAppearance shapeAppearanceOverlay strokeColor strokeWidth
+styleable MaterialButtonToggleGroup checkedButton singleSelection
+styleable MaterialCalendar android_windowFullscreen dayInvalidStyle daySelectedStyle dayStyle dayTodayStyle rangeFillColor yearSelectedStyle yearStyle yearTodayStyle
+styleable MaterialCalendarItem android_insetBottom android_insetLeft android_insetRight android_insetTop itemFillColor itemShapeAppearance itemShapeAppearanceOverlay itemStrokeColor itemStrokeWidth itemTextColor
+styleable MaterialCardView android_checkable cardForegroundColor checkedIcon checkedIconTint rippleColor shapeAppearance shapeAppearanceOverlay state_dragged strokeColor strokeWidth
+styleable MaterialCheckBox buttonTint useMaterialThemeColors
+styleable MaterialRadioButton useMaterialThemeColors
+styleable MaterialShape shapeAppearance shapeAppearanceOverlay
+styleable MaterialTextAppearance android_lineHeight lineHeight
+styleable MaterialTextView android_lineHeight android_textAppearance lineHeight
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable NavigationView android_background android_fitsSystemWindows android_maxWidth elevation headerLayout itemBackground itemHorizontalPadding itemIconPadding itemIconSize itemIconTint itemMaxLines itemShapeAppearance itemShapeAppearanceOverlay itemShapeFillColor itemShapeInsetBottom itemShapeInsetEnd itemShapeInsetStart itemShapeInsetTop itemTextAppearance itemTextColor menu
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable RecyclerView android_clipToPadding android_descendantFocusability android_orientation fastScrollEnabled fastScrollHorizontalThumbDrawable fastScrollHorizontalTrackDrawable fastScrollVerticalThumbDrawable fastScrollVerticalTrackDrawable layoutManager reverseLayout spanCount stackFromEnd
+styleable ScrimInsetsFrameLayout insetForeground
+styleable ScrollingViewBehavior_Layout behavior_overlapTop
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable ShapeAppearance cornerFamily cornerFamilyBottomLeft cornerFamilyBottomRight cornerFamilyTopLeft cornerFamilyTopRight cornerSize cornerSizeBottomLeft cornerSizeBottomRight cornerSizeTopLeft cornerSizeTopRight
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Snackbar snackbarButtonStyle snackbarStyle
+styleable SnackbarLayout actionTextColorAlpha android_maxWidth animationMode backgroundOverlayColorAlpha elevation maxActionInlineWidth
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable SwitchMaterial useMaterialThemeColors
+styleable TabItem android_icon android_layout android_text
+styleable TabLayout tabBackground tabContentStart tabGravity tabIconTint tabIconTintMode tabIndicator tabIndicatorAnimationDuration tabIndicatorColor tabIndicatorFullWidth tabIndicatorGravity tabIndicatorHeight tabInlineLabel tabMaxWidth tabMinWidth tabMode tabPadding tabPaddingBottom tabPaddingEnd tabPaddingStart tabPaddingTop tabRippleColor tabSelectedTextColor tabTextAppearance tabTextColor tabUnboundedRipple
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textFontWeight android_textSize android_textStyle android_typeface fontFamily fontVariationSettings textAllCaps textLocale
+styleable TextInputLayout android_hint android_textColorHint boxBackgroundColor boxBackgroundMode boxCollapsedPaddingTop boxCornerRadiusBottomEnd boxCornerRadiusBottomStart boxCornerRadiusTopEnd boxCornerRadiusTopStart boxStrokeColor boxStrokeWidth boxStrokeWidthFocused counterEnabled counterMaxLength counterOverflowTextAppearance counterOverflowTextColor counterTextAppearance counterTextColor endIconCheckable endIconContentDescription endIconDrawable endIconMode endIconTint endIconTintMode errorEnabled errorIconDrawable errorIconTint errorIconTintMode errorTextAppearance errorTextColor helperText helperTextEnabled helperTextTextAppearance helperTextTextColor hintAnimationEnabled hintEnabled hintTextAppearance hintTextColor passwordToggleContentDescription passwordToggleDrawable passwordToggleEnabled passwordToggleTint passwordToggleTintMode shapeAppearance shapeAppearanceOverlay startIconCheckable startIconContentDescription startIconDrawable startIconTint startIconTintMode
+styleable ThemeEnforcement android_textAppearance enforceMaterialTheme enforceTextAppearance
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight menu navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewPager2 android_orientation
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
+xml standalone_badge
+xml standalone_badge_gravity_bottom_end
+xml standalone_badge_gravity_bottom_start
+xml standalone_badge_gravity_top_start
diff --git a/node_modules/react-native-screens/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-screens/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..61fd3a4
--- /dev/null
+++ b/node_modules/react-native-screens/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:1-5:12
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:1-5:12
+	package
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:3:11-44
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml:2:11-69
+uses-sdk
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/react-native-screens/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-screens/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-screens/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..e16426c
Binary files /dev/null and b/node_modules/react-native-screens/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-screens/ios/RNSScreen.h b/node_modules/react-native-screens/ios/RNSScreen.h
index d5be39a..27dbdeb 100644
--- a/node_modules/react-native-screens/ios/RNSScreen.h
+++ b/node_modules/react-native-screens/ios/RNSScreen.h
@@ -101,7 +101,7 @@ typedef NS_ENUM(NSInteger, RNSWindowTrait) {
 #endif
 
 - (void)notifyFinishTransitioning;
-
+- (BOOL)isModal;
 @end
 
 @interface UIView (RNSScreen)
diff --git a/node_modules/react-native-screens/ios/RNSScreen.m b/node_modules/react-native-screens/ios/RNSScreen.m
index 6d6a05c..b6d6579 100644
--- a/node_modules/react-native-screens/ios/RNSScreen.m
+++ b/node_modules/react-native-screens/ios/RNSScreen.m
@@ -226,6 +226,10 @@ - (void)notifyFinishTransitioning
   [_controller notifyFinishTransitioning];
 }
 
+- (BOOL)isModal {
+    return self.stackPresentation == RNSScreenStackPresentationModal;
+}
+
 - (void)notifyDismissedWithCount:(int)dismissCount
 {
   _dismissed = YES;
