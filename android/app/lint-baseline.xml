<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.0.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.0.0)" variant="all" version="8.0.0">

    <issue
        id="DuplicatePlatformClasses"
        message="`commons-logging` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="DuplicatePlatformClasses"
        message="`httpclient` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`."
        errorLine1="    implementation &apos;org.apache.httpcomponents:httpmime:4.5.13&apos; // Latest version"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="224"
            column="21"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: `READ_MEDIA_IMAGES`, `READ_MEDIA_VIDEO` or `READ_MEDIA_AUDIO`."
        errorLine1="&lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot; />"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="9"
            column="32"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the `MediaStore.createWriteRequest` intent."
        errorLine1="&lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="11"
            column="32"/>
    </issue>

    <issue
        id="NewApi"
        message="`android:forceDarkAllowed` requires API level 29 (current min is 21)"
        errorLine1="        &lt;item name=&quot;android:forceDarkAllowed&quot;>false&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="4"
            column="15"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `autoVerify` is only used in API level 23 and higher (current min is 21)"
        errorLine1="        &lt;intent-filter android:autoVerify=&quot;true&quot;>"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="44"
            column="24"/>
    </issue>

    <issue
        id="IntentFilterUniqueDataAttributes"
        message="Consider splitting data tag into multiple tags with individual attributes to avoid confusion"
        errorLine1="          &lt;data android:host=&quot;auth8&quot; android:scheme=&quot;post&quot; />"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="48"
            column="11"/>
    </issue>

    <issue
        id="IntentFilterUniqueDataAttributes"
        message="Consider splitting data tag into multiple tags with individual attributes to avoid confusion"
        errorLine1="          &lt;data android:scheme=&quot;https&quot; android:host=&quot;dev.legiteem8.app&quot; />"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="49"
            column="11"/>
    </issue>

    <issue
        id="IntentFilterUniqueDataAttributes"
        message="Consider splitting data tag into multiple tags with individual attributes to avoid confusion"
        errorLine1="          &lt;data android:scheme=&quot;http&quot; android:host=&quot;dev.legiteem8.app&quot; />"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="50"
            column="11"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="        android:label=&quot;@string/app_name&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="        android:screenOrientation=&quot;portrait&quot;>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="39"
            column="9"/>
    </issue>

    <issue
        id="VisibleForTests"
        message="This method should only be accessed from tests or within private scope"
        errorLine1="      ReactContext reactContext = reactInstanceManager.getCurrentReactContext();"
        errorLine2="                                                       ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/debug/java/com/auth8/ReactNativeFlipper.java"
            line="51"
            column="56"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="$HOME/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/rn-fetch-blob/android/build/.transforms/59231d322448cba214b63984ad2da060/transformed/out/jars/classes.jar"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="$HOME/Documents/WorkingProjects/Manaknight/ReactNative/auth8/auth8_app/node_modules/rn-fetch-blob/android/build/.transforms/59231d322448cba214b63984ad2da060/transformed/out/jars/classes.jar"/>
    </issue>

    <issue
        id="DataExtractionRules"
        message="The attribute `android:allowBackup` is deprecated from Android 12 and higher and may be removed in future versions. Consider adding the attribute `android:dataExtractionRules` specifying an `@xml` resource which configures cloud backups and device transfers on Android 12 and higher."
        errorLine1="      android:allowBackup=&quot;false&quot;"
        errorLine2="                           ~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="20"
            column="28"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.bootsplash` appears to be unused"
        errorLine1="&lt;layer-list xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot; android:opacity=&quot;opaque&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/bootsplash.xml"
            line="3"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.integer.react_native_dev_server_port` appears to be unused">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.integer.react_native_inspector_proxy_port` appears to be unused">
        <location
            file="build.gradle"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ic_primary.png` in densityless folder">
        <location
            file="src/main/res/drawable/ic_primary.png"/>
    </issue>

</issues>
